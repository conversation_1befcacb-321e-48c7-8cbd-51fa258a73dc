Installation:

in a virtualenv

      - "pip install -r requirements.txt"
      - "for req in `ls ./lambda/*/*/requirements.txt`; do pip install -r $req; done"
      - "for req in `ls ./lambda/*/*/test_requirements.txt`; do pip install -r $req; done"


to lint:
./lint.sh

to run test:

./test.sh


Decided against stepfunction - mainly because we want more control over the batching and more control over the inputs at different stages. Different parts of the system need very different driving policies and SQS triggers seem to do that more easily than event bridge pipes.


If an import file errors, once the error is fixed you can delete the 'Errored' fileimport and it'll rerun if it's still circling the queue. If not you'll need to reupload the file into the s3 bucket to trigger the process again.



Remote tunnel: ssh <EMAIL> -T -R 8100:localaddr:8000



New client setup:

Copy an existing dashboard tf and rename things appropriately (tech debt to terraform module)
Copy a subscriber app tf and rename
Terrafom apply - certificate/bucket policy errors are expected and may take some time or a few apply attempts
Create dashboard cognito pool:
- use prod-<whatever>-dashboard-pool as the name
- traditional web application
- email sign in
- dashboard base url as callback
- create
- rename the pool
- authentication -> signup
-- don't allow cognito to send messages
-- disable self registration
- app client -> edit
-- allow user pathword auth
Create subscriber app cognito pool
- use prod-<whatever>-subscriber-pool as the name
- traditional web application
- email sign in
- sapp base url as callback
- create
- rename the pool
- authentication -> signup
-- don't allow cognito to send messages
-- DO allow self-registration
- app client -> edit
-- allow user pathword auth

Create client in nexus
Copy a dashboard definition in main.tf to create pipeline and deploy things - update the client id. Terraform apply.
Be careful with running local terraform apply and not pushing the changes as CI will undeploy them
Push a deployment to trigger everything else. Set up the plan offerings and dashboard user in nexus.
Terraform apply - I had to apply twice for bucket policy
Add to main.tf and terraform apply
Commit and trigger a deploy
Setup the client plan offerings
Set up stripe webhook and secret (/webhooks/stripe/<payment integration id>/), subscribe to
invoice.payment_succeeded
invoice.payment_failed
invoice.created
invoice.upcoming
invoice.paid
customer.subscription.deleted
customer.subscription.created
customer.subscription.paused
customer.subscription.resumed
customer.subscription.updated
checkout.session.completed
checkout.session.expired
Create transatel webhook