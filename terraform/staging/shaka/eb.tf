resource "aws_iam_instance_profile" "staging_eb_profile" {
  name = "staging-eb-profile"
  role = aws_iam_role.staging_eb_profile_role.name
}

data "aws_iam_policy_document" "staging_eb_profile_role_policy" {
  statement {
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }

    actions = ["sts:AssumeRole"]
  }
}

resource "aws_iam_role" "staging_eb_profile_role" {
  name               = "staging-eb-profile-role"
  path               = "/"
  assume_role_policy = data.aws_iam_policy_document.staging_eb_profile_role_policy.json
}

resource "aws_iam_role_policy_attachment" "staging_eb_profile_role_eb_attach" {
  role       = aws_iam_role.staging_eb_profile_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSElasticBeanstalkWebTier"
}

resource "aws_iam_role_policy_attachment" "staging_eb_profile_role_eb_attach_cognito" {
  role       = aws_iam_role.staging_eb_profile_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonCognitoPowerUser"
}

resource "aws_iam_role_policy_attachment" "staging_eb_profile_role_eb_attach_sns" {
  role       = aws_iam_role.staging_eb_profile_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSNSFullAccess"
}

resource "aws_iam_role_policy_attachment" "staging_eb_profile_role_eb_attach_dynamo" {
  role       = aws_iam_role.staging_eb_profile_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonDynamoDBFullAccess"
}

resource "aws_iam_role_policy_attachment" "staging_eb_profile_role_eb_attach_stepfunctions" {
  role       = aws_iam_role.staging_eb_profile_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSStepFunctionsFullAccess"
}
