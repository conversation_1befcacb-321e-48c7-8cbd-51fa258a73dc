/* Djagno data file bar */
data "aws_iam_policy_document" "prod_bar_data_for_dangerous_users_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

}

resource "aws_iam_policy" "prod_bar_data_for_dangerous_users_policy" {
  name        = "prod-bar-data-for-dangerous-users-policy"
  path        = "/"
  description = "IAM policy for the dangerous users"
  policy      = data.aws_iam_policy_document.prod_bar_data_for_dangerous_users_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_bar_data_for_dangerous_users_attach" {
  role       = aws_iam_role.prod_bar_data_for_dangerous_users_role.name
  policy_arn = aws_iam_policy.prod_bar_data_for_dangerous_users_policy.arn
}

resource "aws_iam_role" "prod_bar_data_for_dangerous_users_role" {
  name = "prod_bar_data_for_dangerous_users_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_bar_data_for_dangerous_users" {
  function_name                  = "prod-bar-data-for-dangerous-users"
  role                           = aws_iam_role.prod_bar_data_for_dangerous_users_role.arn
  handler                        = "lambda.bar_data_for_dangerous_users.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 180
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
    variables = {
      BAR_DATA_ENDPOINT  = "https://nexus.shaka.tel/backend-api/bar-data/"
      BAR_DATA_API_TOKEN = local.nexus_creds["LAMBDA_API_TOKEN"]
    }
  }
}

resource "aws_lambda_alias" "prod_bar_data_for_dangerous_users_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_bar_data_for_dangerous_users.arn
  function_version = "3"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }
}


resource "aws_iam_role" "prod_bar_data_for_dangerous_users_schedule_role" {
  name = "prod_bar_data_for_dangerous_users_schedule_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "scheduler.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

data "aws_iam_policy_document" "prod_bar_data_for_dangerous_users_schedule_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}

resource "aws_iam_policy" "prod_bar_data_for_dangerous_users_schedule_policy" {
  name        = "prod-bar-data-for-dangerous-users-schedule-policy"
  path        = "/"
  description = "IAM policy for the data file bar"
  policy      = data.aws_iam_policy_document.prod_bar_data_for_dangerous_users_schedule_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_bar_data_for_dangerous_users_scedule_attach" {
  role       = aws_iam_role.prod_bar_data_for_dangerous_users_schedule_role.name
  policy_arn = aws_iam_policy.prod_bar_data_for_dangerous_users_schedule_policy.arn
}


resource "aws_scheduler_schedule" "prod_bar_data_for_dangerous_users_schedule" {
  name       = "prod-bar-data-for-dangerous-users-schedule"
  group_name = "default"
  flexible_time_window {
    mode = "OFF"
  }
  description         = "Fires every hour to bar DATA for"
  schedule_expression = "rate(5 minutes)"
  target {
    arn      = aws_lambda_function.prod_bar_data_for_dangerous_users.arn
    role_arn = aws_iam_role.prod_bar_data_for_dangerous_users_schedule_role.arn
    retry_policy {
      maximum_retry_attempts = 0
    }
  }
}
