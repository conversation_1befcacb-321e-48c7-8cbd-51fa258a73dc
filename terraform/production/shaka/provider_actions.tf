/* Provider action run */

data "aws_iam_policy_document" "prod_run_provider_actions_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

}

resource "aws_iam_policy" "prod_run_provider_actions_policy" {
  name        = "prod-run-provider-actions-policy"
  path        = "/"
  description = "IAM policy for the provider file run"
  policy      = data.aws_iam_policy_document.prod_run_provider_actions_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_run_provider_actions_attach" {
  role       = aws_iam_role.prod_run_provider_actions_role.name
  policy_arn = aws_iam_policy.prod_run_provider_actions_policy.arn
}

resource "aws_iam_role" "prod_run_provider_actions_role" {
  name = "prod_run_provider_actions_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_run_provider_actions" {
  function_name                  = "prod-run-provider-actions"
  role                           = aws_iam_role.prod_run_provider_actions_role.arn
  handler                        = "lambda.run_provider_actions.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 180
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
    variables = {
      CDR_DB_ENDPOINT  = "https://cdr-db.shaka.tel/backend-api/run-provider-actions/"
      CDR_DB_API_TOKEN = local.cdr_db_creds["LAMBDA_API_TOKEN"]
    }
  }
}

resource "aws_lambda_alias" "prod_run_provider_actions_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_run_provider_actions.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }

}


resource "aws_iam_role" "prod_run_provider_actions_schedule_role" {
  name = "prod_run_provider_actions_schedule_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "scheduler.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

data "aws_iam_policy_document" "prod_run_provider_actions_schedule_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}

resource "aws_iam_policy" "prod_run_provider_actions_schedule_policy" {
  name        = "prod-run-provider-actions-schedule-policy"
  path        = "/"
  description = "IAM policy for the provider file run"
  policy      = data.aws_iam_policy_document.prod_run_provider_actions_schedule_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_run_provider_actions_scedule_attach" {
  role       = aws_iam_role.prod_run_provider_actions_schedule_role.name
  policy_arn = aws_iam_policy.prod_run_provider_actions_schedule_policy.arn
}


resource "aws_scheduler_schedule" "prod_run_provider_actions_schedule" {
  name       = "prod-run-provider-actions-schedule"
  group_name = "default"
  flexible_time_window {
    mode = "OFF"
  }
  description         = "Fires every hour to run PROVIDER actions"
  schedule_expression = "rate(5 minutes)"
  target {
    arn      = aws_lambda_function.prod_run_provider_actions.arn
    role_arn = aws_iam_role.prod_run_provider_actions_schedule_role.arn
    retry_policy {
      maximum_retry_attempts = 0
    }
  }
}

