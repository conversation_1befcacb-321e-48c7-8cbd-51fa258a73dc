resource "aws_elastic_beanstalk_application" "prod_cdr_db_eb_app" {
  name = "prod-cdr-db-eb-app"
}

resource "aws_cloudwatch_log_group" "prod_cdr_db_eb_log_group" {
  name              = "/aws/elasticbeanstalk/prod-cdr-db-eb-env/django"
  retention_in_days = 7
}

resource "aws_elastic_beanstalk_environment" "prod_cdr_db_eb_env" {
  name                = "prod-cdr-db-eb-env"
  application         = aws_elastic_beanstalk_application.prod_cdr_db_eb_app.name
  solution_stack_name = "64bit Amazon Linux 2023 v4.0.6 running Python 3.11"

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DJANGO_SETTINGS_MODULE"
    value     = "cdr_db.production_settings"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "PROVIDER_SPECIFIC_CDR_TOPIC_ARN"
    value     = aws_sns_topic.prod_provider_specific_cdr_topic.arn
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AGNOSTIC_CDR_GENERATED_TOPIC_ARN"
    value     = aws_sns_topic.prod_provider_agnostic_cdr_topic.arn
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_USER"
    value     = local.cdr_db_creds["DB_USER"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_PASSWORD"
    value     = local.cdr_db_creds["DB_PASSWORD"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_HOST"
    value     = aws_db_instance.prod_cdr_db.address
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_NAME"
    value     = aws_db_instance.prod_cdr_db.db_name
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DB_NAME"
    value     = aws_db_instance.prod_cdr_db.db_name
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AWS_DEFAULT_REGION"
    value     = "eu-west-2"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "LAMBDA_API_TOKEN"
    value     = local.cdr_db_creds["LAMBDA_API_TOKEN"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "NEXUS_API_TOKEN"
    value     = local.cdr_db_creds["NEXUS_API_TOKEN"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "TRANSATEL_ACCESS_TOKEN"
    value     = local.cdr_db_creds["TRANSATEL_ACCESS_TOKEN"]
    resource  = ""
  }


  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "GAMMA_WEBHOOK_SECRET_KEY"
    value     = local.cdr_db_creds["GAMMA_WEBHOOK_SECRET_KEY"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "GAMMA_USERNAME"
    value     = local.cdr_db_creds["GAMMA_USERNAME"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "GAMMA_PASSWORD"
    value     = local.cdr_db_creds["GAMMA_PASSWORD"]
    resource  = ""
  }



  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AIRTABLE_BASE_ID"
    value     = "appWpKQdrOdHNlhH5"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AIRTABLE_PAT"
    value     = local.cdr_db_creds["AIRTABLE_PAT"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AIRTABLE_TABLE_NAME_DATA"
    value     = "tblyoO7aynqzEco8E"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AIRTABLE_TABLE_NAME_VOICE"
    value     = "tbl6OUwrmO0Pk3xxN"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AIRTABLE_TABLE_NAME_SMS"
    value     = "tbljTitQOS3BmDuf3"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "DYNAMODB_AIRTABLE_ACTIVE_SUBSCRIPTIONS"
    value     = "airtable-active-subscriptions"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SFTP_USERNAME"
    value     = local.transatel_sftp_creds["SFTP_USERNAME"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SFTP_PASSWORD"
    value     = local.transatel_sftp_creds["SFTP_PASSWORD"]
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SFTP_HOST"
    value     = "sftp-public.transatel.com"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SFTP_PORT"
    value     = 22
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "SFTP_DIR"
    value     = "/WholesaleCDRs/MediatedCDRs"
    resource  = ""
  }

  setting {
    namespace = "aws:ec2:instances"
    name      = "InstanceTypes"
    value     = "t2.micro"
    resource  = ""
  }
  setting {
    namespace = "aws:elasticbeanstalk:cloudwatch:logs"
    name      = "StreamLogs"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = 1
    resource  = ""
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.prod_eb_profile.name
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:container:python"
    name      = "WSGIPath"
    value     = "cdr_db.wsgi:application"
    resource  = ""
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "LoadBalancerType"
    value     = "application"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "ListenerEnabled"
    value     = "true"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "Protocol"
    value     = "HTTPS"
    resource  = ""
  }

  setting {
    namespace = "aws:elbv2:listener:443"
    name      = "SSLCertificateArns"
    value     = "arn:aws:acm:eu-west-2:************:certificate/0903e571-63a7-4ce2-ab84-3fd34d4ba9f5"
    resource  = ""
  }
}


# https://epam.github.io/edp-install/operator-guide/waf-tf-configuration/
resource "aws_wafv2_regex_pattern_set" "prod_cdr_db_host_regex" {
  name  = "prod-cdr-db-host-regex"
  scope = "REGIONAL"

  regular_expression {
    regex_string = "cdr-db.shaka.tel"
  }
}

resource "aws_wafv2_web_acl" "prod_cdr_db_host_acl" {
  name  = "prod-cdr-db-host-acl"
  scope = "REGIONAL"

  default_action {
    block {}
  }

  rule {
    name     = "AWS-AWSManagedRulesCommonRuleSet"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesCommonRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesLinuxRuleSet"
    priority = 2

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesLinuxRuleSet"
        vendor_name = "AWS"
      }
    }

    override_action {
      none {}
    }


    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesLinuxRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
    priority = 3

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesKnownBadInputsRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWS-AWSManagedRulesKnownBadInputsRuleSet"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "PreventHostInjections"
    priority = 0

    statement {
      regex_pattern_set_reference_statement {
        arn = aws_wafv2_regex_pattern_set.prod_cdr_db_host_regex.arn

        field_to_match {
          single_header {
            name = "host"
          }
        }

        text_transformation {
          priority = 0
          type     = "NONE"
        }
      }
    }

    action {
      allow {}
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "PreventHostInjections"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "prod-cdr-db-host-acl"
    sampled_requests_enabled   = true
  }
}

resource "aws_wafv2_web_acl_association" "prod_waf_cdr_db_alb" {
  resource_arn = aws_elastic_beanstalk_environment.prod_cdr_db_eb_env.load_balancers[0]
  web_acl_arn  = aws_wafv2_web_acl.prod_cdr_db_host_acl.arn
}


resource "aws_db_instance" "prod_cdr_db" {
  allocated_storage   = 10
  db_name             = "cdr_db"
  engine              = "postgres"
  engine_version      = "15.8"
  instance_class      = "db.t3.small"
  username            = local.cdr_db_creds["DB_USER"]
  password            = local.cdr_db_creds["DB_PASSWORD"]
  identifier          = "prod-cdr-db"
  deletion_protection = true
  lifecycle {
    prevent_destroy = true
  }
  backup_retention_period = 7
}
