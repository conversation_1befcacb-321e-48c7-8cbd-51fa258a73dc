
data "aws_iam_policy_document" "prod_gamma_alert_files_notification_policy" {
  statement {
    effect = "Allow"

    principals {
      type        = "*"
      identifiers = ["*"]
    }

    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:*:*:*"]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"
      values   = ["arn:aws:s3:::prod-shaka-alerts-inbound"]
    }
  }
}

resource "aws_sqs_queue" "prod_gamma_new_alert_file_queue" {
  name                       = "prod-gamma-new-alert-file-queue"
  policy                     = data.aws_iam_policy_document.prod_gamma_alert_files_notification_policy.json
  visibility_timeout_seconds = 90
}

resource "aws_s3_bucket_notification" "prod_gamma_alert_files_notification" {
  bucket = "prod-shaka-alerts-inbound"

  queue {
    queue_arn = aws_sqs_queue.prod_gamma_new_alert_file_queue.arn
    events    = ["s3:ObjectCreated:*"]
  }
}



/* Django processing files */
data "aws_iam_policy_document" "prod_import_gamma_alert_file_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }
  statement {
    effect    = "Allow"
    actions   = ["s3:*"] /* todo: readonly */
    resources = ["arn:aws:s3:::prod-shaka-alerts-inbound", "arn:aws:s3:::prod-shaka-alerts-inbound/*", "arn:aws:s3:::prod-shaka-alerts-inbound/"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["sqs:*"] /* todo: readonly */
    resources = ["${aws_sqs_queue.prod_gamma_new_alert_file_queue.arn}"]
  }

}

resource "aws_iam_policy" "prod_import_gamma_alert_file_policy" {
  name        = "prod-import-gamma-alert-file-policy"
  path        = "/"
  description = "IAM policy for the alert file import"
  policy      = data.aws_iam_policy_document.prod_import_gamma_alert_file_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_import_gamma_alert_file_attach" {
  role       = aws_iam_role.prod_import_gamma_alert_file_role.name
  policy_arn = aws_iam_policy.prod_import_gamma_alert_file_policy.arn
}

resource "aws_iam_role" "prod_import_gamma_alert_file_role" {
  name = "prod_import_gamma_alert_file_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_import_gamma_alert_file" {
  function_name                  = "prod-import-gamma-alert-file"
  role                           = aws_iam_role.prod_import_gamma_alert_file_role.arn
  handler                        = "lambda.import_gamma_alert_file.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 90
  reserved_concurrent_executions = 2

  memory_size = 256

  vpc_config {
    subnet_ids         = ["subnet-043ded321d0fab1dd", "subnet-01190ee0916d2651a", "subnet-0bf717b067734bbac"]
    security_group_ids = ["sg-034b2cf978c016575"]
  }


  environment {
    variables = {
      S3_BUCKET_NAME         = "prod-shaka-alerts-inbound"
      DJANGO_SETTINGS_MODULE = "cdr_db.lambda_settings"
      DB_USER                = local.cdr_db_creds["DB_USER"]
      DB_PASSWORD            = local.cdr_db_creds["DB_PASSWORD"]
      DB_HOST                = aws_db_instance.prod_cdr_db.address
      DB_NAME                = aws_db_instance.prod_cdr_db.db_name
    }
  }
}

resource "aws_lambda_alias" "prod_import_gamma_alert_file_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_import_gamma_alert_file.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }

}


resource "aws_lambda_event_source_mapping" "prod_import_gamma_alert_file_mapping" {
  event_source_arn = aws_sqs_queue.prod_gamma_new_alert_file_queue.arn
  function_name    = aws_lambda_function.prod_import_gamma_alert_file.arn
}
