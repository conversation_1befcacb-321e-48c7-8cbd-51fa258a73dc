data "aws_iam_policy_document" "prod_fetch_transatel_sftp_files_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }
  statement {
    effect    = "Allow"
    actions   = ["s3:*"]
    resources = ["${aws_s3_bucket.prod_transatel_cdr_files.arn}", "${aws_s3_bucket.prod_transatel_cdr_files.arn}/*", "${aws_s3_bucket.prod_transatel_cdr_files.arn}/"]
  }

}

resource "aws_iam_policy" "prod_fetch_transatel_sftp_files_policy" {
  name        = "prod-fetch-transatel-sftp-files-policy"
  path        = "/"
  description = "IAM policy for the transatel sftp fetch"
  policy      = data.aws_iam_policy_document.prod_fetch_transatel_sftp_files_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_fetch_transatel_sftp_files_attach" {
  role       = aws_iam_role.prod_fetch_transatel_sftp_files_role.name
  policy_arn = aws_iam_policy.prod_fetch_transatel_sftp_files_policy.arn
}

resource "aws_iam_role" "prod_fetch_transatel_sftp_files_role" {
  name = "prod_fetch_transatel_sftp_files_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_fetch_transatel_sftp_files" {
  function_name                  = "prod-fetch-transatel-sftp-files"
  role                           = aws_iam_role.prod_fetch_transatel_sftp_files_role.arn
  handler                        = "fetch_transatel_sftp_files.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 90
  reserved_concurrent_executions = 2

  environment {
    variables = {
      S3_BUCKET_NAME = aws_s3_bucket.prod_transatel_cdr_files.bucket
      SFTP_USERNAME  = local.transatel_sftp_creds["SFTP_USERNAME"]
      SFTP_PASSWORD  = local.transatel_sftp_creds["SFTP_PASSWORD"]
      SFTP_HOST      = "sftp-public.transatel.com"
      SFTP_PORT      = 22
      SFTP_DIR       = "/WholesaleCDRs/MediatedCDRs"
    }
  }
}

resource "aws_lambda_alias" "prod_fetch_transatel_sftp_files_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_fetch_transatel_sftp_files.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }

}

resource "aws_iam_role" "prod_fetch_transatel_sftp_files_schedule_role" {
  name = "prod_fetch_transatel_sftp_files_schedule_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "scheduler.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

data "aws_iam_policy_document" "prod_fetch_transatel_sftp_files_schedule_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}

resource "aws_iam_policy" "prod_fetch_transatel_sftp_files_schedule_policy" {
  name        = "prod-fetch-transatel-sftp-files-schedule-policy"
  path        = "/"
  description = "IAM policy for the transatel sftp fetch scheduler"
  policy      = data.aws_iam_policy_document.prod_fetch_transatel_sftp_files_schedule_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_fetch_transatel_sftp_files_scedule_attach" {
  role       = aws_iam_role.prod_fetch_transatel_sftp_files_schedule_role.name
  policy_arn = aws_iam_policy.prod_fetch_transatel_sftp_files_schedule_policy.arn
}


resource "aws_scheduler_schedule" "prod_fetch_transatel_sftp_files_schedule" {
  name       = "prod-fetch-transatel-sftp-files-schedule"
  group_name = "default"
  flexible_time_window {
    mode = "OFF"
  }
  description         = "Fires every 5 minutes to fetch transatel files from sftp"
  schedule_expression = "rate(5 minutes)"
  target {
    arn      = aws_lambda_function.prod_fetch_transatel_sftp_files.arn
    role_arn = aws_iam_role.prod_fetch_transatel_sftp_files_schedule_role.arn
    retry_policy {
      maximum_retry_attempts = 0
    }
  }
}


resource "aws_s3_bucket" "prod_transatel_cdr_files" {
  bucket = "prod-transatel-cdr-files"
  lifecycle {
    prevent_destroy = true
  }
}

resource "aws_s3_bucket_versioning" "prod_transatel_cdr_files_versioning" {
  bucket = aws_s3_bucket.prod_transatel_cdr_files.id
  versioning_configuration {
    status = "Enabled"
  }
}




data "aws_iam_policy_document" "prod_transatel_cdr_files_notification_policy" {
  statement {
    effect = "Allow"

    principals {
      type        = "*"
      identifiers = ["*"]
    }

    actions   = ["sqs:SendMessage"]
    resources = ["arn:aws:sqs:*:*:*"]

    condition {
      test     = "ArnEquals"
      variable = "aws:SourceArn"
      values   = [aws_s3_bucket.prod_transatel_cdr_files.arn]
    }
  }
}

resource "aws_sqs_queue" "prod_transatel_new_file_queue" {
  name   = "prod-transatel-new-file-queue"
  policy = data.aws_iam_policy_document.prod_transatel_cdr_files_notification_policy.json
}

resource "aws_s3_bucket_notification" "prod_transatel_cdr_files_notification" {
  bucket = aws_s3_bucket.prod_transatel_cdr_files.id

  queue {
    queue_arn = aws_sqs_queue.prod_transatel_new_file_queue.arn
    events    = ["s3:ObjectCreated:*"]
  }
}




/* Django processing files */
data "aws_iam_policy_document" "prod_import_cdr_file_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }
  statement {
    effect    = "Allow"
    actions   = ["s3:*"] /* todo: readonly */
    resources = ["${aws_s3_bucket.prod_transatel_cdr_files.arn}", "${aws_s3_bucket.prod_transatel_cdr_files.arn}/*", "${aws_s3_bucket.prod_transatel_cdr_files.arn}/"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["sqs:*"] /* todo: readonly */
    resources = ["${aws_sqs_queue.prod_transatel_new_file_queue.arn}"]
  }

}

resource "aws_iam_policy" "prod_import_cdr_file_policy" {
  name        = "prod-import-cdr-file-policy"
  path        = "/"
  description = "IAM policy for the cdr file import"
  policy      = data.aws_iam_policy_document.prod_import_cdr_file_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_import_cdr_file_attach" {
  role       = aws_iam_role.prod_import_cdr_file_role.name
  policy_arn = aws_iam_policy.prod_import_cdr_file_policy.arn
}

resource "aws_iam_role" "prod_import_cdr_file_role" {
  name = "prod_import_cdr_file_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_import_cdr_file" {
  function_name                  = "prod-import-cdr-file"
  role                           = aws_iam_role.prod_import_cdr_file_role.arn
  handler                        = "lambda.import_cdr_file.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 90
  reserved_concurrent_executions = 2

  memory_size = 256

  vpc_config {
    subnet_ids         = ["subnet-043ded321d0fab1dd", "subnet-01190ee0916d2651a", "subnet-0bf717b067734bbac"]
    security_group_ids = ["sg-034b2cf978c016575"]
  }


  environment {
    variables = {
      S3_BUCKET_NAME         = aws_s3_bucket.prod_transatel_cdr_files.bucket
      DJANGO_SETTINGS_MODULE = "cdr_db.lambda_settings"
      DB_USER                = local.cdr_db_creds["DB_USER"]
      DB_PASSWORD            = local.cdr_db_creds["DB_PASSWORD"]
      DB_HOST                = aws_db_instance.prod_cdr_db.address
      DB_NAME                = aws_db_instance.prod_cdr_db.db_name
    }
  }
}

resource "aws_lambda_alias" "prod_import_cdr_file_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_import_cdr_file.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }

}


resource "aws_lambda_event_source_mapping" "prod_import_cdr_file_mapping" {
  event_source_arn = aws_sqs_queue.prod_transatel_new_file_queue.arn
  function_name    = aws_lambda_function.prod_import_cdr_file.arn
}





/* Djagno gap check */
data "aws_iam_policy_document" "prod_check_for_cdr_gaps_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

}

resource "aws_iam_policy" "prod_check_for_cdr_gaps_policy" {
  name        = "prod-check-for-cdr-gaps-policy"
  path        = "/"
  description = "IAM policy for the gap check"
  policy      = data.aws_iam_policy_document.prod_check_for_cdr_gaps_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_check_for_cdr_gaps_attach" {
  role       = aws_iam_role.prod_check_for_cdr_gaps_role.name
  policy_arn = aws_iam_policy.prod_check_for_cdr_gaps_policy.arn
}

resource "aws_iam_role" "prod_check_for_cdr_gaps_role" {
  name = "prod_check_for_cdr_gaps_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_check_for_cdr_gaps" {
  function_name                  = "prod-check-for-cdr-gaps"
  role                           = aws_iam_role.prod_check_for_cdr_gaps_role.arn
  handler                        = "lambda.check_for_cdr_gaps.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 90
  reserved_concurrent_executions = 1

  memory_size = 512

  vpc_config {
    subnet_ids         = ["subnet-043ded321d0fab1dd", "subnet-01190ee0916d2651a", "subnet-0bf717b067734bbac"]
    security_group_ids = ["sg-034b2cf978c016575"]
  }


  environment {
    variables = {
      DJANGO_SETTINGS_MODULE = "cdr_db.lambda_settings"
      DB_USER                = local.cdr_db_creds["DB_USER"]
      DB_PASSWORD            = local.cdr_db_creds["DB_PASSWORD"]
      DB_HOST                = aws_db_instance.prod_cdr_db.address
      DB_NAME                = aws_db_instance.prod_cdr_db.db_name
    }
  }
}

resource "aws_lambda_alias" "prod_check_for_cdr_gaps_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_check_for_cdr_gaps.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }

}


resource "aws_iam_role" "prod_check_for_cdr_gaps_schedule_role" {
  name = "prod_check_for_cdr_gaps_schedule_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "scheduler.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

data "aws_iam_policy_document" "prod_check_for_cdr_gaps_schedule_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}

resource "aws_iam_policy" "prod_check_for_cdr_gaps_schedule_policy" {
  name        = "prod-check-for-cdr-gaps-schedule-policy"
  path        = "/"
  description = "IAM policy for the cdr gap check"
  policy      = data.aws_iam_policy_document.prod_check_for_cdr_gaps_schedule_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_check_for_cdr_gaps_scedule_attach" {
  role       = aws_iam_role.prod_check_for_cdr_gaps_schedule_role.name
  policy_arn = aws_iam_policy.prod_check_for_cdr_gaps_schedule_policy.arn
}


resource "aws_scheduler_schedule" "prod_check_for_cdr_gaps_schedule" {
  name       = "prod-check-for-cdr-gaps-schedule"
  group_name = "default"
  flexible_time_window {
    mode = "OFF"
  }
  description         = "Fires every hour to check for cdr agps"
  schedule_expression = "rate(5 minutes)"
  target {
    arn      = aws_lambda_function.prod_check_for_cdr_gaps.arn
    role_arn = aws_iam_role.prod_check_for_cdr_gaps_schedule_role.arn
    retry_policy {
      maximum_retry_attempts = 0
    }
  }
}


/* Djagno cdr file cleanup */
data "aws_iam_policy_document" "prod_cleanup_cdr_files_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

}

resource "aws_iam_policy" "prod_cleanup_cdr_files_policy" {
  name        = "prod-cleanup-cdr-files-policy"
  path        = "/"
  description = "IAM policy for the cdr file cleanup"
  policy      = data.aws_iam_policy_document.prod_cleanup_cdr_files_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_cleanup_cdr_files_attach" {
  role       = aws_iam_role.prod_cleanup_cdr_files_role.name
  policy_arn = aws_iam_policy.prod_cleanup_cdr_files_policy.arn
}

resource "aws_iam_role" "prod_cleanup_cdr_files_role" {
  name = "prod_cleanup_cdr_files_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_cleanup_cdr_files" {
  function_name                  = "prod-cleanup-cdr-files"
  role                           = aws_iam_role.prod_cleanup_cdr_files_role.arn
  handler                        = "lambda.cleanup_cdr_files.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 180
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
    variables = {
      CDR_DB_ENDPOINT  = "https://cdr-db.shaka.tel/backend-api/cleanup-cdr-files/"
      CDR_DB_API_TOKEN = local.cdr_db_creds["LAMBDA_API_TOKEN"]
    }
  }
}

resource "aws_lambda_alias" "prod_cleanup_cdr_files_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_cleanup_cdr_files.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }

}


resource "aws_iam_role" "prod_cleanup_cdr_files_schedule_role" {
  name = "prod_cleanup_cdr_files_schedule_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "scheduler.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

data "aws_iam_policy_document" "prod_cleanup_cdr_files_schedule_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}

resource "aws_iam_policy" "prod_cleanup_cdr_files_schedule_policy" {
  name        = "prod-cleanup-cdr-files-schedule-policy"
  path        = "/"
  description = "IAM policy for the cdr file cleanup"
  policy      = data.aws_iam_policy_document.prod_cleanup_cdr_files_schedule_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_cleanup_cdr_files_scedule_attach" {
  role       = aws_iam_role.prod_cleanup_cdr_files_schedule_role.name
  policy_arn = aws_iam_policy.prod_cleanup_cdr_files_schedule_policy.arn
}


resource "aws_scheduler_schedule" "prod_cleanup_cdr_files_schedule" {
  name       = "prod-cleanup-cdr-files-schedule"
  group_name = "default"
  flexible_time_window {
    mode = "OFF"
  }
  description         = "Fires every hour to cleanup CDR files"
  schedule_expression = "rate(5 minutes)"
  target {
    arn      = aws_lambda_function.prod_cleanup_cdr_files.arn
    role_arn = aws_iam_role.prod_cleanup_cdr_files_schedule_role.arn
    retry_policy {
      maximum_retry_attempts = 0
    }
  }
}


/* Post to cdr api for airtable import */
data "aws_iam_policy_document" "prod_run_airtable_import_policy_data" {
  statement {
    effect = "Allow"

    actions = [
      "logs:CreateLogGroup",
      "logs:CreateLogStream",
      "logs:PutLogEvents",
    ]

    resources = ["arn:aws:logs:*:*:*"]
  }

  statement {
    actions   = ["ec2:CreateNetworkInterface", "ec2:DescribeNetworkInterfaces", "ec2:DeleteNetworkInterface", "ec2:AssignPrivateIpAddresses", "ec2:UnassignPrivateIpAddresses"]
    effect    = "Allow"
    resources = ["*"]
  }

}

resource "aws_iam_policy" "prod_run_airtable_import_policy" {
  name        = "prod-run-airtable-import-policy"
  path        = "/"
  description = "IAM policy for importing cdrs to airtable"
  policy      = data.aws_iam_policy_document.prod_run_airtable_import_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_run_airtable_import_attach" {
  role       = aws_iam_role.prod_run_airtable_import_role.name
  policy_arn = aws_iam_policy.prod_run_airtable_import_policy.arn
}

resource "aws_iam_role" "prod_run_airtable_import_role" {
  name = "prod_run_airtable_import_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

resource "aws_lambda_function" "prod_run_airtable_import" {
  function_name                  = "prod-run-airtable-import"
  role                           = aws_iam_role.prod_run_airtable_import_role.arn
  handler                        = "run_airtable_import.lambda_handler"
  runtime                        = "python3.11"
  s3_bucket                      = var.lambda_artifact_bucket
  s3_key                         = "lambda-builds/prod/empty.zip"
  publish                        = false // Don't publish because the alias will reset the version
  timeout                        = 180
  reserved_concurrent_executions = 1

  memory_size = 256

  environment {
    variables = {
      CDR_DB_ENDPOINT  = "https://cdr-db.shaka.tel/backend-api/run-airtable-import/"
      CDR_DB_API_TOKEN = local.cdr_db_creds["LAMBDA_API_TOKEN"]
    }
  }
}

resource "aws_lambda_alias" "prod_run_airtable_import_alias" {
  name             = "deployed"
  description      = "Deployed version"
  function_name    = aws_lambda_function.prod_run_airtable_import.arn
  function_version = "1"
  lifecycle {
    ignore_changes = [
      function_version
    ]
  }

}


resource "aws_iam_role" "prod_run_airtable_import_schedule_role" {
  name = "prod_run_airtable_import_schedule_role"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "scheduler.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
EOF
}

data "aws_iam_policy_document" "prod_run_airtable_import_schedule_policy_data" {
  statement {
    actions = [
      "lambda:InvokeFunction"
    ]
    effect    = "Allow"
    resources = ["*"]
  }
}

resource "aws_iam_policy" "prod_run_airtable_import_schedule_policy" {
  name        = "prod-run-airtable-import-schedule-policy"
  path        = "/"
  description = "IAM policy for importing cdrs to airtable"
  policy      = data.aws_iam_policy_document.prod_run_airtable_import_schedule_policy_data.json
}


resource "aws_iam_role_policy_attachment" "prod_run_airtable_import_scedule_attach" {
  role       = aws_iam_role.prod_run_airtable_import_schedule_role.name
  policy_arn = aws_iam_policy.prod_run_airtable_import_schedule_policy.arn
}


resource "aws_scheduler_schedule" "prod_run_airtable_import_schedule" {
  name       = "prod-run-airtable-import-schedule"
  group_name = "default"
  flexible_time_window {
    mode = "OFF"
  }
  description         = "Fires every half hour to import CDR files"
  schedule_expression = "rate(5 minutes)"
  target {
    arn      = aws_lambda_function.prod_run_airtable_import.arn
    role_arn = aws_iam_role.prod_run_airtable_import_schedule_role.arn
    retry_policy {
      maximum_retry_attempts = 0
    }
  }
}
