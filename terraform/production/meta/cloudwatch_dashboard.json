{ "widgets": [
  {
            "type": "alarm",
            "x": 12,
            "y": 0,
            "width": 12,
            "height": 4,
            "properties": {
                "title": "Alarms",
              "alarms": ${jsonencode([
                for alarm in alarms:
                "${alarm.arn}"
              ])}
            }
  },
    %{ for name, lambda_details in lambdas ~}
  {
    "height": 5,
    "width": 6,
    "y": ${4 + (5 * lambda_details.index)},
    "x": 0,
    "type": "log",
    "properties": {
              "liveData": true,
      "query": "SOURCE '/aws/lambda/${lambda_details.lambda_function_name}' | fields @timestamp, @message | filter @message not like /^END/ and @message not like /^REPORT/ and @message not like /^INIT_START/ | sort @timestamp desc\n | limit 6",
      "region": "eu-west-2",
      "stacked": false,
      "view": "table",
      "title": "${name} Logs"
    }
  },
  {
    "height": 5,
    "width": 6,
    "y": ${4 + (5 * lambda_details.index)},
    "x": 6,
    "type": "metric",
    "properties": {
              "liveData": true,
      "title": "${name} Stats",
      "metrics": [
        [ "AWS/Lambda", "Errors", "FunctionName", "${lambda_details.lambda_function_name}", { "region": "eu-west-2", "color": "#d62728" } ],
        [ ".", "Duration", ".", ".", { "region": "eu-west-2", "color": "#1f77b4", "stat": "Average" } ],
        [ "...", { "region": "eu-west-2", "stat": "Maximum", "color": "#ff7f0e" } ],
        [ ".", "Invocations", ".", ".", { "region": "eu-west-2", "color": "#2ca02c", "yAxis": "left" } ]
      ],
      "sparkline": true,
      "view": "singleValue",
      "region": "eu-west-2",
      "period": 900,
      "setPeriodToTimeRange": true,
      "stat": "Sum"
    }
  },
      %{ endfor ~}
    %{ for index, queue_name in queues ~}
{
            "type": "metric",
            "x": 12,
  "y": ${4 + (5 * index)},
            "width": 6,
            "height": 5,
  "properties": {
              "liveData": true,
    "title": "${queue_name} Messages",
                "metrics": [
                  [ "AWS/SQS", "ApproximateNumberOfMessagesVisible", "QueueName", "${queue_name}", { "stat": "Maximum", "color": "#d62728", "label": "Available" } ],
                    [ "AWS/SQS", "NumberOfMessagesSent", "QueueName", "${queue_name}", { "region": "eu-west-2", "label": "Added" } ],
                    [ ".", "NumberOfMessagesReceived", ".", ".", { "region": "eu-west-2", "color": "#2ca02c", "label": "Processed" } ],
                    [ ".", "NumberOfMessagesDeleted", ".", ".", { "region": "eu-west-2", "color": "#bcbd22", "label": "Removed" } ]
                ],
                "view": "timeSeries",
                "region": "eu-west-2",
                "stacked": true,
                "setPeriodToTimeRange": true,
                "stat": "Sum",
                "period": 900
            }
        },
        {
            "type": "metric",
            "x": 18,
            "y": ${4 + (5 * index)},
            "width": 6,
            "height": 5,
            "properties": {
              "liveData": true,
              "title": "${queue_name} Health",
                "metrics": [
                    [ "AWS/SQS", "ApproximateAgeOfOldestMessage", "QueueName", "${queue_name}", { "region": "eu-west-2", "label": "Oldest", "stat": "Maximum" } ],
                  [ ".", "ApproximateNumberOfMessagesVisible", ".", ".", { "region": "eu-west-2", "label": "Available", "stat": "Maximum" } ],
                  [ ".", "ApproximateNumberOfMessagesNotVisible", ".", ".", { "region": "eu-west-2", "label": "Processing", "stat": "Maximum" } ],
                    [ ".", "SentMessageSize", ".", ".", { "color": "#c5b0d5", "stat": "Maximum", "label": "Max Size" } ]
                ],
                "sparkline": true,
                "view": "singleValue",
                "region": "eu-west-2",
                "period": 900,
                "stat": "Sum"
            }
        },
        %{ endfor ~}
    %{ for index, eb_details in beanstalks ~}
{
            "type": "metric",
            "x": 12,
  "y": ${4 + lambda_height + (5 * index)},
            "width": 12,
            "height": 5,
  "properties": {
    "title": "${eb_details.name}",
                "metrics": [
                    [ "AWS/ApplicationELB", "RequestCount", "LoadBalancer", "${eb_details.lb_name}" ],
                    [ ".", "HTTPCode_Target_4XX_Count", ".", "." ],
                    [ ".", "HTTPCode_Target_2XX_Count", ".", "." ],
                    [ ".", "HTTPCode_Target_5XX_Count", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": true,
                "region": "eu-west-2",
                "stat": "Sum",
                "period": 900,
                "liveData": true
            }
},
    %{ endfor ~}
  {
    "type": "metric",
    "x": 0,
    "y": 0,
    "width": 12,
    "height": 4,
    "properties": {
      "metrics": [
        [ "AWS/Lambda", "Invocations", { "region": "eu-west-2", "yAxis": "left" } ]
      ],
      "sparkline": false,
      "view": "timeSeries",
      "region": "eu-west-2",
      "period": 900,
      "stat": "Sum",
      "title": "Average Lambda Invocations",
      "stacked": false,
      "setPeriodToTimeRange": false,
      "trend": true,      
      "liveData": true,
      "annotations": {
        "horizontal": [
          {
            "label": "Alarm Threshold",
            "value": ${prod_lambda_invocations_alarm_threshold}
          }
        ]
      }
    }
  }
]
}
