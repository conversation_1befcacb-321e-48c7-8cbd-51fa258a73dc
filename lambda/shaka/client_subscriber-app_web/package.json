{"name": "client_subscriber-app_web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{ts,tsx}": ["bash -c tsc --noEmit"]}, "dependencies": {"@headlessui/react": "^1.7.18", "@intercom/messenger-js-sdk": "^0.0.13", "@stripe/react-stripe-js": "^2.6.2", "@stripe/stripe-js": "^3.0.10", "@tanstack/react-router": "^1.19.1", "@tanstack/router-vite-plugin": "^1.19.0", "axios": "^1.6.7", "country-flag-icons": "^1.5.13", "jwt-decode": "^4.0.0", "luxon": "^3.5.0", "react": "^18.2.0", "react-canvas-confetti": "^2.0.7", "react-date-picker": "^10.6.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-ga4": "^2.1.0", "react-hook-form": "^7.51.0", "react-slick": "^0.30.2", "react-spring": "^9.7.5", "react-toastify": "^10.0.5", "react-with-gesture": "^4.0.8", "react18-input-otp": "^1.1.4", "slick-carousel": "^1.8.1", "tailwind-merge": "^2.3.0", "vite-plugin-eslint": "^1.8.1", "zustand": "^4.5.4"}, "devDependencies": {"@hookform/resolvers": "^3.3.4", "@types/luxon": "^3.4.2", "@types/node": "^20.12.12", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@types/react-slick": "^0.23.13", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "clsx": "^2.1.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "lefthook": "^1.7.13", "lint-staged": "^15.2.9", "postcss": "^8.4.35", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.4", "vite-plugin-static-copy": "^1.0.5", "yup": "^1.4.0"}, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}