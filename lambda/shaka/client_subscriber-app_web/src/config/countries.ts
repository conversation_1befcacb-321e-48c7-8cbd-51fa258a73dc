export const countries: {
  value: string;
  label: string;
  zone: string;
}[] = [
  {
    value: 'AF',
    label: 'Afghanistan',
    zone: 'c'
  },
  {
    value: 'AX',
    label: 'Åland Islands',
    zone: 'd'
  },
  {
    value: 'AL',
    label: 'Albania',
    zone: 'b'
  },
  {
    value: 'DZ',
    label: 'Algeria',
    zone: 'd'
  },
  {
    value: 'AS',
    label: 'American Samoa',
    zone: 'd'
  },
  {
    value: 'AD',
    label: 'Andorra',
    zone: 'd'
  },
  {
    value: 'AO',
    label: 'Angola',
    zone: 'd'
  },
  {
    value: 'AI',
    label: 'Anguilla',
    zone: 'c'
  },
  {
    value: 'AQ',
    label: 'Antarctica',
    zone: 'd'
  },
  {
    value: 'AG',
    label: 'Antigua and Barbuda',
    zone: 'c'
  },
  {
    value: 'AR',
    label: 'Argentina',
    zone: 'b'
  },
  {
    value: 'AM',
    label: 'Armenia',
    zone: 'b'
  },
  {
    value: 'AW',
    label: 'Aruba',
    zone: 'd'
  },
  {
    value: 'AU',
    label: 'Australia',
    zone: 'b'
  },
  {
    value: 'AT',
    label: 'Austria',
    zone: 'e'
  },
  {
    value: 'AZ',
    label: 'Azerbaijan',
    zone: 'c'
  },
  {
    value: 'BS',
    label: 'Bahamas',
    zone: 'd'
  },
  {
    value: 'BH',
    label: 'Bahrain',
    zone: 'c'
  },
  {
    value: 'BD',
    label: 'Bangladesh',
    zone: 'c'
  },
  {
    value: 'BB',
    label: 'Barbados',
    zone: 'c'
  },
  {
    value: 'BY',
    label: 'Belarus',
    zone: 'c'
  },
  {
    value: 'BE',
    label: 'Belgium',
    zone: 'e'
  },
  {
    value: 'BZ',
    label: 'Belize',
    zone: 'd'
  },
  {
    value: 'BJ',
    label: 'Benin',
    zone: 'd'
  },
  {
    value: 'BM',
    label: 'Bermuda',
    zone: 'c'
  },
  {
    value: 'BT',
    label: 'Bhutan',
    zone: 'd'
  },
  {
    value: 'BO',
    label: 'Bolivia, Plurinational State of',
    zone: 'd'
  },
  {
    value: 'BQ',
    label: 'Bonaire, Sint Eustatius and Saba',
    zone: 'd'
  },
  {
    value: 'BA',
    label: 'Bosnia and Herzegovina',
    zone: 'c'
  },
  {
    value: 'BW',
    label: 'Botswana',
    zone: 'c'
  },
  {
    value: 'BV',
    label: 'Bouvet Island',
    zone: 'd'
  },
  {
    value: 'BR',
    label: 'Brazil',
    zone: 'b'
  },
  {
    value: 'IO',
    label: 'British Indian Ocean Territory',
    zone: 'd'
  },
  {
    value: 'BN',
    label: 'Brunei Darussalam',
    zone: 'b'
  },
  {
    value: 'BG',
    label: 'Bulgaria',
    zone: 'e'
  },
  {
    value: 'BF',
    label: 'Burkina Faso',
    zone: 'd'
  },
  {
    value: 'BI',
    label: 'Burundi',
    zone: 'd'
  },
  {
    value: 'CV',
    label: 'Cabo Verde',
    zone: 'd'
  },
  {
    value: 'KH',
    label: 'Cambodia',
    zone: 'd'
  },
  {
    value: 'CM',
    label: 'Cameroon',
    zone: 'd'
  },
  {
    value: 'CA',
    label: 'Canada',
    zone: 'b'
  },
  {
    value: 'KY',
    label: 'Cayman Islands',
    zone: 'c'
  },
  {
    value: 'CF',
    label: 'Central African Republic',
    zone: 'd'
  },
  {
    value: 'TD',
    label: 'Chad',
    zone: 'd'
  },
  {
    value: 'CL',
    label: 'Chile',
    zone: 'b'
  },
  {
    value: 'CN',
    label: 'China',
    zone: 'b'
  },
  {
    value: 'CX',
    label: 'Christmas Island',
    zone: 'd'
  },
  {
    value: 'CC',
    label: 'Cocos (Keeling) Islands',
    zone: 'd'
  },
  {
    value: 'CO',
    label: 'Colombia',
    zone: 'b'
  },
  {
    value: 'KM',
    label: 'Comoros',
    zone: 'd'
  },
  {
    value: 'CG',
    label: 'Congo',
    zone: 'c'
  },
  {
    value: 'CD',
    label: 'Congo, Democratic Republic of the',
    zone: 'd'
  },
  {
    value: 'CK',
    label: 'Cook Islands',
    zone: 'd'
  },
  {
    value: 'CR',
    label: 'Costa Rica',
    zone: 'b'
  },
  {
    value: 'HR',
    label: 'Croatia',
    zone: 'e'
  },
  {
    value: 'CU',
    label: 'Cuba',
    zone: 'd'
  },
  {
    value: 'CW',
    label: 'Curaçao',
    zone: 'd'
  },
  {
    value: 'CY',
    label: 'Cyprus',
    zone: 'e'
  },
  {
    value: 'CZ',
    label: 'Czechia',
    zone: 'e'
  },
  {
    value: 'CI',
    label: "Côte d'Ivoire",
    zone: 'c'
  },
  {
    value: 'DK',
    label: 'Denmark',
    zone: 'e'
  },
  {
    value: 'DJ',
    label: 'Djibouti',
    zone: 'd'
  },
  {
    value: 'DM',
    label: 'Dominica',
    zone: 'c'
  },
  {
    value: 'DO',
    label: 'Dominican Republic',
    zone: 'c'
  },
  {
    value: 'EC',
    label: 'Ecuador',
    zone: 'b'
  },
  {
    value: 'EG',
    label: 'Egypt',
    zone: 'c'
  },
  {
    value: 'SV',
    label: 'El Salvador',
    zone: 'b'
  },
  {
    value: 'GQ',
    label: 'Equatorial Guinea',
    zone: 'd'
  },
  {
    value: 'ER',
    label: 'Eritrea',
    zone: 'd'
  },
  {
    value: 'EE',
    label: 'Estonia',
    zone: 'e'
  },
  {
    value: 'SZ',
    label: 'Eswatini',
    zone: 'd'
  },
  {
    value: 'ET',
    label: 'Ethiopia',
    zone: 'd'
  },
  {
    value: 'FK',
    label: 'Falkland Islands (Malvinas)',
    zone: 'd'
  },
  {
    value: 'FO',
    label: 'Faroe Islands',
    zone: 'b'
  },
  {
    value: 'FJ',
    label: 'Fiji',
    zone: 'c'
  },
  {
    value: 'FI',
    label: 'Finland',
    zone: 'e'
  },
  {
    value: 'FR',
    label: 'France',
    zone: 'e'
  },
  {
    value: 'GF',
    label: 'French Guiana',
    zone: 'e'
  },
  {
    value: 'PF',
    label: 'French Polynesia',
    zone: 'd'
  },
  {
    value: 'TF',
    label: 'French Southern Territories',
    zone: 'd'
  },
  {
    value: 'GA',
    label: 'Gabon',
    zone: 'b'
  },
  {
    value: 'GM',
    label: 'Gambia',
    zone: 'd'
  },
  {
    value: 'GE',
    label: 'Georgia',
    zone: 'b'
  },
  {
    value: 'DE',
    label: 'Germany',
    zone: 'e'
  },
  {
    value: 'GH',
    label: 'Ghana',
    zone: 'c'
  },
  {
    value: 'GI',
    label: 'Gibraltar',
    zone: 'e'
  },
  {
    value: 'GR',
    label: 'Greece',
    zone: 'e'
  },
  {
    value: 'GL',
    label: 'Greenland',
    zone: 'c'
  },
  {
    value: 'GD',
    label: 'Grenada',
    zone: 'c'
  },
  {
    value: 'GP',
    label: 'Guadeloupe',
    zone: 'e'
  },
  {
    value: 'GU',
    label: 'Guam',
    zone: 'c'
  },
  {
    value: 'GT',
    label: 'Guatemala',
    zone: 'b'
  },
  {
    value: 'GG',
    label: 'Guernsey',
    zone: 'e'
  },
  {
    value: 'GN',
    label: 'Guinea',
    zone: 'd'
  },
  {
    value: 'GW',
    label: 'Guinea-Bissau',
    zone: 'd'
  },
  {
    value: 'GY',
    label: 'Guyana',
    zone: 'd'
  },
  {
    value: 'HT',
    label: 'Haiti',
    zone: 'c'
  },
  {
    value: 'HM',
    label: 'Heard Island and McDonald Islands',
    zone: 'd'
  },
  {
    value: 'VA',
    label: 'Holy See',
    zone: 'd'
  },
  {
    value: 'HN',
    label: 'Honduras',
    zone: 'b'
  },
  {
    value: 'HK',
    label: 'Hong Kong',
    zone: 'b'
  },
  {
    value: 'HU',
    label: 'Hungary',
    zone: 'e'
  },
  {
    value: 'IS',
    label: 'Iceland',
    zone: 'e'
  },
  {
    value: 'IN',
    label: 'India',
    zone: 'b'
  },
  {
    value: 'ID',
    label: 'Indonesia',
    zone: 'b'
  },
  {
    value: 'IR',
    label: 'Iran, Islamic Republic of',
    zone: 'd'
  },
  {
    value: 'IQ',
    label: 'Iraq',
    zone: 'c'
  },
  {
    value: 'IE',
    label: 'Ireland',
    zone: 'e'
  },
  {
    value: 'IM',
    label: 'Isle of Man',
    zone: 'e'
  },
  {
    value: 'IL',
    label: 'Israel',
    zone: 'b'
  },
  {
    value: 'IT',
    label: 'Italy',
    zone: 'e'
  },
  {
    value: 'JM',
    label: 'Jamaica',
    zone: 'c'
  },
  {
    value: 'JP',
    label: 'Japan',
    zone: 'b'
  },
  {
    value: 'JE',
    label: 'Jersey',
    zone: 'e'
  },
  {
    value: 'JO',
    label: 'Jordan',
    zone: 'c'
  },
  {
    value: 'KZ',
    label: 'Kazakhstan',
    zone: 'b'
  },
  {
    value: 'KE',
    label: 'Kenya',
    zone: 'c'
  },
  {
    value: 'KI',
    label: 'Kiribati',
    zone: 'd'
  },
  {
    value: 'KP',
    label: "Korea, Democratic People's Republic of",
    zone: 'd'
  },
  {
    value: 'KR',
    label: 'Korea, Republic of',
    zone: 'b'
  },
  {
    value: 'KW',
    label: 'Kuwait',
    zone: 'b'
  },
  {
    value: 'KG',
    label: 'Kyrgyzstan',
    zone: 'b'
  },
  {
    value: 'LA',
    label: "Lao People's Democratic Republic",
    zone: 'd'
  },
  {
    value: 'LV',
    label: 'Latvia',
    zone: 'e'
  },
  {
    value: 'LB',
    label: 'Lebanon',
    zone: 'd'
  },
  {
    value: 'LS',
    label: 'Lesotho',
    zone: 'd'
  },
  {
    value: 'LR',
    label: 'Liberia',
    zone: 'd'
  },
  {
    value: 'LY',
    label: 'Libya',
    zone: 'd'
  },
  {
    value: 'LI',
    label: 'Liechtenstein',
    zone: 'e'
  },
  {
    value: 'LT',
    label: 'Lithuania',
    zone: 'e'
  },
  {
    value: 'LU',
    label: 'Luxembourg',
    zone: 'e'
  },
  {
    value: 'MO',
    label: 'Macao',
    zone: 'c'
  },
  {
    value: 'MG',
    label: 'Madagascar',
    zone: 'd'
  },
  {
    value: 'MW',
    label: 'Malawi',
    zone: 'd'
  },
  {
    value: 'MY',
    label: 'Malaysia',
    zone: 'b'
  },
  {
    value: 'MV',
    label: 'Maldives',
    zone: 'd'
  },
  {
    value: 'ML',
    label: 'Mali',
    zone: 'd'
  },
  {
    value: 'MT',
    label: 'Malta',
    zone: 'e'
  },
  {
    value: 'MH',
    label: 'Marshall Islands',
    zone: 'd'
  },
  {
    value: 'MQ',
    label: 'Martinique',
    zone: 'e'
  },
  {
    value: 'MR',
    label: 'Mauritania',
    zone: 'd'
  },
  {
    value: 'MU',
    label: 'Mauritius',
    zone: 'd'
  },
  {
    value: 'YT',
    label: 'Mayotte',
    zone: 'e'
  },
  {
    value: 'MX',
    label: 'Mexico',
    zone: 'b'
  },
  {
    value: 'FM',
    label: 'Micronesia, Federated States of',
    zone: 'd'
  },
  {
    value: 'MD',
    label: 'Moldova, Republic of',
    zone: 'c'
  },
  {
    value: 'MC',
    label: 'Monaco',
    zone: 'e'
  },
  {
    value: 'MN',
    label: 'Mongolia',
    zone: 'd'
  },
  {
    value: 'ME',
    label: 'Montenegro',
    zone: 'c'
  },
  {
    value: 'MS',
    label: 'Montserrat',
    zone: 'c'
  },
  {
    value: 'MA',
    label: 'Morocco',
    zone: 'c'
  },
  {
    value: 'MZ',
    label: 'Mozambique',
    zone: 'd'
  },
  {
    value: 'MM',
    label: 'Myanmar',
    zone: 'b'
  },
  {
    value: 'NA',
    label: 'Namibia',
    zone: 'd'
  },
  {
    value: 'NR',
    label: 'Nauru',
    zone: 'd'
  },
  {
    value: 'NP',
    label: 'Nepal',
    zone: 'd'
  },
  {
    value: 'NL',
    label: 'Netherlands',
    zone: 'e'
  },
  {
    value: 'NC',
    label: 'New Caledonia',
    zone: 'd'
  },
  {
    value: 'NZ',
    label: 'New Zealand',
    zone: 'b'
  },
  {
    value: 'NI',
    label: 'Nicaragua',
    zone: 'b'
  },
  {
    value: 'NE',
    label: 'Niger',
    zone: 'b'
  },
  {
    value: 'NG',
    label: 'Nigeria',
    zone: 'b'
  },
  {
    value: 'NU',
    label: 'Niue',
    zone: 'd'
  },
  {
    value: 'NF',
    label: 'Norfolk Island',
    zone: 'd'
  },
  {
    value: 'MK',
    label: 'North Macedonia',
    zone: 'c'
  },
  {
    value: 'MP',
    label: 'Northern Mariana Islands',
    zone: 'd'
  },
  {
    value: 'NO',
    label: 'Norway',
    zone: 'e'
  },
  {
    value: 'OM',
    label: 'Oman',
    zone: 'c'
  },
  {
    value: 'PK',
    label: 'Pakistan',
    zone: 'b'
  },
  {
    value: 'PW',
    label: 'Palau',
    zone: 'd'
  },
  {
    value: 'PS',
    label: 'Palestine, State of',
    zone: 'd'
  },
  {
    value: 'PA',
    label: 'Panama',
    zone: 'b'
  },
  {
    value: 'PG',
    label: 'Papua New Guinea',
    zone: 'd'
  },
  {
    value: 'PY',
    label: 'Paraguay',
    zone: 'b'
  },
  {
    value: 'PE',
    label: 'Peru',
    zone: 'c'
  },
  {
    value: 'PH',
    label: 'Philippines',
    zone: 'd'
  },
  {
    value: 'PN',
    label: 'Pitcairn',
    zone: 'd'
  },
  {
    value: 'PL',
    label: 'Poland',
    zone: 'e'
  },
  {
    value: 'PT',
    label: 'Portugal',
    zone: 'e'
  },
  {
    value: 'PR',
    label: 'Puerto Rico',
    zone: 'b'
  },
  {
    value: 'QA',
    label: 'Qatar',
    zone: 'b'
  },
  {
    value: 'RO',
    label: 'Romania',
    zone: 'e'
  },
  {
    value: 'RU',
    label: 'Russian Federation',
    zone: 'b'
  },
  {
    value: 'RW',
    label: 'Rwanda',
    zone: 'c'
  },
  {
    value: 'RE',
    label: 'Réunion',
    zone: 'e'
  },
  {
    value: 'BL',
    label: 'Saint Barthélemy',
    zone: 'e'
  },
  {
    value: 'SH',
    label: 'Saint Helena, Ascension and Tristan da Cunha',
    zone: 'd'
  },
  {
    value: 'KN',
    label: 'Saint Kitts and Nevis',
    zone: 'c'
  },
  {
    value: 'LC',
    label: 'Saint Lucia',
    zone: 'c'
  },
  {
    value: 'MF',
    label: 'Saint Martin (French part)',
    zone: 'e'
  },
  {
    value: 'PM',
    label: 'Saint Pierre and Miquelon',
    zone: 'd'
  },
  {
    value: 'VC',
    label: 'Saint Vincent and the Grenadines',
    zone: 'c'
  },
  {
    value: 'WS',
    label: 'Samoa',
    zone: 'd'
  },
  {
    value: 'SM',
    label: 'San Marino',
    zone: 'd'
  },
  {
    value: 'ST',
    label: 'Sao Tome and Principe',
    zone: 'd'
  },
  {
    value: 'SA',
    label: 'Saudi Arabia',
    zone: 'c'
  },
  {
    value: 'SN',
    label: 'Senegal',
    zone: 'c'
  },
  {
    value: 'RS',
    label: 'Serbia',
    zone: 'c'
  },
  {
    value: 'SC',
    label: 'Seychelles',
    zone: 'd'
  },
  {
    value: 'SL',
    label: 'Sierra Leone',
    zone: 'd'
  },
  {
    value: 'SG',
    label: 'Singapore',
    zone: 'b'
  },
  {
    value: 'SX',
    label: 'Sint Maarten (Dutch part)',
    zone: 'd'
  },
  {
    value: 'SK',
    label: 'Slovakia',
    zone: 'e'
  },
  {
    value: 'SI',
    label: 'Slovenia',
    zone: 'e'
  },
  {
    value: 'SB',
    label: 'Solomon Islands',
    zone: 'd'
  },
  {
    value: 'SO',
    label: 'Somalia',
    zone: 'd'
  },
  {
    value: 'ZA',
    label: 'South Africa',
    zone: 'b'
  },
  {
    value: 'GS',
    label: 'South Georgia and the South Sandwich Islands',
    zone: 'd'
  },
  {
    value: 'SS',
    label: 'South Sudan',
    zone: 'd'
  },
  {
    value: 'ES',
    label: 'Spain',
    zone: 'e'
  },
  {
    value: 'LK',
    label: 'Sri Lanka',
    zone: 'b'
  },
  {
    value: 'SD',
    label: 'Sudan',
    zone: 'd'
  },
  {
    value: 'SR',
    label: 'Suriname',
    zone: 'd'
  },
  {
    value: 'SJ',
    label: 'Svalbard and Jan Mayen',
    zone: 'd'
  },
  {
    value: 'SE',
    label: 'Sweden',
    zone: 'e'
  },
  {
    value: 'CH',
    label: 'Switzerland',
    zone: 'e'
  },
  {
    value: 'SY',
    label: 'Syrian Arab Republic',
    zone: 'd'
  },
  {
    value: 'TW',
    label: 'Taiwan, Province of China',
    zone: 'b'
  },
  {
    value: 'TJ',
    label: 'Tajikistan',
    zone: 'c'
  },
  {
    value: 'TZ',
    label: 'Tanzania, United Republic of',
    zone: 'd'
  },
  {
    value: 'TH',
    label: 'Thailand',
    zone: 'b'
  },
  {
    value: 'TL',
    label: 'Timor-Leste',
    zone: 'd'
  },
  {
    value: 'TG',
    label: 'Togo',
    zone: 'd'
  },
  {
    value: 'TK',
    label: 'Tokelau',
    zone: 'd'
  },
  {
    value: 'TO',
    label: 'Tonga',
    zone: 'd'
  },
  {
    value: 'TT',
    label: 'Trinidad and Tobago',
    zone: 'd'
  },
  {
    value: 'TN',
    label: 'Tunisia',
    zone: 'c'
  },
  {
    value: 'TR',
    label: 'Turkey',
    zone: 'b'
  },
  {
    value: 'TM',
    label: 'Turkmenistan',
    zone: 'd'
  },
  {
    value: 'TC',
    label: 'Turks and Caicos Islands',
    zone: 'c'
  },
  {
    value: 'TV',
    label: 'Tuvalu',
    zone: 'd'
  },
  {
    value: 'UG',
    label: 'Uganda',
    zone: 'd'
  },
  {
    value: 'UA',
    label: 'Ukraine',
    zone: 'b'
  },
  {
    value: 'AE',
    label: 'United Arab Emirates',
    zone: 'c'
  },
  {
    value: 'GB',
    label: 'United Kingdom',
    zone: 'a'
  },
  {
    value: 'UM',
    label: 'United States Minor Outlying Islands',
    zone: 'd'
  },
  {
    value: 'US',
    label: 'United States',
    zone: 'b'
  },
  {
    value: 'UY',
    label: 'Uruguay',
    zone: 'b'
  },
  {
    value: 'UZ',
    label: 'Uzbekistan',
    zone: 'c'
  },
  {
    value: 'VU',
    label: 'Vanuatu',
    zone: 'd'
  },
  {
    value: 'VE',
    label: 'Venezuela, Bolivarian Republic of',
    zone: 'd'
  },
  {
    value: 'VN',
    label: 'Viet Nam',
    zone: 'c'
  },
  {
    value: 'VG',
    label: 'Virgin Islands, British',
    zone: 'c'
  },
  {
    value: 'VI',
    label: 'Virgin Islands, U.S.',
    zone: 'd'
  },
  {
    value: 'WF',
    label: 'Wallis and Futuna',
    zone: 'd'
  },
  {
    value: 'EH',
    label: 'Western Sahara',
    zone: 'd'
  },
  {
    value: 'YE',
    label: 'Yemen',
    zone: 'c'
  },
  {
    value: 'ZM',
    label: 'Zambia',
    zone: 'd'
  },
  {
    value: 'ZW',
    label: 'Zimbabwe',
    zone: 'd'
  }
];
