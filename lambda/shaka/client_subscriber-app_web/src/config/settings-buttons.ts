import {
  ChangeArrowsIcon,
  CoinIcon,
  CustomerSupportIcon,
  HeadPhonesIcon,
  LockIcon,
  PiggyBankIcon
} from 'src/assets/icons';
import { ROUTES } from './routes';
import { TermsIcon } from 'src/assets/icons/Terms';
import { LinkProps } from '@tanstack/react-router';
import { SecurityIcon } from 'src/assets/icons/Security';
import { EsimIcon } from 'src/assets/icons/Esim';
import { isOpenId } from './env-vars';

export const subscriptionLinks = [
  {
    to: ROUTES.Settings,
    label: 'Upgrade plan',
    search: { 'plan-change': 1 }
  },
  {
    id: 'bolt-ons',
    to: ROUTES.Settings,
    label: 'View bolt-ons',
    search: { modal: 'bolt-ons' }
  },
  {
    to: ROUTES.Dashboard,
    label: 'View usage',
    disabled: true
  }
];

export const supportLinks = [
  { to: ROUTES.Support, label: 'Request help', Icon: HeadPhonesIcon }
];

export const subscriberLinks = [
  ...(!isOpenId
    ? [
        {
          to: ROUTES.Subscriber,
          label: 'Change details',
          Icon: CustomerSupportIcon
        }
      ]
    : []),
  {
    to: ROUTES.CardDetails,
    label: 'Change billing',
    Icon: PiggyBankIcon
  }
];

export const otherLinks: {
  to: LinkProps['to'];
  label: string;
  Icon: (props: React.SVGProps<SVGSVGElement>) => JSX.Element;
  blank?: boolean;
  disabled?: boolean;
}[] = [
  {
    to: ROUTES.Terms,
    label: 'Terms and conditions',
    Icon: TermsIcon
  },
  {
    to: ROUTES.Policy,
    label: 'Privacy policy',
    Icon: SecurityIcon
  },
  ...(!isOpenId
    ? [
        {
          to: ROUTES.ChangePassword,
          label: 'Change password',
          Icon: LockIcon
        }
      ]
    : []),
  {
    to: ROUTES.PortNumber,
    label: 'Number porting',
    Icon: ChangeArrowsIcon
  },
  {
    to: ROUTES.EsimSettingInternal,
    label: 'eSIM instructions',
    Icon: EsimIcon
  },
  {
    to: ROUTES.Dashboard,
    label: 'Bill overview',
    disabled: true,
    Icon: CoinIcon
  }
] as const;
