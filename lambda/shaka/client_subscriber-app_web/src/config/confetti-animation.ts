import { TDecorateOptionsFn } from 'react-canvas-confetti/dist/types';

export const getConfettiOptions: (yPosition?: number) => TDecorateOptionsFn =
  (yPosition) => (options) => {
    return {
      ...options,
      particleCount: 120,
      colors: [
        '#ff36ff',
        '#26ccff',
        '#ffa62d',
        '#fcff42',
        '#a25afd',
        '#88ff5a',
        '#ff5e7e'
      ],
      shapes: ['circle', 'square'],
      ticks: 90,
      origin: { ...options.origin, y: yPosition || 0.8 }
    };
  };
