import { PaymentStatus, SessionStatus } from 'src/types/checkout';
import api from '.';
import { StripeResponse } from 'src/types/stripe';
import { Subscriber } from 'src/types/subscriber';

export const getClientSecretSignUp = (
  planId: string
): Promise<StripeResponse> =>
  api.post(`/plans/sign-up/${planId}/`).then((res) => res.data);

export const checkSessionStatus = (
  sessionId: string
): Promise<{
  payment_status: PaymentStatus;
  status: SessionStatus;
  expires_at: number;
}> =>
  api
    .get(`/checkout/session/status/?session_id=${sessionId}`)
    .then((res) => res.data);

export const getClientSecretCard =
  (returnURL?: string) => (): Promise<string> =>
    api
      .post(
        'subscription/change-card/' +
          (returnURL ? `?return_path=${returnURL}&is_query=true` : '')
      )
      .then((res) => res.data.client_secret);

export const portPhoneNumber = ({
  pacCode,
  phoneNumber,
  date
}: {
  phoneNumber: string;
  pacCode: string;
  date: string;
}): Promise<Subscriber> =>
  api
    .post('/subscription/pac-code/', {
      pac_code: pacCode,
      phone_number: phoneNumber,
      desired_date: date
    })
    .then((res) => res.data);
