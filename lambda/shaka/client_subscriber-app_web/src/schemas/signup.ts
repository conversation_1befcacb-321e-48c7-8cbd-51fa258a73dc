import * as yup from "yup";
import {
  dobScheme,
  emailScheme,
  passwordScheme,
  subscriberName,
} from "./common";

export type SignUpFormInputs = {
  fullName: string;
  dob: string;
  email: string;
  password: string;
};

export const schema = yup
  .object()
  .shape(
    {
      fullName: subscriberName,
      dob: dobScheme,
      email: emailScheme,
      password: passwordScheme,
    },
    [["dob", "dob"]]
  )
  .required() as yup.ObjectSchema<SignUpFormInputs>;


