import { config } from './tc-config';

const paragraphClass = 'flex gap-2';
const subParagraphClass = 'flex gap-2 ps-6 mt-1';
const secondLevelSubParagraphClass = 'flex gap-2 ps-12 mt-1';
const blockClass = 'mb-4';
const subInfoClass = 'mt-3';

const Terms = () => {
  return (
    <div className="flex flex-col gap-2 text-sm">
      {config.map(({ title, blocks, paragraphs }, index) => (
        <div key={index}>
          <h4 className="pt-3 font-bold">{title}</h4>
          {blocks && (
            <div className="mt-3">
              {blocks.map(({ number, paragraph, subparagraphs }, index) => (
                <div key={index} className={blockClass}>
                  <div className={paragraphClass}>
                    <span>{number}.</span>
                    <p>{paragraph}</p>
                  </div>
                  {subparagraphs?.map(
                    (
                      {
                        letter,
                        paragraph,
                        subparagraphs: subparagraphsSecondLevel
                      },
                      index
                    ) => (
                      <>
                        <div key={index} className={subParagraphClass}>
                          <span>{letter}.</span>
                          <p>{paragraph}</p>
                        </div>

                        {subparagraphsSecondLevel &&
                          subparagraphsSecondLevel?.map(
                            ({ letter, paragraph }, index) => (
                              <div
                                key={index}
                                className={secondLevelSubParagraphClass}
                              >
                                <span>{letter}.</span>
                                <p>{paragraph}</p>
                              </div>
                            )
                          )}
                      </>
                    )
                  )}
                </div>
              ))}
            </div>
          )}
          {paragraphs?.map((info, index) => (
            <p key={index} className={subInfoClass}>
              {info}
            </p>
          ))}
        </div>
      ))}
    </div>
  );
};

export default Terms;
