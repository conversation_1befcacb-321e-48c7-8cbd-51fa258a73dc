body {
  --primary-color: #1EC25F;
  --secondary-color: #ffffff;
  --gray-color: #1a1a1a;
  --danger-color: #ff6969;
  --font-color: #000000;
  --secondary-font-color: #ffffff;
  --bg-color: #f3f3f3;
}

body {
  background: var(--bg-color);
  color: var(--font-color);
}

.text-primary {
  color: var(--primary-color);
}

.bg-primary {
  background: var(--primary-color);
}

/* Logo */
.yayzi .logo {
  width: 60%;
  margin: 0 auto;
  transition: all 0.75s;
}

.yayzi .logo-wrapper.small .logo {
  width: 40%;
}

/* Initials */
.initials {
  background-color: var(--primary-color);
}

/* Link */
.button.primary,
.link-button.primary {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}

.button.secondary,
.link-button.secondary {
  background: var(--secondary-color);
}

/* Input */
.input {
  background: var(--secondary-color);
}

.input:focus,
.input:focus {
  outline-color: var(--primary-color);
}

/* Select */
.option.selected {
  background: var(--primary-color);
  color: var(--secondary-font-color);
}

.select.focused {
  outline: 2px solid var(--primary-color);
}

/* Input validation mark */
.validation.valid {
  color: var(--primary-color);
}

/* Welcome page */
.yayzi .welcome-content {
  padding: 0 10%;
}

/* Contact phone */
.contact-phone {
  color: var(--primary-color);
}

/* Plan card */
.yayzi .plan-card-detailed {
  color: var(--secondary-font-color);
}

/* Offer  */
.offer-content {
  background: linear-gradient(173.3deg,
      #ffffff -0.66%,
      #eaeaea 11.96%,
      #e1e1e1 106.26%);
  border-radius: 20px;
  margin: 0 -4%;
  margin-top: 36px;
  padding: 5%;
}

.offer {
  background-color: #74cdc9;
  color: var(--secondary-font-color);
  border-radius: 20px;
  padding: 24px 20px 12px;
}

.otp input {
  border-color: var(--primary-color);
}

/* sim activation */
.yayzi .sim-activation-logo {
  max-width: 40%;
}