import { loadStripe, Stripe } from "@stripe/stripe-js";
import { PropsWithChildren, createContext } from "react";

export const StripeContext = createContext<{
  stripePromise: Promise<Stripe | null>;
}>({ stripePromise: new Promise(() => null) });

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PK);

export const StripeProvider = ({ children }: PropsWithChildren) => {
  return (
    <StripeContext.Provider
      value={{
        stripePromise,
      }}
    >
      {children}
    </StripeContext.Provider>
  );
};
