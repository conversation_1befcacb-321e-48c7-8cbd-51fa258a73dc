import React from 'react';
import { twMerge } from 'tailwind-merge';

export function UploadBoxIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 11 11"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <path
        d="M3.10871 9.97097H1.74994C1.56975 9.97097 1.39695 9.89936 1.26954 9.77197C1.14213 9.64459 1.07056 9.47175 1.07056 9.29158V3.85651H9.90255V9.29158C9.90255 9.47175 9.83095 9.64459 9.70356 9.77197C9.57618 9.89936 9.40334 9.97097 9.22317 9.97097H7.8644"
        stroke="currentColor"
        strokeWidth="1.09747"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.78815 7.59333L5.48661 5.89487L7.18507 7.59333"
        stroke="currentColor"
        strokeWidth="1.09747"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.48657 5.89487V9.97117"
        stroke="currentColor"
        strokeWidth="1.09747"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.40115 1.47873C8.34306 1.378 8.26004 1.29388 8.16017 1.2344C8.06023 1.17492 7.94671 1.14209 7.83046 1.13904H3.14271C3.02647 1.14209 2.91296 1.17492 2.81303 1.2344C2.71311 1.29388 2.63013 1.378 2.57203 1.47873L1.07059 3.85658H9.90259L8.40115 1.47873Z"
        stroke="currentColor"
        strokeWidth="1.09747"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.48657 1.13916V3.8567"
        stroke="currentColor"
        strokeWidth="1.09747"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
