export function ChangeArrowsIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 39 39"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_1134_2304)">
        <path
          d="M3.85938 17.6345C3.86175 17.3566 3.99356 10.8016 11.875 10.7968H29.6649L26.2616 14.2002C25.9932 14.4686 25.8269 14.8403 25.8269 15.25C25.8269 16.0705 26.4919 16.7355 27.3125 16.7355C27.7234 16.7355 28.0939 16.5693 28.3634 16.2997L34.3009 10.3622C34.3152 10.348 34.3199 10.3278 34.333 10.3135C34.4506 10.1912 34.5467 10.0487 34.6168 9.89078L34.6204 9.88128V9.87772C34.6809 9.72334 34.7154 9.54522 34.7154 9.35759C34.7154 9.27328 34.7082 9.19134 34.6952 9.11059L34.6964 9.1189C34.6869 9.04884 34.675 8.98828 34.6596 8.9289L34.6619 8.93959C34.6002 8.67478 34.4743 8.44559 34.2998 8.26153L34.3009 8.26272L28.3634 2.32522C28.0951 2.05565 27.7234 1.8894 27.3125 1.8894C26.4919 1.8894 25.8269 2.5544 25.8269 3.37497C25.8269 3.78465 25.9932 4.15634 26.2616 4.42472L29.6661 7.82809H11.8738C3.23594 7.83403 0.913188 14.2335 0.890625 17.6155C0.890625 17.6178 0.890625 17.6214 0.890625 17.625C0.890625 18.442 1.54969 19.1046 2.3655 19.1093H2.375C3.19081 19.1093 3.85344 18.4503 3.85938 17.6345ZM35.6416 20.8942C34.8389 20.9262 34.1929 21.558 34.1418 22.3536V22.3583C34.1299 23.0578 33.8057 29.1995 26.1262 29.2043H8.33625L11.7384 25.8021C12.008 25.5337 12.1742 25.162 12.1742 24.7512C12.1742 23.9306 11.5093 23.2656 10.6887 23.2656C10.279 23.2656 9.90731 23.4318 9.63894 23.7002L3.70144 29.6377C3.68719 29.652 3.68244 29.6722 3.66937 29.6864C3.55062 29.8087 3.45325 29.9524 3.38319 30.1103L3.37962 30.1198V30.1234C3.3345 30.2338 3.30244 30.3609 3.29294 30.4939V30.4987C3.28938 30.5378 3.28819 30.5842 3.28819 30.6293C3.28819 30.7183 3.29413 30.8062 3.30719 30.8917L3.306 30.8822C3.35231 31.2147 3.49481 31.508 3.70381 31.7396L3.70263 31.7384L9.64013 37.6759C9.9085 37.9443 10.2802 38.1105 10.6899 38.1105C11.5104 38.1105 12.1754 37.4455 12.1754 36.625C12.1754 36.2141 12.0092 35.8436 11.7396 35.574L8.33625 32.1718H26.1274C34.7664 32.1647 37.0904 25.7653 37.1118 22.3845C37.1118 22.3821 37.1118 22.3785 37.1118 22.3762C37.1118 21.5627 36.4562 20.9025 35.644 20.8942H35.6416Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1134_2304">
          <rect
            width="39"
            height="39"
            fill="white"
            transform="translate(0 1)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
