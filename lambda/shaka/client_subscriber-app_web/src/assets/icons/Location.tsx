import React from 'react';
import { twMerge } from 'tailwind-merge';

export function LocationIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 10 9"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.53063 3.15959C2.53063 1.80398 3.62703 0.707581 4.98265 0.707581C6.33826 0.707581 7.43466 1.80398 7.43466 3.15959C7.43466 4.9986 4.98265 7.71333 4.98265 7.71333C4.98265 7.71333 2.53063 4.9986 2.53063 3.15959ZM4.10616 3.15883C4.10616 3.64223 4.49849 4.03455 4.98188 4.03455C5.46528 4.03455 5.8576 3.64223 5.8576 3.15883C5.8576 2.67543 5.46528 2.28311 4.98188 2.28311C4.49849 2.28311 4.10616 2.67543 4.10616 3.15883Z"
        fill="#5C5C5C"
      />
    </svg>
  );
}
