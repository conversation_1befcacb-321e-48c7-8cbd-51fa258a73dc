export function CoinIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 39 39"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g opacity="0.5">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M38.4142 15.3462C38.4142 23.3405 31.9336 29.8212 23.9392 29.8212C15.9449 29.8212 9.46416 23.3405 9.46416 15.3462C9.46416 7.3518 15.9449 0.871094 23.9392 0.871094C31.9336 0.871094 38.4142 7.3518 38.4142 15.3462ZM23.939 4.41512C25.0929 4.41512 26.0283 5.35054 26.0283 6.50441V7.3874C27.023 7.62402 27.901 8.16194 28.5581 8.89603C28.981 9.36857 29.3153 9.9251 29.5326 10.5394C29.917 11.6273 29.3468 12.821 28.2589 13.2055C27.1709 13.59 25.9773 13.0198 25.5927 11.9319C25.5606 11.841 25.5103 11.7563 25.4445 11.6828C25.3043 11.5261 25.1071 11.4324 24.8853 11.4324H23.9529L23.939 11.4325L23.9251 11.4324H22.6881C22.4424 11.4324 22.2433 11.6315 22.2433 11.8771C22.2433 12.0861 22.3889 12.2669 22.593 12.3115L25.9385 13.0433C28.2015 13.5384 29.8134 15.5432 29.8134 17.8584C29.8134 20.1859 28.1993 22.1385 26.0283 22.6542V23.5371C26.0283 24.691 25.0929 25.6264 23.939 25.6264C22.7851 25.6264 21.8498 24.691 21.8498 23.5371V22.6543C20.2146 22.266 18.8975 21.0631 18.3457 19.5019C17.9612 18.414 18.5314 17.2204 19.6193 16.8358C20.7073 16.4513 21.9009 17.0215 22.2855 18.1094C22.3893 18.4034 22.6698 18.6089 22.9928 18.6089H24.8853C25.2983 18.6089 25.6348 18.2733 25.6348 17.8584C25.6348 17.5054 25.3889 17.2005 25.0455 17.1254L21.7001 16.3936C19.5776 15.9293 18.0648 14.0498 18.0648 11.8771C18.0648 9.61001 19.6966 7.72402 21.8498 7.32965V6.50441C21.8498 5.35054 22.7851 4.41512 23.939 4.41512ZM23.9392 33.3034C24.8488 33.3034 25.7426 33.236 26.6158 33.1053C23.9605 36.2693 19.9766 38.2806 15.5229 38.2806C7.52856 38.2806 1.04785 31.7999 1.04785 23.8056C1.04785 19.3784 3.03537 15.4154 6.16685 12.7602C6.04506 13.6046 5.98202 14.4681 5.98202 15.3462C5.98202 25.2637 14.0217 33.3034 23.9392 33.3034Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
}
