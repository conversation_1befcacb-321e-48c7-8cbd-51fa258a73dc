export function AwardIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 53 53"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="size-5"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M26.4879 0C15.7743 0 7.08916 8.68508 7.08916 19.3987C7.08916 30.1123 15.7743 38.7973 26.4879 38.7973C37.2015 38.7973 45.8864 30.1123 45.8864 19.3987C45.8864 8.68508 37.2015 0 26.4879 0ZM27.2548 9.17479L29.8392 14.3736C29.897 14.5051 29.9886 14.6189 30.1048 14.7034C30.221 14.7878 30.3575 14.8399 30.5004 14.8544L36.24 15.7259C36.4041 15.7473 36.5588 15.8149 36.6859 15.9209C36.8129 16.0269 36.9072 16.1669 36.9576 16.3245C37.0081 16.4821 37.0126 16.6508 36.9707 16.8109C36.9287 16.971 36.8421 17.1158 36.7209 17.2284L32.4837 21.2553C32.4205 21.3758 32.3875 21.5098 32.3875 21.6459C32.3875 21.782 32.4205 21.916 32.4837 22.0366L33.2951 27.7462C33.3297 27.9113 33.3157 28.0828 33.2547 28.24C33.1938 28.3973 33.0886 28.5334 32.9518 28.6321C32.815 28.7307 32.6526 28.7875 32.4841 28.7956C32.3157 28.8037 32.1485 28.7628 32.0029 28.6778L26.8943 25.9732C26.762 25.9135 26.6186 25.8826 26.4735 25.8826C26.3285 25.8826 26.185 25.9135 26.0528 25.9732L20.9442 28.6778C20.7985 28.7628 20.6314 28.8037 20.4629 28.7956C20.2945 28.7875 20.1321 28.7307 19.9953 28.6321C19.8585 28.5334 19.7533 28.3973 19.6923 28.24C19.6314 28.0828 19.6174 27.9113 19.652 27.7462L20.6136 22.0366C20.655 21.9041 20.6629 21.7634 20.6367 21.627C20.6105 21.4907 20.5509 21.363 20.4634 21.2553L16.2262 17.1984C16.1138 17.0845 16.0349 16.9419 15.9982 16.7862C15.9614 16.6305 15.9683 16.4677 16.018 16.3156C16.0676 16.1636 16.1582 16.0281 16.2797 15.9241C16.4013 15.8201 16.5491 15.7515 16.707 15.7259L22.4467 14.8845C22.5896 14.87 22.7261 14.8179 22.8423 14.7334C22.9584 14.6489 23.0501 14.5352 23.1079 14.4036L25.6922 9.20485C25.7614 9.05771 25.8703 8.93279 26.0067 8.84433C26.1432 8.75583 26.3016 8.7073 26.4642 8.70415C26.6268 8.70105 26.787 8.74345 26.9267 8.82665C27.0664 8.90981 27.1801 9.03042 27.2548 9.17479ZM46.5632 32.7872C43.3441 37.6049 38.4372 41.1987 32.6878 42.7235L38.0344 51.9837C38.4171 52.6469 39.1613 53.0137 39.9203 52.9137C40.6792 52.8138 41.3034 52.2668 41.5014 51.5272L43.4274 44.3402L50.6143 46.2659C51.3536 46.4642 52.1398 46.1973 52.6058 45.5902C53.0718 44.9826 53.1259 44.1544 52.7432 43.4916L46.5632 32.7872ZM20.3059 42.7288C14.5553 41.2093 9.64588 37.6197 6.42301 32.8057L0.253579 43.4916C-0.129181 44.1544 -0.0748945 44.9826 0.391126 45.5902C0.857144 46.1973 1.64313 46.4642 2.38256 46.2659L9.56953 44.3402L11.4953 51.5272C11.6934 52.2668 12.3174 52.8138 13.0764 52.9137C13.8354 53.0137 14.5798 52.6469 14.9626 51.9837L20.3059 42.7288Z"
        fill="currentColor"
      />
    </svg>
  );
}
