import React from 'react';
import { twMerge } from 'tailwind-merge';

export function FingerTapIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 10 11"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <g clipPath="url(#clip0_157_47378)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.47396 3.62209C2.47396 2.51451 3.37184 1.61663 4.47942 1.61663C5.58701 1.61663 6.48488 2.51451 6.48488 3.62209C6.48488 3.77976 6.46678 3.93255 6.43278 4.07873C6.36699 4.36153 6.54292 4.64412 6.82572 4.7099C7.10853 4.77568 7.39111 4.59975 7.45686 4.31696C7.50894 4.09322 7.53635 3.86052 7.53635 3.62209C7.53635 1.93381 6.1677 0.565186 4.47942 0.565186C2.79114 0.565186 1.42252 1.93381 1.42252 3.62209C1.42252 3.86052 1.44992 4.09322 1.50196 4.31696C1.56774 4.59975 1.85032 4.77568 2.13312 4.7099C2.41592 4.64412 2.59185 4.36153 2.52607 4.07873C2.49206 3.93255 2.47396 3.77976 2.47396 3.62209ZM3.61632 7.3618V3.55087C3.61632 3.06332 4.01156 2.66808 4.49911 2.66808C4.98666 2.66808 5.38189 3.06332 5.38189 3.55086V6.40864H7.1089C8.10497 6.40865 8.91248 7.21614 8.91248 8.21223V9.67774C8.91248 10.0649 8.59866 10.3787 8.21152 10.3787H3.57993C3.34921 10.3787 3.13326 10.2651 3.00246 10.0751L2.46693 9.29676C1.97416 8.58059 2.38079 7.59433 3.23511 7.43355L3.61632 7.3618Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_157_47378">
          <rect
            width="9.81352"
            height="9.81352"
            fill="white"
            transform="translate(0.193268 0.565186)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
