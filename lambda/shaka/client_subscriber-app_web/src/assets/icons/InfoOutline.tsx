import React from "react";
import { twMerge } from "tailwind-merge";

export function InfoOutlineIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={twMerge("w-5 h-5", className)}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M32 9.60019C29.0583 9.60019 26.1455 10.1796 23.4278 11.3053C20.7101 12.431 18.2408 14.081 16.1608 16.161C14.0807 18.241 12.4308 20.7104 11.305 23.4281C10.1793 26.1458 9.59995 29.0586 9.59995 32.0002C9.59995 34.9418 10.1793 37.8546 11.305 40.5723C12.4308 43.29 14.0807 45.7593 16.1608 47.8394C18.2408 49.9194 20.7101 51.5694 23.4278 52.6951C26.1455 53.8208 29.0583 54.4002 32 54.4002C37.9408 54.4002 43.6383 52.0402 47.8391 47.8394C52.04 43.6386 54.3999 37.941 54.3999 32.0002C54.3999 26.0593 52.04 20.3618 47.8391 16.161C43.6383 11.9602 37.9408 9.60019 32 9.60019ZM3.19995 32.0002C3.19995 24.362 6.23423 17.0366 11.6353 11.6355C17.0363 6.23447 24.3617 3.2002 32 3.2002C39.6382 3.2002 46.9636 6.23447 52.3646 11.6355C57.7657 17.0366 60.7999 24.362 60.7999 32.0002C60.7999 39.6384 57.7657 46.9638 52.3646 52.3649C46.9636 57.7659 39.6382 60.8002 32 60.8002C24.3617 60.8002 17.0363 57.7659 11.6353 52.3649C6.23423 46.9638 3.19995 39.6384 3.19995 32.0002ZM28.7999 19.2002C28.7999 18.3515 29.1371 17.5376 29.7372 16.9375C30.3373 16.3373 31.1513 16.0002 32 16.0002H32.0319C32.8806 16.0002 33.6946 16.3373 34.2947 16.9375C34.8948 17.5376 35.2319 18.3515 35.2319 19.2002C35.2319 20.0489 34.8948 20.8628 34.2947 21.4629C33.6946 22.0631 32.8806 22.4002 32.0319 22.4002H32C31.1513 22.4002 30.3373 22.0631 29.7372 21.4629C29.1371 20.8628 28.7999 20.0489 28.7999 19.2002ZM28.832 44.8002C28.832 45.6489 29.1691 46.4628 29.7692 47.0629C30.3693 47.6631 31.1833 48.0002 32.0319 48.0002C32.8806 48.0002 33.6946 47.6631 34.2947 47.0629C34.8948 46.4628 35.2319 45.6489 35.2319 44.8002V28.8002C35.2319 27.9515 34.8948 27.1376 34.2947 26.5375C33.6946 25.9373 32.8806 25.6002 32.0319 25.6002C31.1833 25.6002 30.3693 25.9373 29.7692 26.5375C29.1691 27.1376 28.832 27.9515 28.832 28.8002V44.8002Z"
        fill="currentColor"
      />
    </svg>
  );
}
