export function AirPlaneIcon({ ...props }: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="38"
      height="38"
      viewBox="0 0 38 38"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_1_2399)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M32.5166 0.814453C31.2778 0.814453 30.0897 1.30654 29.2138 2.18247L29.2111 2.18527L22.5973 8.87644L8.64885 2.42857C8.07581 2.15314 7.45253 1.99765 6.81722 1.97166C6.18002 1.9456 5.54419 2.05036 4.94903 2.2795C4.35387 2.50864 3.81196 2.85729 3.35675 3.30396C2.90952 3.74275 2.55527 4.26701 2.31501 4.84543C1.78241 5.87292 1.6552 7.06354 1.95943 8.18101C2.26661 9.30931 2.99073 10.2789 3.98538 10.8938L14.275 17.2298L11.064 20.4409H5.10217C5.08339 20.4409 5.06461 20.4416 5.04588 20.4432C3.89236 20.5392 2.81707 21.0653 2.03328 21.9171C1.24949 22.7688 0.814453 23.8841 0.814453 25.0416C0.814453 26.1991 1.24949 27.3144 2.03328 28.1661C2.81707 29.0178 3.89236 29.5439 5.04588 29.64C5.06461 29.6416 5.08339 29.6424 5.10217 29.6424H8.35931V32.8995C8.35931 32.9183 8.3601 32.937 8.36165 32.9557C8.45771 34.1093 8.98376 35.1844 9.83553 35.9683C10.6873 36.7522 11.8025 37.187 12.96 37.187C14.1175 37.187 15.2328 36.7522 16.0845 35.9683C16.9363 35.1844 17.4624 34.1093 17.5584 32.9557C17.56 32.937 17.5607 32.9183 17.5607 32.8995V26.9376L20.7718 23.7266L27.1072 34.0151C27.722 35.0099 28.6924 35.7349 29.8205 36.0421C30.9387 36.3467 32.1303 36.2191 33.1585 35.6855C33.7212 35.4472 34.2312 35.1 34.6598 34.6641C35.0954 34.2206 35.4374 33.6943 35.6657 33.1161C35.8939 32.5377 36.0039 31.9197 35.9887 31.2981C35.9737 30.6768 35.8337 30.0647 35.5775 29.4983L35.5764 29.4956L29.124 15.4055L35.8163 8.79061L35.819 8.78782C36.6949 7.91189 37.187 6.7239 37.187 5.48516C37.187 4.24641 36.6949 3.0584 35.819 2.18247C34.9431 1.30654 33.7551 0.814453 32.5166 0.814453Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1_2399">
          <rect width="38" height="38" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
