import React from 'react';
import { twMerge } from 'tailwind-merge';

export function PhoneCameraIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 11 10"
      className={twMerge('w-6 h-6', className)}
      {...props}
    >
      <path
        d="M3.67529 7.53212H4.35193"
        stroke="black"
        strokeWidth="0.698117"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.01355 0.427444H1.64533C1.27163 0.427444 0.968689 0.730385 0.968689 1.10408V8.54708C0.968689 8.92079 1.27163 9.22372 1.64533 9.22372H6.38178C6.75547 9.22372 7.05842 8.92079 7.05842 8.54708V6.17885"
        stroke="black"
        strokeWidth="0.698117"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.76502 2.11899C9.76502 1.93953 9.6937 1.76743 9.56683 1.64053C9.43996 1.51364 9.26783 1.44235 9.08838 1.44235H8.75006L8.07343 0.427399H6.72016L6.04352 1.44235H5.7052C5.52574 1.44235 5.35364 1.51364 5.22674 1.64053C5.09985 1.76743 5.02856 1.93953 5.02856 2.11899V3.81058C5.02856 3.99004 5.09985 4.16214 5.22674 4.28904C5.35364 4.41593 5.52574 4.48722 5.7052 4.48722H9.08838C9.26783 4.48722 9.43996 4.41593 9.56683 4.28904C9.6937 4.16214 9.76502 3.99004 9.76502 3.81058V2.11899Z"
        stroke="black"
        strokeWidth="0.698117"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.39681 3.04947C7.25667 3.04947 7.14307 2.93587 7.14307 2.79573C7.14307 2.65559 7.25667 2.54199 7.39681 2.54199"
        stroke="black"
        strokeWidth="0.698117"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.39703 3.04952C7.53717 3.04952 7.65077 2.93591 7.65077 2.79578C7.65077 2.65564 7.53717 2.54204 7.39703 2.54204"
        stroke="black"
        strokeWidth="0.698117"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
