import React from 'react';
import { twMerge } from 'tailwind-merge';

export function InformationDeskIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 67 67"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <g clipPath="url(#clip0_1136_10499)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M29.1478 13.0898C33.945 16.2461 37.1117 21.6782 37.1117 27.85C37.1117 30.5345 36.5126 33.0791 35.4407 35.3573C38.7163 37.8849 42.8259 39.3908 47.2839 39.3908C57.9981 39.3908 66.6837 30.7053 66.6837 19.9911C66.6837 9.27688 57.9981 0.591309 47.2839 0.591309C39.0003 0.591309 31.9299 5.78245 29.1478 13.0898ZM47.2886 18.1902C48.9406 18.1902 50.2797 19.5293 50.2797 21.1812V29.7733C50.2797 31.4252 48.9401 32.7644 47.2885 32.7643C45.6365 32.7643 44.2974 31.4251 44.2974 29.7732L44.2975 21.1812C44.2975 19.5293 45.6367 18.1901 47.2886 18.1902ZM43.7793 10.6275C43.7793 12.8846 45.6473 13.875 46.962 14.0186C47.0725 14.0309 47.1848 14.0373 47.2986 14.0373C48.6032 14.0373 50.809 13.0804 50.809 10.6276C50.809 8.37046 48.9411 7.38011 47.6262 7.23654C47.5157 7.22419 47.4034 7.21782 47.2896 7.21782C45.985 7.21782 43.7793 8.17473 43.7793 10.6275ZM19.4559 39.5236C25.9031 39.5236 31.1295 34.2971 31.1295 27.85C31.1295 21.4029 25.9031 16.1764 19.4559 16.1764C13.0088 16.1764 7.78238 21.4029 7.78238 27.85C7.78238 34.2971 13.0088 39.5236 19.4559 39.5236ZM0 62.8705C0 52.1252 8.71072 43.4147 19.4559 43.4147C29.1036 43.4147 37.1112 50.4368 38.6464 59.6488H63.3255C65.3078 59.6488 66.9148 61.2558 66.9148 63.2381C66.9148 65.2203 65.3078 66.8273 63.3255 66.8273H3.681C1.78315 66.8273 0.229322 65.3543 0.100373 63.4893C0.0352709 63.295 0 63.0868 0 62.8705Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_1136_10499">
          <rect width="67" height="67" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
