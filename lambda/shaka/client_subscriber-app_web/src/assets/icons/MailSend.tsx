import React from 'react';
import { twMerge } from 'tailwind-merge';

export function MailSendIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 67 67"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M66.2994 0.70092C66.968 1.36958 67.18 2.36931 66.8407 3.25191L42.912 65.4661C42.5658 66.3658 41.7135 66.9703 40.7497 66.999C39.786 67.0277 38.8992 66.475 38.5002 65.5973L28.4935 43.5825L43.2166 28.8594C44.6183 27.4577 44.6183 25.1851 43.2166 23.7834C41.8149 22.3817 39.5423 22.3817 38.1406 23.7834L23.4174 38.5065L1.40269 28.4999C0.52495 28.1009 -0.0275935 27.2141 0.00106361 26.2503C0.0297209 25.2866 0.633983 24.4342 1.53388 24.0881L63.7482 0.159563C64.6306 -0.179898 65.6304 0.0322594 66.2994 0.70092Z"
        fill="black"
      />
    </svg>
  );
}
