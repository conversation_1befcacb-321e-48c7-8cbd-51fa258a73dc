import React from 'react';
import { twMerge } from 'tailwind-merge';

export interface ClockProps extends React.SVGProps<SVGSVGElement> {
  className?: string;
}

export function ClockIcon({ className, ...props }: ClockProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 10 11"
      className={twMerge('w-5 h-5', className)}
      {...props}
    >
      <path
        d="M4.99945 3.28119V5.86899L6.68152 6.71002"
        stroke="#1EC25F"
        strokeWidth="1.02153"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.0921 6.51621C8.65398 8.37082 6.98789 9.75096 4.99943 9.75096C2.67697 9.75096 0.79425 7.86827 0.79425 5.54578C0.79425 3.22333 2.67697 1.34061 4.99943 1.34061C6.6218 1.34061 8.02961 2.25935 8.7309 3.60493"
        stroke="#1EC25F"
        strokeWidth="1.02153"
        strokeLinecap="round"
      />
      <path
        d="M9.20459 2.31091V3.92829H7.58722"
        stroke="#1EC25F"
        strokeWidth="1.02153"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
