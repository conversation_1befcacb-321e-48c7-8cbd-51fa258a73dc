import React from "react";

export function CustomerSupportIcon({
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 39 39"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_1125_1988)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.38254 4.38254C4.51316 4.25195 4.69031 4.17857 4.875 4.17857H9.05357C10.2074 4.17857 11.1429 3.24316 11.1429 2.08929C11.1429 0.935404 10.2074 0 9.05357 0H4.875C3.58207 0 2.34209 0.513616 1.42785 1.42785C0.513616 2.34209 0 3.58207 0 4.875V9.05357C0 10.2074 0.935404 11.1429 2.08929 11.1429C3.24316 11.1429 4.17857 10.2074 4.17857 9.05357V4.875C4.17857 4.69031 4.25195 4.51316 4.38254 4.38254ZM39 29.9464C39 28.7926 38.0646 27.8571 36.9107 27.8571C35.7569 27.8571 34.8214 28.7926 34.8214 29.9464V34.125C34.8214 34.3097 34.7482 34.4869 34.6175 34.6175C34.4869 34.7482 34.3097 34.8214 34.125 34.8214H29.9464C28.7926 34.8214 27.8571 35.7569 27.8571 36.9107C27.8571 38.0646 28.7926 39 29.9464 39H34.125C35.4179 39 36.6578 38.4863 37.572 37.572C38.4863 36.6578 39 35.4179 39 34.125V29.9464ZM2.08929 27.8571C3.24316 27.8571 4.17857 28.7926 4.17857 29.9464V34.125C4.17857 34.3097 4.25195 34.4869 4.38254 34.6175C4.51316 34.7482 4.69031 34.8214 4.875 34.8214H9.05357C10.2074 34.8214 11.1429 35.7569 11.1429 36.9107C11.1429 38.0646 10.2074 39 9.05357 39H4.875C3.58207 39 2.34209 38.4863 1.42785 37.572C0.513616 36.6578 0 35.4179 0 34.125V29.9464C0 28.7926 0.935404 27.8571 2.08929 27.8571ZM29.9464 0C28.7926 0 27.8571 0.935404 27.8571 2.08929C27.8571 3.24316 28.7926 4.17857 29.9464 4.17857H34.125C34.3097 4.17857 34.4869 4.25195 34.6175 4.38254C34.7482 4.51316 34.8214 4.69031 34.8214 4.875V9.05357C34.8214 10.2074 35.7569 11.1429 36.9107 11.1429C38.0646 11.1429 39 10.2074 39 9.05357V4.875C39 3.58207 38.4863 2.34209 37.572 1.42785C36.6578 0.513616 35.4179 0 34.125 0H29.9464ZM19.4974 21.663C14.2071 21.663 9.69593 24.9985 7.95204 29.6812C7.64183 30.5142 8.30566 31.3398 9.19461 31.3398H29.8002C30.6891 31.3398 31.3529 30.5142 31.0426 29.6812C29.2988 24.9985 24.7876 21.663 19.4974 21.663ZM25.6511 12.4258C25.6511 15.8268 22.8941 18.5838 19.4931 18.5838C16.0922 18.5838 13.3351 15.8268 13.3351 12.4258C13.3351 9.02488 16.0922 6.26786 19.4931 6.26786C22.8941 6.26786 25.6511 9.02488 25.6511 12.4258Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_1125_1988">
          <rect width="39" height="39" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
