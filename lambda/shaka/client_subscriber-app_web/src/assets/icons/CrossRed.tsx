import React from 'react';
import { twMerge } from 'tailwind-merge';

export function CrossRedIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 16 17"
      className={twMerge('w-6 h-6', className)}
      {...props}
    >
      <path
        d="M15.6525 8.24225C15.6525 12.5507 12.1597 16.0433 7.85118 16.0433C3.54262 16.0433 0.0498352 12.5507 0.0498352 8.24225C0.0498352 3.93384 3.54262 0.441177 7.85118 0.441177C12.1597 0.441177 15.6525 3.93384 15.6525 8.24225Z"
        fill="#E03E8C"
      />
      <g clipPath="url(#clip0_914_248)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.78145 5.5667C5.61408 5.39934 5.34273 5.39934 5.17536 5.5667C5.00799 5.73407 5.00799 6.00543 5.17536 6.1728L7.44375 8.44118L5.17536 10.7096C5.00799 10.8769 5.00799 11.1483 5.17536 11.3156C5.34273 11.483 5.61408 11.483 5.78145 11.3156L8.04984 9.04727L10.3182 11.3156C10.4856 11.483 10.7569 11.483 10.9243 11.3156C11.0917 11.1483 11.0917 10.8769 10.9243 10.7096L8.65593 8.44118L10.9243 6.1728C11.0917 6.00543 11.0917 5.73407 10.9243 5.5667C10.7569 5.39934 10.4856 5.39934 10.3182 5.5667L8.04984 7.83509L5.78145 5.5667Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_914_248">
          <rect
            width="6"
            height="6"
            fill="white"
            transform="translate(5.04984 5.44118)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
