import React from 'react';
import { twMerge } from 'tailwind-merge';

export function CopyIcon({
  className,
  ...props
}: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      width="54"
      height="60"
      viewBox="0 0 54 60"
      className={twMerge('w-6 h-6', className)}
      {...props}
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M35.3077 1.26475e-06H24.0746C18.9854 -5.45492e-05 14.9544 -8.26287e-05 11.7996 0.427369C8.55294 0.867238 5.92508 1.79408 3.85267 3.88253C1.78028 5.97101 0.860568 8.61924 0.424081 11.8911C-8.16629e-05 15.0703 -5.41297e-05 19.1325 1.25489e-06 24.2611V41.1628C1.25489e-06 46.3875 3.79953 50.7181 8.76603 51.5015C9.1481 53.6336 9.87932 55.4534 11.3493 56.935C13.0161 58.6147 15.1149 59.3372 17.6075 59.6749C20.0083 60 23.0608 60 26.848 60H35.4597C39.2469 60 42.2994 60 44.7004 59.6749C47.193 59.3372 49.2915 58.6147 50.9586 56.935C52.6254 55.255 53.3423 53.1402 53.6774 50.6283C54 48.2087 54 45.1326 54 41.316V27.0561C54 23.2396 54 20.1634 53.6774 17.744C53.3423 15.232 52.6254 13.117 50.9586 11.4373C49.4884 9.9559 47.6826 9.21902 45.5669 8.83398C44.7895 3.82898 40.4922 1.26475e-06 35.3077 1.26475e-06ZM41.2042 8.43117C40.3657 5.96157 38.0426 4.18605 35.3077 4.18605H24.2308C18.9503 4.18605 15.199 4.19049 12.3531 4.57608C9.56703 4.95355 7.96184 5.66146 6.78988 6.84251C5.61791 8.02356 4.91544 9.64119 4.54088 12.4489C4.15825 15.3168 4.15385 19.0972 4.15385 24.4186V41.1628C4.15385 43.9189 5.91574 46.26 8.36632 47.105C8.30764 45.403 8.30767 43.4782 8.30769 41.316V27.0561C8.30764 23.2396 8.30758 20.1634 8.63036 17.744C8.9655 15.232 9.68242 13.117 11.3493 11.4373C13.0161 9.75748 15.1149 9.035 17.6075 8.69727C20.0083 8.37198 23.0608 8.37204 26.848 8.37209H35.4597C37.6053 8.37207 39.5153 8.37204 41.2042 8.43117ZM14.2865 14.3972C15.0529 13.6249 16.129 13.1213 18.1609 12.846C20.2527 12.5626 23.025 12.5581 27 12.5581H35.3077C39.2826 12.5581 42.0549 12.5626 44.1468 12.846C46.1789 13.1213 47.2547 13.6249 48.0212 14.3972C48.7878 15.1696 49.2873 16.254 49.5606 18.3017C49.8417 20.4097 49.8462 23.2035 49.8462 27.2093V41.1628C49.8462 45.1686 49.8417 47.9623 49.5606 50.0704C49.2873 52.1182 48.7878 53.2024 48.0212 53.9749C47.2547 54.7473 46.1789 55.2508 44.1468 55.5262C42.0549 55.8095 39.2826 55.8139 35.3077 55.8139H27C23.025 55.8139 20.2527 55.8095 18.1609 55.5262C16.129 55.2508 15.0529 54.7473 14.2865 53.9749C13.5201 53.2024 13.0204 52.1182 12.7472 50.0704C12.4659 47.9623 12.4615 45.1686 12.4615 41.1628V27.2093C12.4615 23.2035 12.4659 20.4097 12.7472 18.3017C13.0204 16.254 13.5201 15.1696 14.2865 14.3972Z"
        fill="#65CDCA"
      />
    </svg>
  );
}
