export function CheckRoundedGreenIcon(
  props: React.SVGProps<SVGSVGElement> & {
    circleColor?: string;
  }
) {
  return (
    <svg
      viewBox="0 0 17 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="size-4"
      {...props}
    >
      <path
        d="M16.3414 7.80108C16.3414 12.1095 12.8486 15.6022 8.54008 15.6022C4.23152 15.6022 0.738739 12.1095 0.738739 7.80108C0.738739 3.49266 4.23152 0 8.54008 0C12.8486 0 16.3414 3.49266 16.3414 7.80108Z"
        fill="#1EC25F"
      />
      <path
        d="M11.1925 5.10438L7.62621 10.4542L5.84305 8.84924"
        fill="#1EC25F"
      />
      <path
        d="M11.1925 5.10438L7.62621 10.4542L5.84305 8.84924"
        stroke="white"
        strokeWidth="1.09544"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
