export function TermsIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      fill="#000000"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 298 298"
      {...props}
    >
      <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
      <g
        id="SVGRepo_tracerCarrier"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></g>
      <g id="SVGRepo_iconCarrier">
        <g>
          <rect x="66.5" y="67" width="98" height="16"></rect>
          <polygon points="200.574,70.429 191.993,61.31 180.341,72.274 201.32,94.571 231.189,58.611 218.881,48.389 "></polygon>
          <rect x="66.5" y="117" width="98" height="16"></rect>
          <polygon points="200.574,120.429 191.993,111.31 180.341,122.274 201.32,144.571 231.189,108.611 218.881,98.389 "></polygon>
          <rect x="66.5" y="167" width="98" height="16"></rect>
          <polygon points="200.574,170.429 191.993,161.31 180.341,172.274 201.32,194.571 231.189,158.611 218.881,148.389 "></polygon>
          <rect x="66.5" y="217" width="98" height="16"></rect>
          <polygon points="200.574,220.429 191.993,211.31 180.341,222.274 201.32,244.571 231.189,208.611 218.881,198.389 "></polygon>
          <path d="M264.5,23c0-12.703-10.297-23-23-23h-185c-12.703,0-23,10.297-23,23v252c0,12.703,10.297,23,23,23h185 c12.703,0,23-10.297,23-23V23z M248.5,275c0,3.866-3.134,7-7,7h-185c-3.866,0-7-3.134-7-7V23c0-3.866,3.134-7,7-7h185 c3.866,0,7,3.134,7,7V275z"></path>
        </g>
      </g>
    </svg>
  );
}
