import React from 'react';
import { twMerge } from 'tailwind-merge';

interface QRCodeIconProps extends React.SVGProps<SVGSVGElement> {
  primaryStroke?: string;
  secondaryStroke?: string;
}

export function QRCodeIcon({
  className,
  primaryStroke = 'black',
  secondaryStroke = '#E03E8C',
  ...props
}: QRCodeIconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 90 90"
      stroke="currentColor"
      className={twMerge('w-6 h-6', className)}
      {...props}
    >
      <path
        d="M38.9767 22.0107H27.6641C24.5403 22.0107 22.0078 24.5432 22.0078 27.667V38.9797C22.0078 42.1035 24.5403 44.636 27.6641 44.636H38.9767C42.1006 44.636 44.633 42.1035 44.633 38.9797V27.667C44.633 24.5432 42.1006 22.0107 38.9767 22.0107Z"
        stroke={primaryStroke}
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.1408 4H9.6563C8.15614 4 6.71746 4.59593 5.65669 5.65669C4.59593 6.71746 4 8.15614 4 9.6563V18.1408"
        stroke={secondaryStroke}
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M71.1328 4H79.6173C81.1173 4 82.5563 4.59593 83.6168 5.65669C84.6774 6.71746 85.2736 8.15614 85.2736 9.6563V18.1408"
        stroke={secondaryStroke}
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M18.1408 85.2716H9.6563C8.15614 85.2716 6.71746 84.6754 5.65669 83.6149C4.59593 82.5543 4 81.1154 4 79.6153V71.1309"
        stroke={secondaryStroke}
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M71.1328 85.2716H79.6173C81.1173 85.2716 82.5563 84.6754 83.6168 83.6149C84.6774 82.5543 85.2736 81.1154 85.2736 79.6153V71.1309"
        stroke={secondaryStroke}
        strokeWidth="8"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22.0078 58.7764V67.2608H30.4923"
        stroke={primaryStroke}
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M44.6368 67.2608V58.7764H36.1523"
        stroke={primaryStroke}
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M67.2657 30.4952H58.7812V22.0107"
        stroke={primaryStroke}
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M67.2657 50.2926V41.8081H58.7812"
        stroke={primaryStroke}
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M58.7812 58.7764V67.2608H67.2657"
        stroke={primaryStroke}
        strokeWidth="6"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
