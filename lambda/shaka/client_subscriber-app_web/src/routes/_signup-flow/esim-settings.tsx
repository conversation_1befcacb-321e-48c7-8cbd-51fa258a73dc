import { createFileRoute } from '@tanstack/react-router';
import { useEffect, useRef, useState } from 'react';
import { RightArrowIcon } from 'src/assets/icons';
import Button from 'src/components/common/Button';
import ProgressLinks from 'src/components/common/ProgressLinks';
import { EsimSettings as EsimSettingsContent } from 'src/components/esim-settings';
import { ROUTES } from 'src/config/routes';

export const Route = createFileRoute('/_signup-flow/esim-settings')({
  component: EsimSettings
});

function EsimSettings() {
  const [scrolled, setScrolled] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  const scrollPage = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({
        behavior: 'smooth',
        top: scrollRef.current.scrollHeight
      });
      setScrolled(true);
    }
  };

  const handleScroll = () => {
    const element = scrollRef.current;

    if (
      element &&
      element.scrollTop + element.clientHeight >= element.scrollHeight
    ) {
      setScrolled(true);
    }
  };

  useEffect(() => {
    const element = scrollRef.current;

    element?.addEventListener('scroll', handleScroll);

    return () => {
      element?.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const handleWheel = (event: WheelEvent) => {
      const element = scrollRef.current;
      if (!element) return;

      if (element.scrollHeight > element.clientHeight) {
        element.scrollTop += event.deltaY;
        event.preventDefault();
      }
    };

    document.addEventListener('wheel', handleWheel, { passive: false });

    return () => {
      document.removeEventListener('wheel', handleWheel);
    };
  }, []);

  return (
    <>
      <div
        className="esim-settings-content"
        ref={scrollRef}
        onWheel={handleScroll}
      >
        <EsimSettingsContent />
      </div>

      {!scrolled && (
        <div className="bottom-0 right-0 fixed md:right-max-nav p-6 pr-5">
          <Button
            type="submit"
            color="secondary"
            rightIcon={<RightArrowIcon className="rotate-90" />}
            onClick={scrollPage}
            narrow
            fullWidth
            transparentBg
          >
            Complete all steps
          </Button>
        </div>
      )}
      <ProgressLinks nextTo={ROUTES.FinishSignUp} hideNextButton={!scrolled} />
    </>
  );
}
