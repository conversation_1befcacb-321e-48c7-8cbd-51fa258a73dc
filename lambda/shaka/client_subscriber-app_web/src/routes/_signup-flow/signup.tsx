import { yupResolver } from '@hookform/resolvers/yup';
import { createFileRoute, Navigate, useNavigate } from '@tanstack/react-router';
import { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { signup } from 'src/api/auth';
import { Calendar } from 'src/components/common/CalendarInput';
import ErrorText from 'src/components/common/ErrorText';
import Input from 'src/components/common/Input';
import Link from 'src/components/common/Link';
import ProgressLinks from 'src/components/common/ProgressLinks';
import { isOpenId } from 'src/config/env-vars';
import {
  deleteAllStorageExceptKey,
  getAwaitingPlanId,
  LocalKey
} from 'src/config/localStorageActions';
import { ROUTES } from 'src/config/routes';
import { passwordValidation } from 'src/helpers';
import { useAuth } from 'src/hooks/useAuth';
import { useRequest } from 'src/hooks/useRequest';
import { schema, SignUpFormInputs } from 'src/schemas/signup';

export const Route = createFileRoute('/_signup-flow/signup')({
  component: SignUpForm
});

function SignUpForm() {
  const {
    clientProps: { isSignupDisabled }
  } = Route.useRouteContext();

  const auth = useAuth();
  const navigate = useNavigate();
  const { run: runSignup, error } = useRequest(signup);

  const isExistingEmailError =
    error === 'Failed to sign up - email already in use';

  const [isPasswordValidationVisible, setIsPasswordValidationVisible] =
    useState(false);
  const [isNextLoading, setIsNextLoading] = useState(false);

  const {
    control,
    register,
    handleSubmit,
    trigger,
    watch,
    formState: { errors, isValid, dirtyFields, isDirty }
  } = useForm<SignUpFormInputs>({
    resolver: yupResolver(schema),
    mode: 'all'
  });

  const canSubmit = isDirty && isValid;
  const password = watch('password');

  const onSubmit = (data: SignUpFormInputs) => {
    const planId = getAwaitingPlanId() || '';
    setIsNextLoading(true);

    runSignup({
      email: data.email,
      password: data.password,
      name: data.fullName,
      date_of_birth: data.dob,
      plan_id: planId
    })
      .then((data) => {
        const {
          IdToken: id_token,
          AccessToken: access_token,
          RefreshToken: refresh_token
        } = data;
        deleteAllStorageExceptKey(LocalKey.PLAN_ID);
        auth.login({
          id_token,
          access_token,
          refresh_token
        });

        navigate({ to: ROUTES.EmailConfirmation });
      })
      .finally(() => {
        setIsNextLoading(false);
      });
  };

  if (isOpenId) {
    return <Navigate to={ROUTES.OpenIdLogin} />;
  }

  if (isSignupDisabled) {
    return <Navigate to={ROUTES.Login} replace={true} />;
  }

  return (
    <div className="signup-content">
      <form className="mt-5 space-y-6 pb-8" onSubmit={handleSubmit(onSubmit)}>
        <div className="relative">
          <Input
            placeholder="Full Name"
            {...register('fullName')}
            required
            withValidationMark
            error={errors?.fullName?.message}
            dirty={dirtyFields.fullName}
          />
        </div>

        <div>
          <p className="text-gray-500 text-xs mb-1">Date of birth</p>
          <div className="relative">
            <Controller
              control={control}
              name="dob"
              render={({ field: { onChange, value, onBlur } }) => (
                <Calendar
                  value={value}
                  onChange={(val) => {
                    onChange(val);
                    trigger('dob');
                    onBlur();
                  }}
                  error={errors?.dob?.message}
                  hideErrorMessage={errors?.dob?.message === 'hidden'}
                  withValidationMark
                  dirty={dirtyFields.dob}
                />
              )}
            />
          </div>
        </div>

        <div className="relative">
          <Input
            placeholder="Email"
            error={errors?.email?.message}
            {...register('email')}
            required
            withValidationMark
            dirty={dirtyFields.email}
          />
        </div>

        <div className="relative">
          <Input
            placeholder="Password"
            type="password"
            {...register('password')}
            error={isPasswordValidationVisible ? errors?.password?.message : ''}
            onBlur={() => setIsPasswordValidationVisible(true)}
            description={
              <p className="flex flex-col gap-1">
                {passwordValidation.map(({ label, isValid }) => (
                  <span
                    key={label}
                    className={
                      isValid(password) ? 'text-green-500' : 'text-gray-500'
                    }
                  >
                    {label}
                    <br />
                  </span>
                ))}
              </p>
            }
            withValidationMark
            dirty={dirtyFields.password}
          />
        </div>

        {error && (
          <div>
            <ErrorText>{error}</ErrorText>
            {isExistingEmailError && (
              <div className="text-sm">
                Did you mean to <Link to={ROUTES.Login}>log in</Link>?
              </div>
            )}
          </div>
        )}

        <ProgressLinks
          disabledNext={!canSubmit || isNextLoading}
          backTo={ROUTES.ExplorePlans}
          isLoading={isNextLoading}
        />
      </form>
    </div>
  );
}
