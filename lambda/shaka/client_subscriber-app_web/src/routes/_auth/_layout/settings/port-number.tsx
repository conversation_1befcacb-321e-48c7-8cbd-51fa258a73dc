import { createFileRoute } from '@tanstack/react-router';
import { ROUTES } from 'src/config/routes';
import { useForm } from 'react-hook-form';
import Input from 'src/components/common/Input';
import Button from 'src/components/common/Button';
import { useMemo, useState } from 'react';
import Link from 'src/components/common/Link';
import { yupResolver } from '@hookform/resolvers/yup';
import { PortNumberFormInputs, schema } from 'src/schemas/port-number';
import { portPhoneNumber } from 'src/api/stripe';
import useSubscription from 'src/hooks/useSubscription';
import PageTitle from 'src/components/common/PageTitle';
import ErrorText from 'src/components/common/ErrorText';
import { PacCodeStatus } from 'src/types/subscriber';
import { OneInputCalendar } from 'src/components/common/CalendarInput';
import { formateToShortDateString } from 'src/helpers';
import { isWeekendOrHoliday } from 'src/helpers/date';

const PortNumber = () => {
  const { setSubscriber, subscriberLastPlan } = useSubscription();
  const isPacCodePending =
    subscriberLastPlan.pac_code_status === PacCodeStatus.PENDING;
  const isPacCodeInProgress =
    subscriberLastPlan.pac_code_status === PacCodeStatus.IN_PROGRESS;
  const isPacCodeFailed =
    subscriberLastPlan.pac_code_status === PacCodeStatus.ERRORED;

  const isSimActive = subscriberLastPlan.sim_activation_status === 'active';

  const {
    control,
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<PortNumberFormInputs>({
    resolver: yupResolver(schema)
  });
  const [error, setError] = useState<string | null>(null);

  const [isRequestSent, setIsRequestSent] = useState(false);

  const onSubmit = (data: PortNumberFormInputs) => {
    portPhoneNumber({
      ...data,
      date: formateToShortDateString(data.date)
    })
      .then((newSubscriberData) => {
        setIsRequestSent(true);
        setSubscriber(newSubscriberData);
      })
      .catch((error) => {
        setError(error.response?.data.error || 'Something went wrong');
      });
  };

  const isTileDisabled = useMemo(
    () =>
      ({ date, view }: { date: Date; view: string }) => {
        if (view === 'month') {
          const isToday = date.toDateString() === new Date().toDateString();
          return isWeekendOrHoliday(date) || isToday;
        }
        return false;
      },
    []
  );

  if (isPacCodeFailed) {
    return (
      <div className="space-y-4">
        <PageTitle>Something went wrong</PageTitle>
        <p>There was a problem porting your number, please contact support</p>

        <Link to={ROUTES.Support} styleType="button">
          Contact support
        </Link>
        <Link to={ROUTES.Dashboard} styleType="button" color="secondary">
          Home
        </Link>
      </div>
    );
  }

  if (isPacCodeInProgress) {
    return (
      <div className="space-y-4">
        <PageTitle>Port in progress</PageTitle>

        <p>
          We have successfully received your request and your number porting
          will happen automatically.
        </p>

        <Link to={ROUTES.Dashboard} styleType="button">
          Home
        </Link>
      </div>
    );
  }

  if (isRequestSent || isPacCodePending) {
    return (
      <div className="space-y-4">
        <PageTitle>Request received</PageTitle>
        {isSimActive ? (
          <p>
            We have successfully received your request and your number porting
            will happen automatically.
          </p>
        ) : (
          <p>
            Your number porting request will happen automatically when the SIM
            is activated.
          </p>
        )}

        <Link to={ROUTES.Dashboard} styleType="button">
          Home
        </Link>
      </div>
    );
  }

  return (
    <div>
      <PageTitle>Number porting</PageTitle>
      <p>
        If you want to keep your existing number, speak to your current network
        provider and request your PAC code. This code is valid for 30 days.
      </p>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="mt-5 space-y-4 mb-16">
          <Input
            placeholder="Phone number to port"
            error={errors?.phoneNumber?.message}
            {...register('phoneNumber')}
          />

          <Input
            placeholder="PAC code"
            error={errors?.pacCode?.message}
            {...register('pacCode')}
          />

          <OneInputCalendar
            label="Porting date (optional)"
            error={errors?.date?.message}
            minDate={new Date()}
            control={control}
            name="date"
            tileDisabled={isTileDisabled}
          />
        </div>

        <ErrorText>{error}</ErrorText>

        <Button type="submit">Submit</Button>
      </form>
    </div>
  );
};

export const Route = createFileRoute('/_auth/_layout/settings/port-number')({
  component: PortNumber
});
