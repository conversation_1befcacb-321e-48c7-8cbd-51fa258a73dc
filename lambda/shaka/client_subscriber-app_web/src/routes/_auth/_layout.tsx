import {
  Outlet,
  createFileRoute,
  useRouterState
} from '@tanstack/react-router';
import Header from 'src/components/common/Header';
import Navigation from 'src/components/Navigation';
import Container from 'src/components/common/Container';
import { ROUTES } from 'src/config/routes';
import useSubscriptionRedirect from 'src/hooks/useSubscriptionRedirect';
import 'react-toastify/dist/ReactToastify.css';
import ScrollToTop from 'src/components/ScrollToTop';

export const Route = createFileRoute('/_auth/_layout')({
  component: LayoutComponent
});

function LayoutComponent() {
  useSubscriptionRedirect();
  const { location } = useRouterState();
  const pathname = location.pathname;
  const isCheckout = pathname.startsWith(ROUTES.Checkout);

  return (
    <Container>
      {!isCheckout && <Header />}
      <Outlet />
      <ScrollToTop />
      {!isCheckout && <Navigation />}
    </Container>
  );
}
