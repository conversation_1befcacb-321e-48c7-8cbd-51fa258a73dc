import { useState } from 'react';
import { createFileRoute, useNavigate } from '@tanstack/react-router';
import { useForm } from 'react-hook-form';
import { sendForgotPassword } from 'src/api/auth';
import { ROUTES } from 'src/config/routes';
import Container from 'src/components/common/Container';
import PageTitle from 'src/components/common/PageTitle';
import ErrorText from 'src/components/common/ErrorText';
import Input from 'src/components/common/Input';
import Button from 'src/components/common/Button';
import Link from 'src/components/common/Link';

type ForgotPasswordInputs = {
  email: string;
};

function ForgotPassword() {
  const navigate = useNavigate();
  const { register, handleSubmit } = useForm<ForgotPasswordInputs>();

  const [error, setError] = useState<string | null>(null);

  const onSubmit = (data: ForgotPasswordInputs) => {
    sendForgotPassword(data)
      .then(() => {
        navigate({
          to: ROUTES.ForgotConfirmPassword,
          search: { email: data.email }
        });
      })
      .catch((error) => {
        setError(error.response?.data.error || 'Something went wrong');
      });
  };
  return (
    <Container>
      <PageTitle>Forgot password?</PageTitle>
      <p>
        No worries, we’ll send you a magic link, just enter the email you used
        to sign up with below:
      </p>

      <form className="space-y-6 mt-10" onSubmit={handleSubmit(onSubmit)}>
        <Input {...register('email')} placeholder="Email" />

        <div className="space-y-2">
          <ErrorText>{error}</ErrorText>
          <Button type="submit">Send link</Button>
          <Link styleType="button" to={ROUTES.Login} color="secondary">
            Return
          </Link>
        </div>
      </form>
    </Container>
  );
}

export const Route = createFileRoute('/forgot-password')({
  component: ForgotPassword
});
