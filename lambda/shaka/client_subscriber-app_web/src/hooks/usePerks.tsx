import { useRequest } from './useRequest';
import {
  usePerksStore,
  selectorPerksLoaded,
  setPerks,
  selectorPerks,
  selectorPointsBalance
} from 'src/store/perks';
import { useEffect, useMemo } from 'react';
import { fetchPerks } from 'src/api/perks';
import { Perk } from 'src/types/perks';
import { claimDetails } from 'src/components/perks/helpers';

export default function usePerks() {
  const perks = usePerksStore(selectorPerks);
  const pointsBalance = usePerksStore(selectorPointsBalance);
  const isPerksLoaded = usePerksStore(selectorPerksLoaded);
  const featuredPerks = useMemo(
    () =>
      perks.filter((perk) => perk.featured && perk.enabled && !perk.isClaimed),
    [perks]
  );

  const activePerks = useMemo(() => {
    const { available, claimed } = perks.reduce(
      (acc, perk) => {
        const { canBeClaimed } = claimDetails(
          perk,
          pointsBalance >= Number(perk.amount)
        );

        if (
          !perk.enabled ||
          new Date(perk.availabilityDate).getTime() > Date.now()
        ) {
          return acc;
        }

        return canBeClaimed
          ? {
              ...acc,
              available: [...acc.available, perk]
            }
          : {
              ...acc,
              claimed: [...acc.claimed, perk]
            };
      },
      {
        available: [] as Perk[],
        claimed: [] as Perk[]
      }
    );

    const sortedAvailable = available.sort((a, b) => {
      return b.progress - a.progress;
    });

    return [...sortedAvailable, ...claimed];
  }, [perks]);

  const activePerksAmount = useMemo(
    () =>
      activePerks.filter(
        ({ isClaimed, multiplyRedeemable, remaining }) =>
          !(isClaimed && (!multiplyRedeemable || remaining < 1))
      ).length,
    [activePerks]
  );

  const claimedPerks = useMemo(() => {
    const { available, claimed } = perks.reduce(
      (acc, perk) => {
        const { canBeClaimedSeveralTimes } = claimDetails(
          perk,
          pointsBalance >= Number(perk.amount)
        );

        if (!perk.isClaimed) {
          return acc;
        }

        return !perk.isClaimed || canBeClaimedSeveralTimes
          ? {
              ...acc,
              available: [...acc.available, perk]
            }
          : {
              ...acc,
              claimed: [...acc.claimed, perk]
            };
      },
      {
        available: [] as Perk[],
        claimed: [] as Perk[]
      }
    );

    return [...available, ...claimed];
  }, [perks]);

  const { run: doFetchPerks, isLoading } = useRequest(fetchPerks);

  useEffect(() => {
    if (!isPerksLoaded) {
      doFetchPerks().then((data) => {
        setPerks(data);
      });
    }
  }, []);

  return {
    isLoading: isLoading || !isPerksLoaded,
    loadPerks: doFetchPerks,
    featuredPerks,
    activePerks,
    claimedPerks,
    pointsBalance,
    activePerksAmount,
    perks,
    setPerks
  };
}
