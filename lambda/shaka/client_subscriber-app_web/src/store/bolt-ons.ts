import { create } from 'zustand';
import { RoamingBoltOn } from 'src/types/boltons';

type State = { boltOns: RoamingBoltOn[]; isLoaded: boolean };

const defaultState: State = {
  boltOns: [],
  isLoaded: false
};

const useBoltOnsStore = create<State>(() => defaultState);

const selectorBoltOns = (state: State) => state.boltOns;
const selectorBoltOnsLoaded = (state: State) => state.isLoaded;

const setBoltOns = (data: RoamingBoltOn[]) =>
  useBoltOnsStore.setState(() => ({ boltOns: data, isLoaded: true }));

const resetBoltOns = () => useBoltOnsStore.setState(defaultState);

export {
  useBoltOnsStore,
  selectorBoltOns,
  selectorBoltOnsLoaded,
  setBoltOns,
  resetBoltOns
};
