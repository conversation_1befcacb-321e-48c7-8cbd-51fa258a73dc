import { Tab } from '@headlessui/react';
import { PropsWithChildren } from 'react';
import { Fragment } from 'react/jsx-runtime';
import { twMerge } from 'tailwind-merge';

export default function TabTitle({
  children,
  last
}: PropsWithChildren<{
  last?: boolean;
}>) {
  return (
    <>
      <Tab as={Fragment}>
        {({ selected }) => (
          <button
            className={twMerge(
              'px-6 py-3.5 font-semibold rounded-t-xl outline-none relative',
              selected ? 'bg-white z-10' : 'bg-[#EBEBEB] text-gray-500'
            )}
          >
            {children}
            <span
              className={twMerge(
                'bg-white w-4 absolute inset-y-0 -right-4 pointer-events-none',
                selected ? 'translate-x-0' : '-translate-x-full',
                !selected && last && 'opacity-0'
              )}
            />
            <span
              className={twMerge(
                'bg-[#EBEBEB] rounded-bl-xl w-4 absolute inset-y-0 -right-4 pointer-events-none',
                selected ? 'translate-x-0' : '-translate-x-full -scale-x-[1]',
                selected && last && 'bg-general',
                !selected && last && 'opacity-0'
              )}
            />
          </button>
        )}
      </Tab>
    </>
  );
}
