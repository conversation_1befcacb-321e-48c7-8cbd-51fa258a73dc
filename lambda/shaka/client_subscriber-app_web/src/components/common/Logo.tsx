import { useState } from "react";
import { Link } from "@tanstack/react-router";
import clsx from "clsx";
import { ROUTES } from "src/config/routes";

const Logo = ({
  className,
  disableRedirect,
}: {
  className?: string;
  disableRedirect?: boolean;
}) => {
  const [isError, setIsError] = useState(false);
  const logoElement = (
    <img src="/logo.png" alt="logo" onError={() => setIsError(true)} />
  );

  if (isError) return <div className="text-center">Your Logo</div>;

  return disableRedirect ? (
    <div className={clsx("logo", className)}>{logoElement}</div>
  ) : (
    <Link to={ROUTES.Home} className={clsx("block logo", className)}>
      {logoElement}
    </Link>
  );
};

export default Logo;
