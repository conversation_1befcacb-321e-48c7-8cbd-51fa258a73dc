import { CloseIcon } from "src/assets/icons";
import { twMerge } from "tailwind-merge";

export default function CloseButton({
  onClose,
  isLight,
}: {
  onClose: () => void;
  isLight?: boolean;
}) {
  return (
    <button
      type="button"
      className={twMerge(
        "relative rounded-full bg-gray-200 p-1.5 hover:bg-gray-300 focus-visible:outline-none",
        isLight && "bg-white/20"
      )}
      onClick={onClose}
    >
      <span className="sr-only">Close</span>
      <CloseIcon
        className={twMerge("size-5 text-gray-500", isLight && "text-white")}
      />
    </button>
  );
}
