import { CheckRoundedFilledIcon } from "src/assets/icons/CheckRoundedFilled";
import { twMerge } from "tailwind-merge";

interface Props {
  error: string | undefined;
  dirty?: boolean;
}

export default function ValidationMark({ error, dirty }: Props) {
  return (
    <span
      className={twMerge(
        "transition",
        !dirty || error ? "text-gray-300" : "validation valid"
      )}
    >
      <CheckRoundedFilledIcon />
    </span>
  );
}
