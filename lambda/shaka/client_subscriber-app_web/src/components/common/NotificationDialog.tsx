import { BlockedIcon } from 'src/assets/icons/Blocked';
import Dialog, { ButtonActionColor, DialogActionButton } from './Dialog';
import { QuestionIcon } from 'src/assets/icons/Question';
import { InfoOutlineIcon } from 'src/assets/icons/InfoOutline';

const getDialogIcon = (dialogType: 'warning' | 'question' | 'info') => {
  switch (dialogType) {
    case 'warning':
      return <BlockedIcon className="size-6" />;
    case 'question':
      return <QuestionIcon className="size-6" />;
    case 'info':
      return <InfoOutlineIcon className="size-6" />;
    default:
      return null;
  }
};

interface Props {
  isOpen: boolean;
  onCancel: () => void;
  onSubmit?: () => void;
  onClose?: () => void;
  title?: string;
  description?: React.ReactNode;
  cancelButtonText?: string;
  submitButtonText?: string;
  cancelActionColor?: ButtonActionColor;
  submitActionColor?: ButtonActionColor;
  dialogType?: 'warning' | 'question' | 'info';
}

export default function NotificationDialog({
  isOpen,
  onCancel,
  onSubmit,
  onClose,
  description,
  title,
  cancelActionColor = 'success',
  submitActionColor = 'danger',
  cancelButtonText = 'No',
  submitButtonText = 'Yes',
  dialogType = 'warning'
}: Props) {
  return (
    <Dialog
      title={
        <span className="inline-block rounded-full bg-gray-100 p-2">
          {getDialogIcon(dialogType)}
        </span>
      }
      isOpen={isOpen}
      onClose={onClose || onCancel}
      leftButton={
        onSubmit && (
          <DialogActionButton
            className="w-full"
            actionColor={submitActionColor}
            onClick={onSubmit}
          >
            {submitButtonText}
          </DialogActionButton>
        )
      }
      rightButton={
        <DialogActionButton
          className="w-full"
          onClick={onCancel}
          actionColor={cancelActionColor}
        >
          {cancelButtonText}
        </DialogActionButton>
      }
      hideCloseButton
      panelStyles="w-4/5 px-5 sm:max-w-sm"
    >
      <h3 className="text-lg font-semibold text-black  text-center">{title}</h3>
      <div className="mt-4">{description}</div>
    </Dialog>
  );
}
