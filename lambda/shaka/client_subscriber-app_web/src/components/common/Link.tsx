import {
  Link as TanLink,
  LinkProps as TanLinkProps
} from '@tanstack/react-router';
import clsx from 'clsx';
import { PropsWithChildren } from 'react';
import { RightArrowIcon } from 'src/assets/icons';

type LinkProps = {
  color?: 'primary' | 'secondary' | 'black';
  styleType?: 'button' | 'link';
  narrow?: boolean;
  withArrow?: boolean;
  size?: 'small' | 'medium' | 'large';
} & TanLinkProps;

const Link = ({
  styleType = 'link',
  color = 'primary',
  children,
  withArrow,
  narrow,
  size,
  ...rest
}: PropsWithChildren<LinkProps>) => {
  const typeStyles =
    styleType === 'button' ? clsx('link-button', color) : 'link';

  return (
    <TanLink
      className={clsx(
        typeStyles,
        'relative text-base',
        narrow && 'narrow',
        size
      )}
      {...rest}
    >
      {children}
      {withArrow && (
        <span className="link-arrow absolute right-4 top-[14px]">
          <RightArrowIcon />
        </span>
      )}
    </TanLink>
  );
};

export default Link;
