import { Menu } from '@headlessui/react';
import { ChevronDownIcon } from 'src/assets/icons/ChevronDown';
import { twMerge } from 'tailwind-merge';

interface Props {
  wrapperStyles?: string;
  value: string;
  placeholder?: string;
  options: {
    label: string;
    value: string;
  }[];
  onSelect: (value: string) => void;
  disabled?: boolean;
}

function Dropdown({
  value,
  options,
  wrapperStyles,
  disabled,
  placeholder,
  onSelect
}: Props) {
  return (
    <div className={twMerge('relative', wrapperStyles)}>
      <Menu>
        <Menu.Button
          className={twMerge('dropdown-button', disabled && 'disabled')}
        >
          {value || placeholder}
          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-4">
            <ChevronDownIcon className="size-3" />
          </span>
        </Menu.Button>

        <Menu.Items className="absolute left-0 mt-1 w-full rounded-md bg-white shadow-lg focus:outline-none max-h-[200px] overflow-y-auto z-10">
          <div className="px-1 py-1 ">
            {options.map((option) => (
              <Menu.Item key={option.value}>
                {({ active }) => (
                  <button
                    type="button"
                    onClick={() => onSelect(option.value)}
                    className={twMerge(
                      active && 'active',
                      'dropdown-item block w-full rounded-md px-2 py-2 text-sm whitespace-nowrap text-left'
                    )}
                  >
                    {option.label}
                  </button>
                )}
              </Menu.Item>
            ))}
          </div>
        </Menu.Items>
      </Menu>
    </div>
  );
}

export default Dropdown;
