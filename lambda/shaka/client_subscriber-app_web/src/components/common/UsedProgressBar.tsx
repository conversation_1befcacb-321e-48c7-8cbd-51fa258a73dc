import { twMerge } from "tailwind-merge";

interface Props {
  used: number;
  limit: number;
  color?: "black" | "white";
  size?: "small" | "medium" | "large";
}

export default function UsedProgressBar({
  limit,
  used,
  color = "white",
  size = "medium",
}: Props) {
  return (
    <div className={twMerge("relative h-2", size === "small" && "h-1.5")}>
      <span className="block size-full rounded-xl bg-black opacity-10" />
      <span
        className={twMerge(
          "h-full rounded-xl absolute top-0 left-0",
          color === "black" ? "bg-black" : "bg-white"
        )}
        style={{
          width: `${(used * 100) / limit}%`,
        }}
      />
    </div>
  );
}
