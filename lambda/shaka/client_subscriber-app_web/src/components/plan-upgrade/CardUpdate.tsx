import { useContext } from "react";
import { getClientSecretCard } from "src/api/stripe";
import { StripeContext } from "src/context/StripeContext";
import {
  EmbeddedCheckout,
  EmbeddedCheckoutProvider,
} from "@stripe/react-stripe-js";

export default function CardUpdate({ redirectUrl }: { redirectUrl: string }) {
  const { stripePromise } = useContext(StripeContext);

  return (
    <div>
      <EmbeddedCheckoutProvider
        stripe={stripePromise}
        options={{
          fetchClientSecret: getClientSecretCard(redirectUrl),
        }}
      >
        <EmbeddedCheckout />
      </EmbeddedCheckoutProvider>
    </div>
  );
}
