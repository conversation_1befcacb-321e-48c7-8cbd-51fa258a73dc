import clsx from 'clsx';
import { AirPlaneIcon } from 'src/assets/icons/AirPlane';
import { ChatIcon } from 'src/assets/icons/Chat';
import { CloudsIcon } from 'src/assets/icons/Clouds';
import {
  currencyRounded,
  getSmsVoiceLabel,
  getRoamingLabel,
  getBgIndex
} from 'src/helpers';
import { ClientPlan } from 'src/types/plans';

interface Props {
  onClick: () => void;
  selected: boolean;
}

export default function PlanPreview({
  id,
  bg,
  data,
  price,
  sms,
  voice,
  title,
  onClick,
  selected
}: ClientPlan & Props) {
  const bgIndex = getBgIndex(id, bg);

  return (
    <div
      id={'plan-preview-' + id}
      className={clsx(
        'bg-white rounded-[2rem] p-3 border-2 cursor-pointer',
        selected ? 'border-black' : 'border-transparent',
        !selected && 'hover:border-gray-200'
      )}
      onClick={onClick}
    >
      <div
        className="relative rounded-[2rem] flex items-center w-full py-5 px-6 h-[150px] bg-white bg-cover text-white"
        style={{ backgroundImage: `url(/background/card-${bgIndex}.png)` }}
      >
        <span className="card-top absolute top-3 right-6 text-xl font-bold text-black/20">
          5G
        </span>

        <div className="grow flex items-center">
          <h3 className="text-2xl font-bold tracking-tighter card-text">
            {title}
          </h3>
        </div>
      </div>

      <div className="flex gap-2 justify-between items-center px-5 mt-4 mb-2">
        <div className="space-y-2 text-xs">
          <div className="flex gap-2">
            <CloudsIcon className="size-3.5 -mt-0.5" />
            <span>
              {data === 'unlimited' ? 'Unlimited data' : `${data} GB`}
            </span>
          </div>

          <div className="flex gap-2 items-center">
            <ChatIcon className="size-3" />
            {getSmsVoiceLabel(sms, voice)}
          </div>

          <div className="flex gap-2">
            <AirPlaneIcon className="size-3" />
            <span>{getRoamingLabel(data)}</span>
          </div>
        </div>
        <div className="flex flex-col">
          <p className="text-2xl font-bold">{currencyRounded(Number(price))}</p>
          <span className="block text-[0.625rem] -mt-0.5">per month</span>
        </div>
      </div>
    </div>
  );
}
