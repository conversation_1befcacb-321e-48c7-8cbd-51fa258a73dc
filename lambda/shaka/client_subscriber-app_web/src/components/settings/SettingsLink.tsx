import { Link, LinkProps } from "@tanstack/react-router";
import clsx from "clsx";
import { PropsWithChildren } from "react";

export const settingsLinkStyles =
  "px-4 py-3.5 text-xs flex items-center gap-4 hover:bg-gray-100 font-bold";

interface Props {
  to: LinkProps["to"];
  disabled?: boolean;
  blank?: boolean;
}

const SettingLink = ({
  to,
  children,
  disabled,
  blank,
}: PropsWithChildren<Props>) => (
  <Link
    to={to}
    className={clsx(
      settingsLinkStyles,
      disabled && "opacity-50 pointer-events-none"
    )}
    {...(blank && { target: "_blank" })}
  >
    {children}
  </Link>
);

export default SettingLink;
