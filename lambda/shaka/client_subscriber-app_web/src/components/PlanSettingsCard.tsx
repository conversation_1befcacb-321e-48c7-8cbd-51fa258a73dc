import { useContext, useMemo, useState } from 'react';
import { ClientContext } from 'src/context/ClientContext';
import useSubscription from 'src/hooks/useSubscription';
import PlanStatus from './common/PlanStatus';
import Chip from './common/Chip';
import {
  PlanChangeStatus,
  PlanChangeType,
  Subscriber
} from 'src/types/subscriber';
import {
  currencyRounded,
  epochToDate,
  getBgIndex,
  humanReadableDateFormat
} from 'src/helpers';
import BigActionButton from './common/BigActionButton';
import { InfoIcon } from 'src/assets/icons/Info';
import { cancelSubscriptionUpdate } from 'src/api/subscription';
import NotificationDialog from './common/NotificationDialog';
import Logo from './common/Logo';
import { twMerge } from 'tailwind-merge';

const isPlainCard = window.clientConfig.plainDashboardCard;

const getSubscriptionStatus = (subscriber: Subscriber | null) => {
  if (!subscriber) return '';

  const {
    sim_activation_status: simStatus,
    subscription_status: subscriptionStatus,
    latest_plan_change: latestPlanChange
  } = subscriber.plans?.[0] || {};

  if (subscriptionStatus === 'active') {
    switch (simStatus) {
      case 'not-assigned':
        return 'Your plan is being set up';
      case 'pending-activation':
        return 'Your SIM is pending activation';
      default:
        break;
    }
  }

  if (
    latestPlanChange &&
    latestPlanChange.change_type === PlanChangeType.UPGRADE &&
    latestPlanChange.status === PlanChangeStatus.IN_PROGRESS
  ) {
    return 'Your plan is being upgraded, we will let you know when it is ready!';
  }

  switch (subscriptionStatus) {
    case 'inactive':
      return 'Your subscription is inactive. Please wait for activation';
    case 'cancelled':
      return 'Your subscription is cancelled';
    default:
      return '';
  }
};

export default function PlanSettingsCard() {
  const { subscriber, fetchSubscriber } = useSubscription();
  const { getPlanById } = useContext(ClientContext);

  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const plan = subscriber?.plans?.[0] || ({} as Subscriber['plans'][0]);

  const {
    id,
    title,
    data,
    voice,
    sms,
    price,
    plan_id,
    bg,
    can_cancel_change: canCancelPlanChange,
    next_bill_date_epoch: billDate,
    latest_plan_change: latestPlanChange,
    sim_type,
    next_bill_amount: nexBillAmount
  } = plan;
  const withDiscount = price !== nexBillAmount;
  const bgIndex = getBgIndex(plan_id || id, bg);
  const status = getSubscriptionStatus(subscriber);
  const simType = sim_type === 'esim' ? 'eSIM' : 'SIM';

  const isPlanCancelled =
    latestPlanChange &&
    latestPlanChange.change_type === PlanChangeType.CANCELLATION;
  const isPlanChanged =
    latestPlanChange &&
    latestPlanChange.change_type !== PlanChangeType.CANCELLATION;

  const planInfo = useMemo(() => {
    if (plan.plan_id) {
      return getPlanById(plan.plan_id);
    }

    return null;
  }, [getPlanById, subscriber?.plans]);

  const closeDialog = () => {
    setIsDialogOpen(false);
  };

  const cancelChange = () => {
    cancelSubscriptionUpdate(latestPlanChange!.id).then(() => {
      fetchSubscriber();
      closeDialog();
    });
  };

  const handleCancelPlanChange = () => {
    if (latestPlanChange) {
      cancelChange();
    }
  };

  const handleCancelPlanCancelation = () => {
    if (
      latestPlanChange &&
      latestPlanChange.change_type === PlanChangeType.CANCELLATION
    ) {
      cancelChange();
    }
  };

  return (
    <>
      <div className="plan-card-wrapper relative">
        <div
          className="plan-card-detailed flex flex-col size-full text-base py-6 px-6 justify-between absolute inset-0"
          style={{ backgroundImage: `url(/background/card-${bgIndex}.png)` }}
        >
          {isPlainCard ? (
            <div className="w-full flex justify-between text-black font-bold text-xl">
              <div className="plan-card-logo">
                <Logo disableRedirect />
              </div>
              <span className="opacity-20">{simType}</span>
            </div>
          ) : (
            <div className="card-top text-xl font-bold text-black/20">
              {simType}
            </div>
          )}
          <div className="flex justify-between gap-4">
            <h3
              className={twMerge(
                'plan-card-title card-text grow text-2xl mb-4 font-bold',
                isPlainCard ? 'capitalize' : 'uppercase'
              )}
            >
              {title}
            </h3>
            {price && (
              <div className="text-right">
                <p className="font-bold text-2xl relative card-text">
                  {withDiscount ? (
                    <>
                      <span className="text-sm line-through mr-2 font-semibold">
                        {currencyRounded(Number(price))}
                      </span>
                      <span>{currencyRounded(Number(nexBillAmount))}</span>
                      <span className="absolute -top-6 right-0 text-xs font-semibold bg-black/10 rounded-full py-0.5 px-3 ">
                        % offer
                      </span>
                    </>
                  ) : (
                    currencyRounded(Number(price))
                  )}
                </p>
                {billDate &&
                  (isPlanCancelled ? (
                    <p className="text-xs whitespace-nowrap card-text">
                      cancelled {epochToDate(billDate)}
                    </p>
                  ) : (
                    <p className="text-xs whitespace-nowrap card-text">
                      next on {epochToDate(billDate)}
                    </p>
                  ))}
              </div>
            )}
          </div>
          <div className="flex justify-between items-end">
            {status ? (
              <PlanStatus>{status}</PlanStatus>
            ) : (
              planInfo && (
                <div className="flex flex-wrap gap-1.5">
                  <Chip>
                    {sms?.is_unlimited
                      ? 'Unlimited SMS'
                      : `${planInfo.sms} SMS`}
                  </Chip>
                  <Chip>
                    {data?.is_unlimited
                      ? 'Unlimited data'
                      : `${planInfo.data}GB data`}
                  </Chip>
                  <Chip>
                    {voice?.is_unlimited
                      ? 'Unlimited calls'
                      : `${planInfo.voice} mins`}
                  </Chip>
                  <Chip>EU roaming</Chip>
                  <Chip>5G</Chip>
                </div>
              )
            )}
          </div>
        </div>
      </div>

      {billDate && isPlanCancelled && canCancelPlanChange && (
        <div className="flex gap-2 mt-5">
          <div className="grow bg-white rounded-2xl py-3 px-5 font-semibold text-xs flex items-center gap-3">
            <InfoIcon />
            <div className="flex flex-col justify-center gap-0.5">
              <p>Your plan is due to terminate</p>
              <p>on the {humanReadableDateFormat(new Date(billDate * 1000))}</p>
            </div>
          </div>

          <BigActionButton
            actionType="cancel"
            cancelText="Cancel"
            onClick={() => setIsDialogOpen(true)}
          />

          <NotificationDialog
            isOpen={isDialogOpen}
            onCancel={handleCancelPlanCancelation}
            onSubmit={closeDialog}
            cancelButtonText="Yes"
            submitButtonText="No"
            title="Stop plan termination?"
            description="If you stop the termination, you will continue on your previous plan."
          />
        </div>
      )}

      {billDate && isPlanChanged && canCancelPlanChange && (
        <div className="flex gap-2 mt-5 mb-6">
          <div className="grow bg-white rounded-2xl py-3 px-5 font-semibold text-xs flex items-center gap-3">
            <InfoIcon />
            <div className="flex flex-col justify-center gap-0.5">
              <p>Your plan is changing</p>
              <p>on the {humanReadableDateFormat(new Date(billDate * 1000))}</p>
            </div>
          </div>

          <BigActionButton
            actionType="cancel"
            cancelText="Cancel"
            onClick={() => setIsDialogOpen(true)}
          />

          <NotificationDialog
            isOpen={isDialogOpen}
            onCancel={closeDialog}
            onSubmit={handleCancelPlanChange}
            title="Are you sure you want to cancel this plan change?"
            description="In case of cancellation, you will continue on your previous plan."
          />
        </div>
      )}
    </>
  );
}
