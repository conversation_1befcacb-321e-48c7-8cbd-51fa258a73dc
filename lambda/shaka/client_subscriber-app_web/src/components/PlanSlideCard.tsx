import {
  currencyRounded,
  getSmsVoiceLabel,
  getRoamingLabel,
  getBgIndex
} from 'src/helpers';
import { ClientPlanDetails } from 'src/types/slide';
import { CloudsIcon } from 'src/assets/icons/Clouds';
import { ChatIcon } from 'src/assets/icons/Chat';
import { AirPlaneIcon } from 'src/assets/icons/AirPlane';

const PlanCard = ({ clientPlan = {} as ClientPlanDetails['clientPlan'] }) => {
  const { title, price, data, sms, voice, bg, current_price } = clientPlan;
  const bgIndex = getBgIndex(bg);
  const isOffer = current_price && current_price !== price;

  return (
    <div
      className="plan-card-detailed flex flex-col size-full py-5 px-6 pb-7 h-[220px] bg-white bg-cover"
      style={{ backgroundImage: `url(/background/card-${bgIndex}.png)` }}
    >
      <div className="card-top text-2xl flex justify-between font-bold text-black/20">
        <span />
        <span className="network">5G</span>
      </div>

      <div className="grow flex items-center">
        <h3 className="text-xl font-bold card-text plan-card-title">{title}</h3>
      </div>

      <div className="flex gap-2 justify-between items-end">
        <div className="plan-feature space-y-2 font-semibold text-xs">
          <div className="flex gap-2">
            <CloudsIcon className="card-icons size-4 -mt-0.5" />
            <span className="card-text">
              {data === 'unlimited' ? 'Unlimited data' : `${data} GB`}
            </span>
          </div>

          <div className="flex gap-2 items-center">
            <ChatIcon className="card-icons size-3.5 ml-0.5" />
            <span className="card-text">{getSmsVoiceLabel(sms, voice)}</span>
          </div>

          <div className="flex gap-2">
            <AirPlaneIcon className="card-icons size-3.5 ml-0.5" />
            <span className="card-text">{getRoamingLabel(data)}</span>
          </div>
        </div>
        <div className="relative text-3xl font-bold card-text">
          {isOffer ? (
            <>
              <span className="text-lg line-through mr-2 font-semibold">
                {currencyRounded(Number(price))}
              </span>
              <span>{currencyRounded(Number(current_price))}</span>
              <span className="absolute -top-6 right-0 text-xs font-semibold bg-black/10 rounded-full py-0.5 px-3 ">
                % offer
              </span>
            </>
          ) : (
            currencyRounded(Number(price))
          )}
        </div>
      </div>
    </div>
  );
};

export default PlanCard;
