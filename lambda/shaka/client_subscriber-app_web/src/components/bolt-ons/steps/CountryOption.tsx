import { Combobox } from '@headlessui/react';
import CountryIcon from 'src/components/common/CountryIcon';
import { BoltOnCountry } from 'src/types/boltons';
import { twMerge } from 'tailwind-merge';

export default function CountryOption({
  country,
  Icon
}: {
  country: BoltOnCountry;
  Icon: React.ElementType;
}) {
  return (
    <Combobox.Option
      key={country.value}
      className="py-2 cursor-pointer"
      value={country}
    >
      {({ selected, active }) => (
        <div
          className={twMerge(
            'py-3 px-3 rounded-lg font-semibold text-black flex justify-between items-center',
            active && 'bg-[#F3F3F3]',
            !selected && 'hover:bg-[#FAFAFA]'
          )}
        >
          {country.label}

          <CountryIcon Icon={Icon} title={country.label} />
        </div>
      )}
    </Combobox.Option>
  );
}
