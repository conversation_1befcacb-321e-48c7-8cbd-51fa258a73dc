import { useNavigate } from '@tanstack/react-router';
import { useState } from 'react';
import BoltonPayment from './BoltOnPayment';
import FullPageLoader from '../common/FullPageLoader';
import { RoamingBoltOn, BoltOnCountry } from 'src/types/boltons';
import { ROUTES } from 'src/config/routes';
import withDrawer from 'src/hocs/withDrawer';
import useBoltOns from 'src/hooks/useBoltOns';
import CountrySelection from './steps/CountrySelection';
import BoltOnConfirmation from './steps/BoltOnConfirmation';
import ActiveBolton from './ActiveBolton';
import useSubscription from 'src/hooks/useSubscription';

const BoltOnsContent = ({
  navPosition
}: {
  navPosition: 'fixed' | 'absolute';
}) => {
  const navigate = useNavigate();
  const { boltOns, buyBoltOn, isLoading } = useBoltOns();
  const { subscriberLastPlan } = useSubscription();

  const [step, setStep] = useState(0);
  const [selectedBoltOn, setSelectedBoltOn] = useState<RoamingBoltOn>();
  const [selectedCountry, setSelectedCountry] = useState<BoltOnCountry>();

  const [clientSecret, setClientSecret] = useState<string | null>(null);

  const subscriberBoltOn = subscriberLastPlan.roaming_bolt_on;

  const handleBoltOnSelect = (country?: BoltOnCountry) => {
    const selected =
      country && boltOns.find((boltOn) => boltOn.zone === country.zone);

    setSelectedCountry(country);

    if (selected) {
      setSelectedBoltOn(selected);
    }
  };

  const handleConfirmAndPay = (isNewPaymentMethod: boolean) => {
    if (!selectedBoltOn) return;

    buyBoltOn({
      id: selectedBoltOn.id,
      isNew: isNewPaymentMethod,
      country_code: selectedCountry?.value
    }).then(({ client_secret }) => {
      if (client_secret) {
        setClientSecret(client_secret);
        return;
      }

      navigate({ to: ROUTES.Settings, search: { modal: 'bolt-ons-payment' } });
    });
  };

  const returnToMainScreen = () => {
    setClientSecret(null);
  };

  const setNextStep = () => {
    setStep((currentStep) => currentStep + 1);
  };

  if (isLoading) {
    return <FullPageLoader />;
  }

  if (subscriberBoltOn) {
    return (
      <>
        <div className="text-center mb-6">
          <span className="bg-white rounded-full text-xs py-1 px-8">
            Travel
          </span>
        </div>
        <div className="px-5 text-black min-h-[70vh]">
          <ActiveBolton {...subscriberBoltOn} />
        </div>
      </>
    );
  }

  return (
    <>
      <div className="text-center mb-6">
        <span className="bg-white rounded-full text-xs py-1 px-8">Travel</span>
      </div>
      <div className="px-5 text-black min-h-[70vh]">
        {clientSecret ? (
          <BoltonPayment
            onReturn={returnToMainScreen}
            clientSecret={clientSecret}
          />
        ) : (
          <>
            {step === 0 && (
              <CountrySelection
                onSelect={handleBoltOnSelect}
                onNextClick={setNextStep}
                selectedBoltOn={selectedBoltOn}
                selectedCountry={selectedCountry}
                navPosition={navPosition}
                setClientSecret={setClientSecret}
              />
            )}
            {step === 1 && (
              <BoltOnConfirmation
                onNextClick={handleConfirmAndPay}
                onBackClick={() => setStep(0)}
                selectedBoltOn={selectedBoltOn}
                selectedCountry={selectedCountry}
                navPosition={navPosition}
              />
            )}
          </>
        )}
      </div>
    </>
  );
};

const BoltOnsDrawer = withDrawer(BoltOnsContent);

export default BoltOnsDrawer;
