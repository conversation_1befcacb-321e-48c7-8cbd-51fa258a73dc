import { useForm } from "react-hook-form";
import Input from "./common/Input";
import Button from "./common/Button";
import { yupResolver } from "@hookform/resolvers/yup";
import { LoginFormInputs, schema } from "../schemas/login";
import { login } from "src/api/auth";
import { LoginData } from "src/types/login";
import { ROUTES } from "src/config/routes";
import Link from "./common/Link";
import ErrorText from "./common/ErrorText";
import { useRequest } from "src/hooks/useRequest";
import { RightArrowIcon } from "src/assets/icons";

interface LoginFormProps {
  onSubmit: (data: LoginData) => void;
  disabledSignup?: boolean;
}

export const LoginForm = ({ onSubmit, disabledSignup }: LoginFormProps) => {
  const { run: runLogin, error } = useRequest(login, {
    defaultErrorText: "Invalid email or password",
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormInputs>({
    resolver: yupResolver(schema),
  });

  const onFormSubmit = (data: LoginFormInputs) => {
    runLogin(data).then((res) => {
      onSubmit(res);
    });
  };

  return (
    <>
      <form className="space-y-6 mt-10" onSubmit={handleSubmit(onFormSubmit)}>
        <Input
          placeholder="Email address"
          {...register("email")}
          error={errors.email?.message}
        />
        <Input
          {...register("password")}
          placeholder="Password"
          type="password"
          error={errors.password?.message}
        />

        <div>
          <ErrorText>{error}</ErrorText>
          <Button
            type="submit"
            rightIcon={
              <span className="absolute right-4 top-[14px]">
                <RightArrowIcon />
              </span>
            }
          >
            Log in
          </Button>

          {disabledSignup && (
            <div className="text-center mt-3">
              <div className="text-center mt-10 text-black/40 text-sm">
                The Yayzi Mobile Beta <br /> is now closed for new users
              </div>
            </div>
          )}

          <div className="text-center mt-[15vh]">
            <Link
              to={ROUTES.ForgotPassword}
              className="text-sm text-[#858585] underline font-semibold"
            >
              Forgot password?
            </Link>
          </div>
        </div>
      </form>
    </>
  );
};
