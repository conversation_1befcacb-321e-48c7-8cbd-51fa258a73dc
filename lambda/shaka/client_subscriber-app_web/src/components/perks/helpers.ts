import { PointsNaming } from 'src/types/client';
import { Perk } from 'src/types/perks';

const convertMonthToYears = (months: number) => {
  if (months < 12) return `${Math.ceil(months)} months`;

  const years = Math.floor(months / 12);

  return years === 1 ? `1 year` : `${(months / 12).toFixed(1)} years`;
};

export const getCostTypeLabel = (
  type: Perk['eligibilityType'],
  cost: number,
  pointsNaming: PointsNaming
) => {
  if (type === 'tenure') {
    return convertMonthToYears(cost);
  }

  if (type === 'total_points_earned' || type === 'no_free') {
    const costFormatted = cost > 1000 ? `${Math.floor(cost / 1000)}k` : cost;

    return (
      costFormatted +
      ' ' +
      (cost !== 1 ? pointsNaming.plural : pointsNaming.singular)
    );
  }
  return '';
};

export const claimDetails = (perk: Perk, canClaimByPoints: boolean) => {
  const {
    claimHistory,
    isClaimed,
    type,
    multiplyRedeemable,
    remaining,
    isRedemptionLimit,
    amount,
    progress,
    eligibilityType
  } = perk;

  const isFreeRedeem = eligibilityType !== 'no_free';

  const wasClaimedDefault =
    isClaimed && claimHistory.some((c) => !c.paidWithPoints);

  const canBeClaimedSeveralTimes =
    multiplyRedeemable &&
    (remaining > 0 || !isRedemptionLimit) &&
    type !== 'voucher';

  const possibleToClaimByPoints = amount && (progress !== 100 || !isFreeRedeem);

  const canBeClaimedByPoints =
    (!isClaimed || canBeClaimedSeveralTimes) && canClaimByPoints;

  const canBeClaimedDefault =
    (!isClaimed || canBeClaimedSeveralTimes) && !wasClaimedDefault;

  const canBeClaimed =
    (possibleToClaimByPoints && canBeClaimedByPoints) || canBeClaimedDefault;

  return {
    canBeClaimed,
    canBeClaimedSeveralTimes,
    possibleToClaimByPoints,
    canBeClaimedDefault,
    canBeClaimedByPoints
  };
};
