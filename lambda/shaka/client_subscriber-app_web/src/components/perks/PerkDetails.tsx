import { Perk } from 'src/types/perks';
import ProgressBar from './ProgressBar';
import Button from '../common/Button';
import { CheckRoundedFilledIcon } from 'src/assets/icons/CheckRoundedFilled';
import { useEffect, useState } from 'react';
import Divider from '../common/Divider';
import { twMerge } from 'tailwind-merge';
import { PointsNaming } from 'src/types/client';
import { useClient } from 'src/hooks/useClient';
import { toast } from 'react-toastify';
import { CopyIcon } from 'src/assets/icons/Copy';
import { claimDetails, getCostTypeLabel } from 'src/components/perks/helpers';
import NotificationDialog from 'src/components/common/NotificationDialog';
import { getArticleForTitle } from 'src/helpers';

export type ClaimUnit = 'default' | 'points';
const getClaimNotificationDescription = (
  perk: Perk,
  claimUnit: ClaimUnit,
  pointsNaming: PointsNaming
) => {
  const isClaimPerPoints = claimUnit === 'points';
  const costType = getCostTypeLabel(
    isClaimPerPoints ? 'no_free' : perk.eligibilityType,
    isClaimPerPoints ? Number(perk.amount) : perk.cost,
    pointsNaming
  );

  return `Are you sure you wish to redeem ${getArticleForTitle(perk.title)} ${perk.title}${
    isClaimPerPoints ? ` for ${costType}` : ''
  }?`;
};

const costDescription = {
  total_points_earned: (cost: Perk['cost'], pointsNaming: PointsNaming) =>
    cost !== 1
      ? `${cost} ${pointsNaming.plural}`
      : `${cost} ${pointsNaming.singular}`,
  tenure: (cost: Perk['cost']) => 'Stay for ' + cost + ' months',
  airdrop: () => 'Airdrop',
  total_spend: () => 'Gift'
};

export default function PerkDetails({
  perk,
  onClaim,
  canClaimByPoints
}: {
  perk: Perk;
  onClaim: (unit: ClaimUnit) => void;
  canClaimByPoints: boolean;
}) {
  const [isJustClaimed, setIsJustClaimed] = useState(false);
  const { pointsNaming } = useClient();
  const [isShowMore, setIsShowMore] = useState(false);
  const [claimUnit, setClaimUnit] = useState<ClaimUnit>('default');
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);

  const {
    image,
    title,
    progress,
    cost,
    claimHistory,
    description,
    isClaimed,
    type,
    eligibilityType,
    amount,
    details
  } = perk;
  const hasClaimHistory = isClaimed && claimHistory.length > 0;
  const canShowMoreDetails = hasClaimHistory || details;

  const { canBeClaimedByPoints, canBeClaimedDefault } = claimDetails(
    perk,
    canClaimByPoints
  );

  const isFreeRedeem = eligibilityType !== 'no_free';
  const isVoucher = type === 'voucher';

  const copyCodeToClipboard = () => {
    navigator.clipboard.writeText(details!.code);
    toast.success('Code copied to clipboard');
  };

  const claimPerk = () => {
    setIsConfirmDialogOpen(false);
    onClaim(claimUnit);
    setIsJustClaimed(true);
  };

  const handleClaimClick = (unit: ClaimUnit) => () => {
    setClaimUnit(unit);
    setIsConfirmDialogOpen(true);
  };

  const closeConfirmDialog = () => {
    setIsConfirmDialogOpen(false);
  };

  useEffect(() => {
    if (isJustClaimed && details?.code) {
      setIsShowMore(true);
    }
  }, [isJustClaimed, details]);

  return (
    <>
      <div
        className={twMerge(
          'h-[calc(100dvh_-_40px)] relative flex flex-col justify-end pb-10 px-5 text-white',
          isShowMore && 'justify-start'
        )}
      >
        <div
          className="absolute inset-0 -z-[1]"
          style={{
            backgroundImage: `url(${image})`,
            backgroundPosition: 'center',
            backgroundSize: 'cover'
          }}
        >
          <div
            className={twMerge(
              'absolute inset-x-0 bottom-0 h-[50%] bg-gradient-to-t from-black to-transparent',
              isShowMore && 'h-[100%] to-black'
            )}
          />
        </div>
        {!isShowMore && (
          <>
            <div className="mb-4">
              <h2 className="font-bold text-xl">{title}</h2>
              <ProgressBar progress={progress} />
            </div>

            {amount && (progress !== 100 || !isFreeRedeem) && (
              <>
                <Divider className="my-3" />
                <div className="flex justify-center items-center">
                  <h4 className="grow text-lg font-semibold">
                    {costDescription['total_points_earned']?.(
                      Number(amount),
                      pointsNaming
                    )}
                  </h4>
                  <div className="w-[37%]">
                    {canBeClaimedByPoints ? (
                      <Button
                        fullWidth
                        squared
                        color="secondary"
                        onClick={handleClaimClick('points')}
                        narrow
                      >
                        Claim now
                      </Button>
                    ) : (
                      <div className="bg-white/10 p-3 rounded-[10px] text-center font-bold opacity-60">
                        {isClaimed ? 'Claimed' : 'Claim now'}
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {isFreeRedeem && (
              <>
                <Divider className="my-3" />
                <div className="flex justify-center items-center">
                  <h4 className="grow text-lg font-semibold">
                    {costDescription[eligibilityType]?.(cost, pointsNaming) ||
                      eligibilityType}
                  </h4>
                  <div className="w-[37%]">
                    {canBeClaimedDefault && progress === 100 ? (
                      <Button
                        fullWidth
                        squared
                        color="secondary"
                        onClick={handleClaimClick('default')}
                        narrow
                      >
                        Claim free
                      </Button>
                    ) : (
                      <div className="bg-white/10 p-3 px-3.5 rounded-[10px] text-center font-bold opacity-60">
                        {canBeClaimedDefault ? 'Claim free' : 'Claimed'}
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            <Divider className="my-3" />
            {description && <div className="mb-6">{description}</div>}
            {canShowMoreDetails && (
              <div>
                <button
                  onClick={() => setIsShowMore(!isShowMore)}
                  className="h-[21px] border-b border-white/60 hover:border-white"
                >
                  More details
                </button>
              </div>
            )}
          </>
        )}

        {isShowMore && (
          <div className="pt-20">
            <h2 className="font-bold text-xl">{title}</h2>

            {claimHistory.length > 0 && (
              <h4 className="text-sm font-semibold mt-8 mb-2">Claim history</h4>
            )}

            <div className="space-y-4">
              {claimHistory.map((claim) => (
                <div className="flex justify-between items-center gap-3 py-2.5 px-5 border-dotted bg-white rounded-xl  text-black">
                  {!isVoucher && (
                    <>
                      <CheckRoundedFilledIcon className="size-9" stroke="1" />
                      <p className="grow text-sm">Claimed - {type} applied</p>
                    </>
                  )}
                  {isVoucher && details?.code && (
                    <p className="grow text-sm flex justify-between items-center uppercase mr-2">
                      <span className="mr-4">{details.code}</span>
                      <button onClick={copyCodeToClipboard}>
                        <CopyIcon />
                      </button>
                    </p>
                  )}

                  <div className="text-[10px] text-right">
                    Claimed date:{' '}
                    <p className="font-semibold text-sm">
                      {new Date(claim.date).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {details?.expiry_date && (
              <div className="mt-10">
                <h4 className="text-sm font-semibold mb-4">Expiry date</h4>

                <p className="text-lg">
                  {new Date(details.expiry_date).toLocaleDateString()}
                </p>
              </div>
            )}

            {(details?.instructions || details?.url) && (
              <div className="mt-10">
                <h4 className="text-sm font-semibold mb-4">
                  Claim instructions
                </h4>

                {details.instructions && (
                  <p className="text-xs">{details.instructions}</p>
                )}

                {details.url && (
                  <a
                    href={details.url}
                    target="_blank"
                    className="text-sm underline"
                  >
                    {details.url}
                  </a>
                )}
              </div>
            )}

            <div className="m-auto w-2/5 mt-10">
              <Button
                color="secondary"
                onClick={() => setIsShowMore(false)}
                narrow
                squared
              >
                Done
              </Button>
            </div>
          </div>
        )}
      </div>
      {isConfirmDialogOpen && (
        <NotificationDialog
          isOpen
          onSubmit={closeConfirmDialog}
          onClose={closeConfirmDialog}
          onCancel={claimPerk}
          cancelButtonText="Yes"
          submitButtonText="No"
          dialogType="info"
          title="Confirmation"
          description={getClaimNotificationDescription(
            perk || ({} as Perk),
            claimUnit,
            pointsNaming
          )}
        />
      )}
    </>
  );
}
