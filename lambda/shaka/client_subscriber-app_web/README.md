# React + TypeScript + Vite

# Create a new client branding
For new clients

1. in /shaka/client_subscriber-app_web/vite.config.ts 
    1. add new client name (CN)  to `custom_clients`
    2. add new CN to `noFollowClients`  until the time of final deploy
2. create folder with CN in /shaka/client_subscriber-app_web/src/clients and make sure it has
    1. client-config.tsx 
        1. offer is the component in the ***components*** folder that displays on the home page
        2. policy and terms are the components in the ***components*** folder for the Terms and Policy pages content
        3. plainDashboardCard is for controlling plan card style on home page (is going to be updated with hideCircleLogo and dashboardCard props when this branch is in master)
        4. loginSource for the login source name on login page when using openID
        5. supportEmail, supportLink, help_data_issue, help_esim_install are links  for the support page
        6. backgroundImageNumber in case if there several artworks for the client we should define it here
    2. index.html (with updated title, styles href, and client-config path) and add class with client name to the root div
    3. styles.css - styles file that rewrites default styles for client
3. create folder with CN in /shaka/client_subscriber-app_web/public 
    1. add all files with proper naming as in the others similar clients folders
    2. if several background images and they're fixed by plan - check Card background/artwork bg param in the DB so they match the following
    the bg param calculates based on number of images. for example we have 3 images(N), then to match card index we use `bg%(N)+1`  
    card-1 - bg 3  where: bg % N + 1 = 3%3 + 1 = 0 + 1 = 1 (card-1.png)
    card-2 - bg 1  bg % N + 1 = 1%3 + 1 = 1 + 1 = 2 (card-2.png)
    card-3 - bg 2  bg % N + 1 = 2%3 + 1 = 2 + 1 = 3 (card-3.png)

4. add VITE_CUSTOM_CLIENT to .env 
5. in case we need google analytics there is VITE_GOOGLE_TRACKING_ID in env. 
   so we need to add it to the client envs on backend terraform/production/meta/main.tf

the list of assets to start a new client development:

- Card background/artwork (several if needed)
- main logo for login/ signup pages
- circle logo for home screen
- primary color
- secondary color (if primary is too light, i.e. for text or small icons like check marks on sign up form)
- email logo (if different from main)
- support email
- support link - link to knowledge base
- help_esim_install - link to q&a for “I am having eSIM issues” on “request help” page and in emails
- help_data_issue - link to “My data isn’t working” on “request help” page and in emails
- policy texts
- terms text
- intercom styles (updated on the intercom dashboard)
- google analytics