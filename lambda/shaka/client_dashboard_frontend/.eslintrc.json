{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "parser": "@typescript-eslint/parser", "plugins": ["react-refresh"], "rules": {"@typescript-eslint/semi": "off", "@typescript-eslint/explicit-function-return-type": "off", "react/react-in-jsx-scope": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/non-nullable-type-assertion-style": "off", "@typescript-eslint/no-floating-promises": "off", "@typescript-eslint/triple-slash-reference": "off", "@typescript-eslint/strict-boolean-expressions": "off", "@typescript-eslint/space-before-function-paren": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/member-delimiter-style": "off", "react-hooks/exhaustive-deps": "off", "@typescript-eslint/no-explicit-any": "off", "react/prop-types": "off", "react/display-name": "off"}}