export const flattenErrorsToSingleString = (
  obj: Record<string, any>,
  prefix = ''
): Record<string, any> => {
  const flatErrors: Record<string, any> = {};

  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;

      if (Array.isArray(obj[key]) && obj[key].length === 1) {
        flatErrors[newKey] = obj[key][0];
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        Object.assign(
          flatErrors,
          flattenErrorsToSingleString(obj[key], newKey)
        );
      } else {
        flatErrors[newKey] = obj[key];
      }
    }
  }

  return flatErrors;
};
