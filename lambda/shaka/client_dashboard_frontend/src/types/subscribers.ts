type SimType = 'esim' | 'physical';

export type Subscriber = {
  id: number;
  revenue_generated: string;
  group: string;
  status: string;
  latest_plan_name: string;
  latest_number: string;
  latest_number_display: string;
  name: string;
  start_date: string;
  email: string;
};

export type SubscriberPlan = {
  voice_component_offering: null;
  sms_component_offering: null;
  data_component_offering: null;
  name: string;
  price: string | null;
  status: string | null;
  implementation_datetime: null;
  plan_key: string;
  version_number: null;
  custom_voice_limit: null;
  custom_sms_limit: null;
  custom_data_limit: null;
  is_bundled: boolean;
  provider: null;
  bundle_id: null;
};

export type SubscriberInfo = {
  id: number;
  name: string;
  email: string;
  address: string;
  sim_status: string;
  num_subscriptions: number;
  total_profit: number;
  data_usage: number;
  voice_usage: number;
  sms_usage: number;
  start_date: string;
  latest_number: string;
  latest_number_display: string;
  latest_plan: SubscriberPlan;
  status: string;
  revenue_generated: string;
  group: string;
  date_of_birth: string;
  latest_sim_type?: SimType;
  perk_points: number;
  total_points_earned: number;
  perk_redemption_amount: number;
  send_marketing: boolean;
};

export enum SubscriberStatus {
  Active = 'Active',
  Pending = 'Pending',
  Inactive = 'Inactive',
  Warning = 'Warning',
  NeedsSim = 'Needs sim'
}
