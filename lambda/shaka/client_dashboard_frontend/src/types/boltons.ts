export type Bolton = {
  id: number;
  name: string;
  cost: string;
  status: 'enabled' | 'disabled';
  availability_date: string;
  min_cost?: string;
  initial_name?: string;
  offering: number;
};

export type BoltonsDetails = {
  purchases: number;
  revenue: string;
  perk_redemptions: number;
  uptake: string;
  bolt_ons: Bolton[];
};

export type BoltOnOffering = {
  available_for_new_selection: boolean;
  billing_calendar_sync: boolean;
  bolt_on_type: string;
  description: string;
  duration_type: string;
  id: number;
  name: string;
  price: string;
};
