import { useContext, useEffect, useState } from 'react';
import { useLocation, Link } from 'react-router-dom';
import clsx from 'clsx';
import Drawer from 'react-modern-drawer';
import { Menu } from '@headlessui/react';
import {
  ArrowRightStartOnRectangleIcon,
  Bars3Icon,
  ChevronDownIcon
} from '@heroicons/react/24/solid';
import { Cog8ToothIcon } from '@heroicons/react/24/outline';
import Avatar from 'src/components/Avatar';
import { ROUTES, disabledNavLinks, navbarConfig } from 'src/config/navigation';
import Loader from 'src/components/Loader';
import { BrandingContext } from 'src/contexts/BrandingContext';
import Logo from 'src/components/Logo';
import Typography, {
  TypographyColor,
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import NavBarItem from './NavBarItem';
import { SpnContext } from 'src/contexts/SpnContext';
import { AuthContext } from 'src/contexts/AuthContext';
import { getFirstNLetters } from 'src/helper';

export default function Header() {
  const { pathname } = useLocation();

  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeLink, setActiveLink] = useState(0);

  const numberOfLinks = navbarConfig.length;
  const indicatorWidth = `calc(100% / ${numberOfLinks})`;
  const positionOfIndicator = `calc( ${(100 / numberOfLinks) * activeLink}%)`;

  const { spn } = useContext(SpnContext);
  const {
    logoutUser,
    user: { email }
  } = useContext(AuthContext);
  const { branding, isLoading: isBrandingLoading } =
    useContext(BrandingContext);

  const handleToggleDrawer = () => {
    setIsDrawerOpen((prevState) => !prevState);
  };

  const handleLinkClick = (index: number) => () => {
    setActiveLink(index);
  };

  useEffect(() => {
    setActiveLink(navbarConfig.findIndex((item) => pathname.includes(item.id)));
  }, [pathname]);

  return (
    <div className="w-full max-w-[1960px] flex justify-between items-center m-auto mb-20 relative">
      <div className="flex items-center gap-6">
        <div className="w-14 h-14 rounded-lg overflow-hidden">
          {isBrandingLoading ? <Loader /> : <Logo src={branding?.logo} />}
        </div>
        <div className="max-w-[15vw]">
          <Typography as="p" bold truncate>
            {spn}
          </Typography>
          <Typography color={TypographyColor.Accent} bold>
            Mobile
          </Typography>
        </div>
      </div>
      <div className="hidden xl:block w-1/2 absolute left-1/2 -translate-x-1/2">
        <div className="relative z-10">
          <span className="absolute inline-block w-full h-full rounded-full bg-black/20 pointer-events-none z-[-1]" />
          {activeLink >= 0 && (
            <div
              className="indicator rounded-full bg-gradient-blue z-[-1]"
              style={{
                left: positionOfIndicator,
                width: indicatorWidth
              }}
            />
          )}
          <div className="flex h-14">
            {navbarConfig.map((item, i) => (
              <NavBarItem
                key={item.id}
                title={item.title}
                to={item.path}
                isActive={activeLink === i}
                onClick={handleLinkClick(i === 3 ? 0 : i)}
                disabled={disabledNavLinks.includes(item.id)}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="block xl:hidden">
        <button
          className=" p-3 hover:bg-white/10 rounded-xl"
          onClick={handleToggleDrawer}
        >
          <Bars3Icon
            className="h-5 w-5 text-gray-900"
            style={{ color: 'white' }}
          />
        </button>
        <Drawer
          open={isDrawerOpen}
          onClose={handleToggleDrawer}
          direction="left"
        >
          {navbarConfig.map((item) => (
            <Link
              key={item.id}
              to={item.path}
              className={clsx(
                'block text-navy-blue px-3.5 py-3  hover:bg-grey-200',
                {
                  'background-mystic-gradient text-white': pathname.includes(
                    item.id
                  )
                },
                {
                  'opacity-50 pointer-events-none': disabledNavLinks.includes(
                    item.id
                  )
                }
              )}
              onClick={() => handleToggleDrawer()}
            >
              <Typography color={TypographyColor.Secondary}>
                {item.title}
                {disabledNavLinks.includes(item.id) && (
                  <span className="ml-1">
                    <Typography
                      color={TypographyColor.Inherit}
                      shade={TypographyShade.Light}
                      size={TypographySize.Caption}
                    >
                      (Coming soon)
                    </Typography>
                  </span>
                )}
              </Typography>
            </Link>
          ))}
        </Drawer>
      </div>

      <div className="flex items-center translate-x-3">
        <div className="z-10">
          <Avatar size={48} initials={getFirstNLetters(email)} />
        </div>
        <div className="-translate-x-3 z-0">
          <Menu>
            <Menu.Button className="inline-flex w-full justify-center rounded-md bg-black/20 px-4 py-2 pl-6 text-sm font-medium text-white hover:bg-black/30 focus:outline-none">
              {email}
              <ChevronDownIcon
                className="-mr-1 ml-2 h-5 w-5 text-violet-200 hover:text-violet-100"
                aria-hidden="true"
              />
            </Menu.Button>

            <Menu.Items className="absolute right-0 mt-2 origin-top-right rounded-lg bg-black/20 shadow-sm w-auto">
              <Menu.Item>
                <Link
                  to={ROUTES.PROFILE}
                  className="flex items-center gap-3 px-2.5 py-2 text-white/50 font-medium hover:text-white"
                >
                  <Cog8ToothIcon className="w-5 h-5" />
                  <span>Settings</span>
                </Link>
              </Menu.Item>
              <Menu.Item>
                <Link
                  to={ROUTES.DASHBOARD}
                  reloadDocument
                  onClick={logoutUser}
                  className="flex items-center gap-3 px-2.5 py-2 text-white/50 font-medium hover:text-white"
                >
                  <ArrowRightStartOnRectangleIcon className="w-5 h-5" />
                  <span>Logout</span>
                </Link>
              </Menu.Item>
            </Menu.Items>
          </Menu>
        </div>
      </div>
    </div>
  );
}
