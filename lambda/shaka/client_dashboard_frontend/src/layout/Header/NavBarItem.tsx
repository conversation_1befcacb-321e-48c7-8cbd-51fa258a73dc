import clsx from 'clsx';
import { Link } from 'react-router-dom';
import ComingSoonCover from 'src/components/ComingSoonCover';
import Typography, { TypographyColor } from 'src/components/Typography';

type NavBarItemProps = {
  title: string;
  to: string;
  onClick?: () => void;
  isActive?: boolean;
  disabled?: boolean;
};

export default function NavBarItem({
  title,
  onClick,
  to,
  isActive,
  disabled
}: NavBarItemProps) {
  const text = <Typography color={TypographyColor.Inherit}>{title}</Typography>;

  if (disabled) {
    // will be removed once all pages are implemented
    return (
      <ComingSoonCover content="Coming soon" disabledChild fullSize debounced>
        <span
          onClick={onClick}
          className={clsx(
            'w-full h-full rounded-full text-sm font-medium leading-5 flex items-center justify-center'
          )}
        >
          {text}
        </span>
      </ComingSoonCover>
    );
  }

  return (
    <Link
      to={to}
      onClick={onClick}
      className={clsx(
        'w-full rounded-full leading-5 flex items-center justify-center',
        'ring-white/60 ring-offset-2 ring-offset-blue-400 focus:outline-none',
        {
          'text-text-dark': isActive,
          'text-white hover:text-blue-600': !isActive
        }
      )}
    >
      {text}
    </Link>
  );
}
