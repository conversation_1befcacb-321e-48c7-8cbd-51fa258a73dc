import { ChevronDownIcon } from '@heroicons/react/24/solid';
import { useRef, useState } from 'react';
import Button from './Button';
import { useClickOutside } from 'src/hooks/useClickOutside';
import { DateTime } from 'luxon';
import clsx from 'clsx';

const months = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December'
];
const monthShort = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec'
];

const { year, month } = DateTime.now().toObject();
const yearsOptionsDefault = [year.toString()];
const selectorLikeButtonStyle =
  'relative bg-grey-input text-white w-full cursor-default rounded-full py-2 pl-4 pr-8 text-left text-lg font-medium hover:cursor-pointer';

interface Props {
  onChange: (year: string, month: string) => void;
  minDate?: { month: string; year: string };
  yearsOptions?: string[];
}

const MonthPicker = ({
  onChange,
  minDate,
  yearsOptions = yearsOptionsDefault
}: Props) => {
  const ref = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const [selectedYear, setSelectedYear] = useState<string>(year.toString());
  const [selectedMonth, setSelectedMonth] = useState<number>(month - 1);
  const [isYearOptionsVisible, setIsYearOptionsVisible] = useState(false);

  const handleMonthSelect = (month: number) => () => {
    setSelectedMonth(month);
    setIsVisible(false);
    onChange(selectedYear, `${month + 1}`);
  };

  const handleYearSelect = (year: string) => () => {
    setSelectedYear(year);
    setIsYearOptionsVisible(false);
  };

  useClickOutside(ref, () => {
    setIsVisible(false);
    setIsYearOptionsVisible(false);
  });

  return (
    <div className="w-[250px] relative" ref={ref}>
      <Button
        onClick={() => setIsVisible(true)}
        className={selectorLikeButtonStyle}
      >
        {months[selectedMonth]} {selectedYear}
        <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
          <ChevronDownIcon className="h-5 w-5" aria-hidden="true" />
        </span>
      </Button>

      {isVisible && (
        <div className="mt-1 py-2 px-4 bg-grey-input-solid rounded-xl absolute z-10">
          <button
            className={selectorLikeButtonStyle}
            onClick={() => setIsYearOptionsVisible(true)}
          >
            <span className="block truncate">{selectedYear}</span>
            <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
              <ChevronDownIcon className="h-5 w-5" aria-hidden="true" />
            </span>
          </button>

          <div className="mt-4 grid grid-cols-4 gap-4">
            {isYearOptionsVisible
              ? yearsOptions.map((year) => (
                  <button
                    key={year}
                    onClick={handleYearSelect(year)}
                    className="hover:bg-deep-blue-200 p-1 rounded-lg"
                  >
                    {year}
                  </button>
                ))
              : monthShort.map((monthTitle, index) => (
                  <button
                    key={monthTitle}
                    onClick={handleMonthSelect(index)}
                    className={clsx('hover:bg-deep-blue-200 p-1 rounded-lg', {
                      'opacity-50 pointer-events-none hover:bg-transparent':
                        (selectedYear === year.toString() &&
                          index > month - 1) ||
                        (minDate?.year === selectedYear &&
                          index < +minDate?.month)
                    })}
                  >
                    {monthTitle}
                  </button>
                ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MonthPicker;
