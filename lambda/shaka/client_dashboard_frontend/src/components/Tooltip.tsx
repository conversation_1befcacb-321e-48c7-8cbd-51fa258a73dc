import { PropsWithChildren, useState } from 'react';
import Typography, { TypographySize } from './Typography';
import clsx from 'clsx';

interface Props {
  text: string;
  alwaysShow?: boolean;
  bold?: boolean;
  hidden?: boolean;
  wrapperClassName?: string;
  tooltipClassName?: string;
  near?: boolean;
  colorScheme?: 'default' | 'flat' | 'flat-white';
  textSize?: TypographySize;
  textWrap?: boolean;
}

const Tooltip = ({
  children,
  text,
  alwaysShow,
  bold,
  hidden,
  wrapperClassName,
  tooltipClassName,
  near,
  colorScheme,
  textSize = TypographySize.Caption,
  textWrap
}: PropsWithChildren<Props>) => {
  const [show, setShow] = useState(alwaysShow);

  return (
    <div
      onMouseEnter={() => !hidden && setShow(true)}
      onMouseLeave={() => !alwaysShow && setShow(false)}
      className={clsx('tooltip', wrapperClassName)}
    >
      {children}
      {show && (
        <div
          className={clsx(
            'tooltiptext',
            tooltipClassName,
            colorScheme,
            near && 'near'
          )}
        >
          <Typography size={textSize} bold={bold} noWrap={!textWrap}>
            {text}
          </Typography>
        </div>
      )}
    </div>
  );
};

export default Tooltip;
