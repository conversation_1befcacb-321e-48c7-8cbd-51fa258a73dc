import { useEffect, useState } from 'react';
import clsx from 'clsx';
import Alert from './Alert';

type Props = {
  errors: string[];
};

export default function ValidationErrorsBox({ errors }: Props) {
  const [showErrors, setShowErrors] = useState(true);

  useEffect(() => {
    setShowErrors(true);
  }, [errors]);

  return (
    <div className="mb-5">
      <Alert
        className={clsx({
          hidden: !showErrors
        })}
        variant="error"
        onClose={() => setShowErrors(false)}
      >
        <ul>{errors?.map((error, idx) => <li key={idx}>{error}</li>)}</ul>
      </Alert>
    </div>
  );
}
