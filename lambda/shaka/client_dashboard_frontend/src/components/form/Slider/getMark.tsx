import clsx from 'clsx';
import { ReactSliderProps } from 'react-slider';

const getMark =
  (
    format: (value: number) => string,
    max?: number,
    unlimited?: boolean
  ): ReactSliderProps['renderMark'] =>
  (props) => {
    if (!props.key) return <></>;

    const key = Number(props.key);

    return (
      <div {...props} className="z-[-1]">
        <div className="relative">
          <span
            className={clsx(
              'block rounded-full w-6 h-6 bg-grey-slider absolute -top-1 -translate-x-1/2 backdrop-blur-xl opacity-90',
            )}
          />
          <div
            className={clsx(
              'absolute translate-y-7 -translate-x-1/2 text-xs font-medium text-grey-400'
            )}
          >
            {key === max && unlimited ? 'Unlimited' : format(key)}
          </div>
        </div>
      </div>
    );
  };

export default getMark;
