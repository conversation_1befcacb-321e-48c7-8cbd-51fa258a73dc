import { useFormContext } from 'react-hook-form';
import Typography, {
  TypographyColor,
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import clsx from 'clsx';
import Button from 'src/components/Button';
import Logo from 'src/components/Logo';
import useImageDrop from 'src/hooks/useImageDrop';

//arrow-up-tray

export default function BrandingLogo() {
  const {
    watch,
    setValue,
    formState: {
      errors: { logo: error }
    }
  } = useFormContext();

  const onDrop = (src: string) => {
    setValue('logo', src, { shouldValidate: true });
  };

  const { isDropped, metadata, getRootProps, getInputProps } =
    useImageDrop(onDrop);

  const logo = watch('logo', '');

  return (
    <div>
      <div className="mb-4">
        <Typography color={TypographyColor.Accent}>Logo</Typography>
      </div>

      <div
        {...getRootProps()}
        className={clsx(
          'relative w-full border-dashed border-2 border-white/20 rounded-lg bg-white/10 cursor-pointer',
          'md:h-36'
        )}
      >
        <input {...getInputProps()} />
        <div
          className={clsx(
            'w-full h-full flex gap-4 items-center p-5 md:gap-12',
            'max-md:flex-col max-md:text-center'
          )}
        >
          <div className="w-24 h-24">
            <Logo src={logo} />
          </div>

          {isDropped ? (
            <div>
              <Typography bold as="p">
                {metadata.name}
              </Typography>
              <Typography
                shade={TypographyShade.Light}
                as="p"
                size={TypographySize.Caption}
              >
                {metadata.width}x{metadata.height} pixels
              </Typography>
              <Button type="button">Change image</Button>
            </div>
          ) : (
            <Typography>
              Drag and drop new image here or{' '}
              <Typography underline color={TypographyColor.Accent}>
                click
              </Typography>{' '}
              to upload
            </Typography>
          )}
        </div>
      </div>

      {error && (
        <div className="text-red-600 py-1">{error.message?.toString()}</div>
      )}
    </div>
  );
}
