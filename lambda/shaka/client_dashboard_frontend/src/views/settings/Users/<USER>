import Card from 'src/components/Card';
import UsersTable from './UsersTable';
import Link from 'src/components/Link';
import { ROUTES } from 'src/config/navigation';
import { useAsync } from 'src/api/useApi';
import { fetchUsers } from 'src/api/users';
import { useEffect } from 'react';
import {
  selectorSetUsers,
  selectorUsers,
  selectorUsersLoaded,
  useUsersStore
} from 'src/store/users';

export default function Users() {
  const users = useUsersStore(selectorUsers);
  const setUsers = useUsersStore(selectorSetUsers);
  const usersLoaded = useUsersStore(selectorUsersLoaded);
  const { run: doGetUserList, isLoading: isUsersLoading } =
    useAsync(fetchUsers);

  useEffect(() => {
    if (usersLoaded) return;

    doGetUserList().then((res) => {
      setUsers(res);
    });
  }, []);

  return (
    <Card
      title="Users"
      actions={<Link to={ROUTES.USER_ADD}>Add</Link>}
      isLoading={isUsersLoading && !usersLoaded}
      fullHeight
    >
      <UsersTable data={users} />
    </Card>
  );
}
