import CardWithTabs from 'src/components/CardWithTabs';
import { Tab } from '@headlessui/react';
import PlansList from './PlansList';
import BoltOnsList from './BoltOnsList';

export default function PlansView() {
  return (
    <CardWithTabs
      tabs={[
        { title: 'Plans', tabName: 'plans' },
        { title: 'Bolt-ons', tabName: 'bolt-ons' }
      ]}
    >
      <Tab.Panels>
        <Tab.Panel>
          <PlansList />
        </Tab.Panel>
        <Tab.Panel>
          <BoltOnsList />
        </Tab.Panel>
      </Tab.Panels>
    </CardWithTabs>
  );
}
