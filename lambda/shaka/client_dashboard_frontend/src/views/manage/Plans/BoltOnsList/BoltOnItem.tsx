import { Link } from 'react-router-dom';
import Status, { StatusColor, StatusSize } from 'src/components/Status';
import { getBoltOnEditRoute } from 'src/config/navigation';
import { currencyRounded } from 'src/helper';
import { <PERSON> } from 'src/types/boltons';

export default function BoltOnItem({
  id,
  cost,
  status,
  name,
  initial_name
}: Bolton) {
  const isActive = status === 'enabled';
  return (
    <Link to={getBoltOnEditRoute(id)}>
      <div className="bg-gradient-bolton bg-[length:100%_200%] hover:bg-[center_15%] transition-all h-[220px] rounded-[32px] p-5 shadow-lg flex flex-col justify-between">
        <Status
          status={isActive ? 'Active' : 'Inactive'}
          color={isActive ? StatusColor.Success : StatusColor.Error}
          size={StatusSize.Big}
        />
        <p className="text-2xl font-semibold text-ellipsis">
          {name || initial_name}
        </p>
        <p className="text-right text-3xl font-semibold">
          {currencyRounded(Number(cost))}
        </p>
      </div>
    </Link>
  );
}
