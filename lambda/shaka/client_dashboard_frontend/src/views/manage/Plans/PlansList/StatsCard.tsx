import clsx from 'clsx';
import { useMemo } from 'react';
import { capitalize, currencyFormat } from 'src/helper';
import {
  selectorPlanComponentOfferingById,
  usePlansOfferingStore
} from 'src/store/plansOfferings';
import { Plan, PlanStatus } from 'src/types/plans';
import { getDataText, getSmsText, getVoiceText } from 'src/helper/plans';
import { getPlanEditRoute } from 'src/config/navigation';
import Link, { LinkColor } from 'src/components/Link';
import { PlanTheme1 } from 'src/assets/plan-theme-1';

const statusStyles = {
  [PlanStatus.Active]: 'bg-success',
  [PlanStatus.Inactive]: 'bg-warning'
};

interface Props extends Partial<Plan> {
  classes?: string;
}

export default function StatsCard({
  id,
  name,
  price,
  status,
  data_component_offering: dataOfferingId,
  sms_component_offering: smsOfferingId,
  voice_component_offering: voiceOfferingId,
  custom_data_limit,
  custom_sms_limit,
  custom_voice_limit,
  classes = ''
}: Props) {
  const plansById = usePlansOfferingStore(selectorPlanComponentOfferingById);

  const dataDescription = useMemo(() => {
    const dataLimit =
      dataOfferingId && plansById[dataOfferingId].plan_component.max_limit;

    return getDataText(custom_data_limit || dataLimit);
  }, [plansById, dataOfferingId, custom_data_limit]);

  const smsDescription = useMemo(() => {
    const smsLimit =
      smsOfferingId && plansById[smsOfferingId].plan_component.max_limit;

    return getSmsText(custom_sms_limit || smsLimit);
  }, [plansById, smsOfferingId, custom_sms_limit]);

  const voiceDescription = useMemo(() => {
    const voiceLimit =
      voiceOfferingId && plansById[voiceOfferingId].plan_component.max_limit;

    return getVoiceText(custom_voice_limit || voiceLimit);
  }, [plansById, voiceOfferingId, custom_voice_limit]);

  return (
    <div
      className={clsx(
        'relative rounded-2xl px-5 pt-6 pb-3 bg-gradient-plan w-[450px] h-[260px]',
        classes
      )}
    >
      <PlanTheme1 className="absolute w-full h-auto top-0 left-0 z-[-1]" />
      <div className="flex flex-col justify-between h-full ">
        <div className="mb-8">
          <span className="inline-flex items-center gap-2 rounded-lg px-3 py-1 text-white bg-black/[.15] text-base">
            <span
              className={clsx('h-2 w-2 rounded-full', statusStyles[status!])}
            />
            {capitalize(status!)}
          </span>
          <div className="text-4xl mt-5">{name || 'Default name'}</div>
          <div>{dataDescription}</div>
          <div>{voiceDescription}</div>
          <div>{smsDescription}</div>
        </div>
        <div className="w-full flex justify-between">
          <div className="text-2xl font-bold">
            {currencyFormat(Number(price))}
            <span className="text-base">/month</span>
          </div>

          {id && (
            <Link
              to={getPlanEditRoute(id)}
              color={LinkColor.Helper}
              className="block absolute top-0 right-0 w-[120px] p-1.5"
            >
              Edit
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}
