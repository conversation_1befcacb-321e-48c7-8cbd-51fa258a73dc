import { createSearch<PERSON>ara<PERSON>, useNavigate, useParams } from 'react-router-dom';
import {
  createBolton,
  getBoltOnsOfferings,
  updateBolton
} from 'src/api/boltons';
import { useAsync } from 'src/api/useApi';
import Card from 'src/components/Card';
import Status, { StatusColor } from 'src/components/Status';
import { getBoltonsViewRoute, ROUTES } from 'src/config/navigation';
import useBoltonList from 'src/hooks/useBoltonList';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import Button, { ButtonColor } from 'src/components/Button';
import Input from 'src/components/form/Input';
import InputWrapper from 'src/components/form/WrapperWithLabel';
import Link from 'src/components/Link';
import ErrorText from 'src/components/ErrorText';
import RadioButtonGroup from 'src/components/form/RadioButtonGroup';
import { statusOptions } from 'src/constants';
import Availability from 'src/components/Availability';
import { currencyRounded } from 'src/helper';
import { useEffect, useMemo } from 'react';
import { Inputs } from './types';
import { schema } from './schema';
import { yupResolver } from '@hookform/resolvers/yup';
import NumberInput from 'src/components/form/NumberInput';
import Dropdown from 'src/components/form/Dropdown';

export default function BoltOnView() {
  const { boltonId } = useParams();
  const navigate = useNavigate();

  const {
    isLoading: isDataLoading,
    getBoltonById,
    loadBoltOns
  } = useBoltonList();

  const bolton = getBoltonById(boltonId);

  const { error: updateError, run: doUpdateBolton } = useAsync(updateBolton);
  const { error: createError, run: doCreateBolton } = useAsync(createBolton);
  const { data: boltOnsOfferings } = useAsync(getBoltOnsOfferings, {
    fetchOnMount: true
  });

  const responseError = updateError || createError;

  const isEditPage = Boolean(boltonId);

  const methods = useForm<Inputs>({
    defaultValues: {
      name: '',
      cost: '',
      availability_date: '',
      availability: 'active',
      status: 'enabled',
      min_cost: '',
      edit_page: isEditPage
    },
    resolver: yupResolver(schema)
  });

  const {
    handleSubmit,
    register,
    control,
    watch,
    reset,
    setValue,
    formState: { errors }
  } = methods;

  const watchName = watch('name');
  const watchOfferingId = watch('offering_id');
  const watchMinCost = watch('min_cost');

  const boltOnsOfferingsOptions = useMemo(
    () =>
      boltOnsOfferings?.map((item) => ({
        value: item.id,
        label: item.name
      })) || [],
    [boltOnsOfferings]
  );

  const namePrompt =
    bolton?.initial_name && watchName !== bolton?.initial_name
      ? `Bolt-on offer: ${bolton?.initial_name}`
      : '';

  useEffect(() => {
    if (isEditPage && !isDataLoading) {
      reset({
        name: bolton?.name || bolton?.initial_name,
        cost: bolton?.cost,
        availability_date: bolton?.availability_date.slice(0, 16) || '',
        availability: bolton?.availability_date ? 'inactive' : 'active',
        status: (bolton?.status as Inputs['status']) || 'enabled',
        min_cost: bolton?.min_cost,
        edit_page: isEditPage,
        offering_id: bolton?.offering
      });
    }
  }, [bolton, isDataLoading]);

  useEffect(() => {
    if (!isEditPage) {
      const minValue = boltOnsOfferings?.find(
        (offering) => offering.id === watchOfferingId
      )?.price;

      setValue('min_cost', minValue);
    }
  }, [watchOfferingId]);

  const goToBolotonsList = () => {
    loadBoltOns();
    navigate({
      pathname: ROUTES.PLANS,
      search: createSearchParams({
        tab: 'bolt-ons'
      }).toString()
    });
  };

  const onSubmit = ({
    availability,
    cost,
    name,
    status,
    availability_date,
    offering_id
  }: Inputs) => {
    const payload = {
      cost,
      name,
      availability_date:
        availability === 'inactive'
          ? availability_date!
          : new Date().toISOString(),
      status,
      min_cost: bolton?.min_cost,
      initial_name: bolton?.initial_name,
      offering: offering_id
    };

    if (isEditPage) {
      doUpdateBolton(boltonId!, payload).then(() => {
        goToBolotonsList();
      });

      return;
    }

    doCreateBolton(payload).then(() => {
      goToBolotonsList();
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <FormProvider {...methods}>
        <Card
          title="Bolt-ons"
          subtitle={isEditPage ? 'Edit bolt-on' : 'Create bolt-on'}
          isLoading={isDataLoading}
          actions={
            isEditPage &&
            (bolton?.status === 'enabled' ? (
              <Status status="Active" color={StatusColor.Success} />
            ) : (
              <Status status="Inactive" color={StatusColor.Error} />
            ))
          }
          bottomActions={
            <>
              <Link to={getBoltonsViewRoute()}>Cancel</Link>
              <Button type="submit" color={ButtonColor.Pink}>
                Save changes
              </Button>
            </>
          }
        >
          <div className="space-y-16 mb-10">
            {!isEditPage && (
              <InputWrapper
                label="Bolt-on base"
                error={errors.offering_id?.message}
              >
                <Dropdown
                  control={control}
                  name="offering_id"
                  options={boltOnsOfferingsOptions}
                />
              </InputWrapper>
            )}

            <div>
              <InputWrapper
                label="Bolt-on name"
                error={errors.name?.message}
                description={namePrompt}
              >
                <Input {...register('name')} />
              </InputWrapper>
            </div>

            <InputWrapper
              label="Cost"
              postInputLabel={
                watchMinCost &&
                `Minimum ${currencyRounded(Number(watchMinCost))}`
              }
              error={errors.cost?.message}
            >
              <div className="w-1/4">
                <NumberInput {...register('cost', { valueAsNumber: true })} />
              </div>
            </InputWrapper>

            <InputWrapper label="Status">
              <Controller
                name="status"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <div className="w-min">
                    <RadioButtonGroup
                      horizontal
                      value={value}
                      options={statusOptions}
                      onChange={onChange}
                    />
                  </div>
                )}
              />
            </InputWrapper>

            <Availability />
          </div>

          <ErrorText
            titles={{
              name: 'Bolt-on name',
              cost: 'Cost',
              offering: 'Bolt-on base'
            }}
          >
            {responseError}
          </ErrorText>
        </Card>
      </FormProvider>
    </form>
  );
}
