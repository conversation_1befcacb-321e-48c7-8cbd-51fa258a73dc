import Slider from 'src/components/form/Slider';
import { useEffect, useMemo, useState } from 'react';
import { PlanComponentOffering, PlanDimension } from 'src/types/plans';
import { dimensionMarks } from './constants';
import { DIMENSION_UNLIMITED } from 'src/config/constants';
import { findItemWithClosestLimit } from './helper';
import { DimensionsLimit } from 'src/store/plansOfferings';

interface Props {
  type: PlanDimension;
  customLimit?: number;
  offering: PlanComponentOffering;
  extension: string;
  dimensionLimits?: DimensionsLimit[];
  disabled?: boolean;
  onChange?: (value: number, dimensionId: number) => void;
  defaultState?: boolean;
}

export const DimensionData = ({
  offering,
  extension,
  customLimit,
  type,
  dimensionLimits,
  disabled,
  onChange,
  defaultState
}: Props) => {
  const { max_limit: limit } = offering?.plan_component || {};

  const [slider, setSlider] = useState(customLimit || limit || 1);

  const sliderValues = useMemo(
    () =>
      dimensionLimits
        ?.filter(Boolean)
        .map((limit) => limit.limit)
        .sort((a, b) => a - b) || dimensionMarks[type],
    [type, dimensionLimits]
  );
  const maxValue = sliderValues[sliderValues.length - 1] * 1.3;

  useEffect(() => {
    setSlider(defaultState ? 1 : customLimit || limit || maxValue);
  }, [limit]);

  const handleChange = (value: number) => {
    if (dimensionLimits) {
      const closestLimit = findItemWithClosestLimit(
        dimensionLimits,
        value,
        maxValue
      );

      if (closestLimit) {
        const { id: dimensionId, limit: sliderValue } = closestLimit;
        setSlider(sliderValue || maxValue);

        onChange &&
          onChange(
            sliderValue === maxValue ? DIMENSION_UNLIMITED : sliderValue,
            dimensionId
          );
      }
    }
  };

  return (
    <div className="px-2 w-2/3">
      <Slider
        min={5}
        max={maxValue}
        unlimited
        value={Number(slider)}
        marks={sliderValues}
        onChange={handleChange}
        disabled={disabled}
        format={(value) => `${value}${extension === 'GB' ? `\u00A0GB` : ''}`}
        hideTooltip={defaultState}
      />
    </div>
  );
};
