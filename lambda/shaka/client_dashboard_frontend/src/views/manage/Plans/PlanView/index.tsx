import clsx from 'clsx';
import { useEffect, useState } from 'react';
import Card from 'src/components/Card';
import Input from 'src/components/form/Input';
import { Plan, PlanStatus } from 'src/types/plans';
import Button, { ButtonColor } from 'src/components/Button';
import {
  Controller,
  FormProvider,
  SubmitHandler,
  useForm
} from 'react-hook-form';
import {
  selectorPlanComponentOfferingById,
  usePlansOfferingStore
} from 'src/store/plansOfferings';
import usePlansOffering from 'src/hooks/usePlansOffering';
import { formDefaultValues, planActivationOptions } from './constants';
import { Tab } from '@headlessui/react';
import {
  createBundledPlan,
  createCustomPlan,
  updateBundledPlan,
  updateCustomPlan
} from 'src/api/plans';
import { useNavigate, useParams } from 'react-router-dom';
import { ROUTES } from 'src/config/navigation';
import LabeledInputWrapper from 'src/components/form/WrapperWithLabel';
import Calendar from 'src/components/form/Calendar';
import RadioButtonGroup from 'src/components/form/RadioButtonGroup';
import PresetTab from './tabs/PresetTab';
import CustomTab from './tabs/CustomTab';
import { Inputs } from './types';
import usePlanList from 'src/hooks/usePlanList';
import { selectorPlanById, usePlansStore } from 'src/store/plan';
import { convertToFormData, convertToPayload } from './helper';
import { yupResolver } from '@hookform/resolvers/yup';
import { schema } from './schema';
import Link from 'src/components/Link';
import PlanMinPrice from './PlanMinPrice';
import Typography, {
  TypographyColor,
  TypographyShade
} from 'src/components/Typography';
import { useAsync } from 'src/api/useApi';
import ErrorText from 'src/components/ErrorText';
import StatsCard from '../PlansList/StatsCard';
import { planGeneratorOption } from '../constants';
import { yesNoOptions } from 'src/views/monetize/Perks/PerkView/constants';
import NumberInput from 'src/components/form/NumberInput';

export default function PlanView() {
  const navigate = useNavigate();
  const { planId } = useParams();

  const { isLoading: isOfferingLoading } = usePlansOffering();
  const { isLoading: isPlansLoading, loadPlans } = usePlanList();

  const { run: doCreateCustomPlan, error: createCustomPlanError } =
    useAsync(createCustomPlan);
  const { run: doCreateBundledPlan, error: createBundledPlanError } =
    useAsync(createBundledPlan);
  const { run: doUpdateCustomPlan, error: updateCustomPlanError } =
    useAsync(updateCustomPlan);
  const { run: doUpdateBundledPlan, error: updateBundledPlanError } =
    useAsync(updateBundledPlan);

  const error =
    createCustomPlanError ||
    createBundledPlanError ||
    updateCustomPlanError ||
    updateBundledPlanError;

  const plan = usePlansStore(selectorPlanById(planId));
  const planComponentOfferingById = usePlansOfferingStore(
    selectorPlanComponentOfferingById
  );

  const methods = useForm<Inputs>({
    defaultValues: plan ? convertToFormData(plan) : formDefaultValues,
    resolver: yupResolver(schema)
  });
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    getValues,
    control,
    formState: { errors }
  } = methods;
  const planStatus = watch('status');
  const pointsAccrual = watch('pointsAccrual');

  const submitButtonLabel = planId ? 'Save changes' : 'Create plan';

  const [selectedTab, setSelectedTab] = useState(
    planId ? (plan?.bundle_id ? 0 : 1) : 0
  );
  const [prevPlan, setPreviousPlan] = useState<Partial<Inputs>>({});

  useEffect(() => {
    if (planId && !isPlansLoading && plan) {
      setSelectedTab(plan.bundle_id ? 0 : 1);
      reset(convertToFormData(plan));
    }
  }, [isPlansLoading, planId, plan]);

  const onPlanSave = () => {
    loadPlans();
    navigate(ROUTES.PLANS);
  };

  const updatePlan = (payload: Partial<Plan>) => {
    const isBundled = payload.bundle_id;

    if (!planId) return;

    if (isBundled) {
      doUpdateBundledPlan(planId, payload).then(onPlanSave);
      return;
    }

    doUpdateCustomPlan(planId, payload).then(onPlanSave);
  };

  const createPlan = (payload: Partial<Plan>) => {
    const isBundled = payload.bundle_id;

    if (isBundled) {
      doCreateBundledPlan(payload).then(onPlanSave);
      return;
    }

    doCreateCustomPlan(payload).then(onPlanSave);
  };

  const onSubmit: SubmitHandler<Inputs> = (data) => {
    const payload: Partial<Plan> = convertToPayload({
      ...data,
      provider:
        planComponentOfferingById[data.dataOfferingId!]?.plan_component.provider
    });

    if (planId) {
      updatePlan(payload);
      return;
    }

    createPlan(payload);
  };

  const onDimensionOptionChange =
    (formField: keyof Inputs, limitField: keyof Inputs) => (event: any) => {
      const offeringId = Number(event.target.value);
      const offeringLimit =
        planComponentOfferingById[offeringId]?.plan_component.max_limit;

      setValue(limitField, offeringLimit);
      register(formField).onChange(event);
    };

  const onBundledPlanChange = (newFormData: Partial<Inputs>) => {
    reset((prev) => ({ ...prev, ...newFormData }));
  };

  const handleTabChange = (index: number) => {
    setSelectedTab(index);
    const currentValues = getValues();
    const {
      bundleId = null,
      dataOfferingId,
      smsOfferingId,
      voiceOfferingId,
      voiceLimit,
      smsLimit,
      dataLimit
    } = prevPlan;
    reset((prev) => ({
      ...prev,
      bundleId,
      dataOfferingId,
      smsOfferingId,
      voiceOfferingId,
      voiceLimit,
      smsLimit,
      dataLimit
    }));
    setPreviousPlan(currentValues);
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Card
          title="Plans"
          subtitle={!planId ? 'New plan wizard' : 'Edit plan'}
          bottomActions={
            <>
              <Link to={ROUTES.PLANS}>Cancel</Link>
              <Button type="submit" color={ButtonColor.Pink}>
                {submitButtonLabel}
              </Button>
            </>
          }
          isLoading={isOfferingLoading || (Boolean(planId) && isPlansLoading)}
          fullHeight
        >
          <div className="flex flex-col gap-8">
            <div className="mb-10">
              <StatsCard
                status={watch('status')}
                name={watch('name')}
                data_component_offering={watch('dataOfferingId')}
                sms_component_offering={watch('smsOfferingId')}
                voice_component_offering={watch('voiceOfferingId')}
                custom_data_limit={watch('dataLimit')}
                custom_sms_limit={watch('smsLimit')}
                custom_voice_limit={watch('voiceLimit')}
                price={watch('price').toString()}
                classes="drop-shadow-lg max-w-[434px]"
              />
            </div>

            <Tab.Group onChange={handleTabChange} selectedIndex={selectedTab}>
              <div className="w-full flex gap-x-6">
                <LabeledInputWrapper label="Name" className="w-2/5">
                  <Input placeholder="Nearly unlimited" {...register('name')} />
                </LabeledInputWrapper>

                <LabeledInputWrapper label="Plan generator" className="w-2/5">
                  <Tab.List className="flex gap-1 rounded-full bg-grey-input p-1 h-12">
                    {planGeneratorOption.map(({ value, label }) => (
                      <Tab
                        key={value}
                        className={({ selected }) =>
                          clsx(
                            'w-full rounded-full text-sm font-medium leading-5',
                            'ring-white/60 focus:outline-none',
                            selected
                              ? 'bg-white text-black shadow'
                              : 'hover:bg-white/[0.12] hover:text-white'
                          )
                        }
                      >
                        <Typography color={TypographyColor.Inherit}>
                          {label}
                        </Typography>
                      </Tab>
                    ))}
                  </Tab.List>
                </LabeledInputWrapper>
              </div>

              <Tab.Panels>
                <Tab.Panel>
                  <PresetTab onBundledPlanChange={onBundledPlanChange} />
                </Tab.Panel>

                <Tab.Panel>
                  <CustomTab onDimensionChange={onDimensionOptionChange} />
                </Tab.Panel>
              </Tab.Panels>
            </Tab.Group>

            <LabeledInputWrapper
              label="Price"
              subLabel={<PlanMinPrice />}
              className="w-2/5"
            >
              <Input placeholder="0" {...register('price')} prefix="£" />
            </LabeledInputWrapper>

            <LabeledInputWrapper
              label="Perk points accrual"
              error={errors.pointsPerMonth?.message}
            >
              <div className="flex items-center gap-8 h-12">
                <Controller
                  name="pointsAccrual"
                  control={control}
                  render={({ field: { value = 'no', onChange, ref } }) => (
                    <div className="flex gap-10 relative">
                      <input ref={ref} className="absolute opacity-0 size-0" />
                      <RadioButtonGroup
                        options={yesNoOptions}
                        horizontal
                        value={value}
                        onChange={onChange}
                      />
                      {pointsAccrual === 'yes' && (
                        <div className="inline-flex items-center gap-4">
                          <NumberInput
                            {...register('pointsPerMonth')}
                            placeholder="Enter point amount"
                            integerOnly
                          />
                          <Typography shade={TypographyShade.Dark}>
                            points per month
                          </Typography>
                        </div>
                      )}
                    </div>
                  )}
                />
              </div>
            </LabeledInputWrapper>

            <LabeledInputWrapper label="Activation date">
              <div className="flex items-center gap-8 h-12">
                <RadioButtonGroup
                  horizontal
                  value={planStatus}
                  options={planActivationOptions}
                  onChange={(value) => {
                    setValue('status', value as PlanStatus);
                  }}
                />
                {planStatus === PlanStatus.Inactive && (
                  <div className="inline-block w-2/5">
                    <Calendar {...register('implementationDate')} />
                  </div>
                )}
              </div>
            </LabeledInputWrapper>
          </div>

          <ErrorText error={error} />
        </Card>
      </form>
    </FormProvider>
  );
}
