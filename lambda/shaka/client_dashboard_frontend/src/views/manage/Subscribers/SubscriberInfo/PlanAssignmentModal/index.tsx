import { Dialog, Transition } from '@headlessui/react';
import { Fragment, useState } from 'react';
import { createPortal } from 'react-dom';
import Button, { ButtonColor } from 'src/components/Button';
import CloseButton from 'src/components/CloseButton';
import Typography, { TypographySize } from 'src/components/Typography';
import NumberInput from 'src/components/form/NumberInput';
import { Controller, useForm } from 'react-hook-form';
import SimCard from './SimCard';
import ErrorText from 'src/components/ErrorText';
import { Inputs } from './types';
import { yupResolver } from '@hookform/resolvers/yup';
import { schema } from './schema';
import { useAsync } from 'src/api/useApi';
import { assignSimToSubscriber } from 'src/api/subscriber';
import { SubscriberInfo } from 'src/types/subscribers';
import OtpInput from 'react18-input-otp';
import { inputClass } from 'src/components/sharedStyles';
import { twMerge } from 'tailwind-merge';

export default function PlanAssignmentModal({
  title,
  isOpen,
  onClose,
  onSubmit,
  subscriber
}: {
  title: string;
  onClose: () => void;
  onSubmit: () => void;
  isOpen: boolean;
  subscriber: SubscriberInfo;
}) {
  const {
    run: runAssignSimToSubscriber,
    isLoading,
    error
  } = useAsync(assignSimToSubscriber);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const {
    handleSubmit,
    register,
    watch,
    control,
    formState: { errors }
  } = useForm<Inputs>({
    resolver: yupResolver(schema)
  });

  const [step, setStep] = useState(0);

  const onFormSubmit = (data: Inputs) => {
    setIsSubmitted(true);
    runAssignSimToSubscriber({
      serial: data.serial,
      subscription_id: subscriber.id
    }).then(() => {
      onSubmit();
      onClose();
      setIsSubmitted(false);
    });
  };

  const inputs = watch();
  const serialNumber = watch('serial');
  const lastDigitsFilled = Object.values(inputs).every((i) => i);

  return createPortal(
    <Transition show={isOpen}>
      <Dialog as="div" onClose={onClose} className="relative z-50">
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-white/[0.01] backdrop-blur-sm" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              enter="ease-in duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-out duration-300"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <Dialog.Panel className="px-10 py-8 bg-white/[0.03] backdrop-blur-[55px] min-w-[700px] w-[70vw] h-[70vh] max-w-[1400px] rounded-xl">
                <Dialog.Title className="flex justify-between items-center">
                  <Typography size={TypographySize.Title}>{title}</Typography>

                  <div className="-mr-2 -mt-1">
                    <CloseButton onClose={onClose} />
                  </div>
                </Dialog.Title>
                <form onSubmit={handleSubmit(onFormSubmit)} className="h-full">
                  <div className="h-full flex justify-between items-center gap-10 text-left max-w-[85%] m-auto">
                    {step === 0 && (
                      <div className="flex flex-col gap-10 grow">
                        <Typography
                          size={TypographySize.CardTitle}
                          lineHeight={1}
                        >
                          Enter the SIM serial to assign
                        </Typography>
                        <div className="w-10/12">
                          <NumberInput
                            integerOnly
                            placeholder="Enter SIM serial"
                            shape="square"
                            {...register('serial')}
                          />
                        </div>
                        <div>
                          <Button
                            onClick={() => setStep(1)}
                            color={ButtonColor.Pink}
                            disabled={!serialNumber || serialNumber.length < 10}
                          >
                            Next
                          </Button>
                        </div>
                      </div>
                    )}
                    {step === 1 && (
                      <div className="flex flex-col gap-10 text-left relative">
                        <Typography
                          size={TypographySize.CardTitle}
                          lineHeight={1}
                        >
                          Confirm the last 4 digits
                        </Typography>
                        <div>
                          <Controller
                            control={control}
                            name="code"
                            render={({
                              // eslint-disable-next-line @typescript-eslint/no-unused-vars
                              field: { ref, onChange, ...rest }
                            }) => (
                              <>
                                <OtpInput
                                  {...rest}
                                  onChange={(value: string) => {
                                    if (/^\d+$/.test(value)) {
                                      4;
                                      onChange(value);
                                    } else {
                                      return;
                                    }
                                  }}
                                  shouldAutoFocus
                                  isInputNum
                                  containerStyle={{
                                    display: 'flex',
                                    gap: 12
                                  }}
                                  className={twMerge(
                                    'otp',
                                    inputClass,
                                    'p-0 rounded-xl w-13'
                                  )}
                                />
                              </>
                            )}
                          />

                          <div>
                            <ErrorText>
                              {errors['code']?.message || error}
                            </ErrorText>
                          </div>
                        </div>

                        <div className="flex flex-col gap-10">
                          <div className="flex gap-5">
                            <Button onClick={() => setStep(0)}>Back</Button>
                            <Button
                              type="submit"
                              color={ButtonColor.Pink}
                              disabled={!lastDigitsFilled}
                              isLoading={isSubmitted && isLoading}
                            >
                              Assign
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}

                    <SimCard
                      backSide={step === 1}
                      serialNumber={serialNumber}
                    />
                  </div>
                </form>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>,

    document.body
  );
}
