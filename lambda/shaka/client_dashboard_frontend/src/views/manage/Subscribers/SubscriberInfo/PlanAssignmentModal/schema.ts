import * as yup from 'yup';
import { Inputs } from './types';

const lastDigitsError = 'Last 4 digits do not match the serial number';

export const schema = yup
  .object({
    serial: yup.string().required(),
    code: yup
      .string()
      .required()
      .when('serial', ([serial], schema) => {
        const last4 = serial.slice(-4);
        return schema.matches(new RegExp(last4), lastDigitsError);
      })
  })
  .required() as yup.ObjectSchema<Inputs>;
