import { useMemo } from 'react';
import CsvDownloader from 'react-csv-downloader';
import { DateTime } from 'luxon';
import { useAsync } from 'src/api/useApi';
import Card from 'src/components/Card';
import SubscribersTable from './SubscribersTable';
import { fetchSubscribers } from 'src/api/subscriber';
import { CsvKey, SUBSCRIBER_COLUMNS_CSV } from './constants';
import Button, { ButtonColor } from 'src/components/Button';
import { csvFormatSubscribers } from 'src/helper/csvFormat';
import AddIcon from 'src/icons/Add';
import useClientData from 'src/hooks/useClient';

export default function SubscribersView() {
  const { run: runFetchSubscribers } = useAsync(fetchSubscribers);
  const { clientData } = useClientData();

  const fileName = `${
    clientData.name
  }-subscribers-${DateTime.now().toLocaleString()}.csv`;

  const csvData = async () => {
    const subscribers = await runFetchSubscribers();

    return csvFormatSubscribers(subscribers);
  };

  const activeSubscribersAmount = useMemo(() => {
    const subsAmount = clientData.active_subscriber_count;

    return subsAmount === 1
      ? '1 active subscriber'
      : `${subsAmount} active subscribers`;
  }, [clientData]);

  return (
    <Card
      title="Subscribers"
      subtitle={activeSubscribersAmount}
      actions={
        <CsvDownloader
          filename={fileName}
          columns={Object.keys(SUBSCRIBER_COLUMNS_CSV).map((key) => ({
            id: key,
            displayName: SUBSCRIBER_COLUMNS_CSV[key as CsvKey]
          }))}
          datas={csvData}
        >
          <Button color={ButtonColor.GrayGradient} leftIcon={<AddIcon />}>
            <span>Export to CSV</span>
          </Button>
        </CsvDownloader>
      }
      fullHeight
    >
      <SubscribersTable />
    </Card>
  );
}
