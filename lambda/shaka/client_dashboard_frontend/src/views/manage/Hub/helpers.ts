import { currencyFormat } from 'src/helper';

export const getPrevMonthInIso = () => {
  const d = new Date();
  d.setMonth(d.getMonth() - 1);
  d.setDate(1);
  return d.toISOString().split('T')[0];
};

const checkIfNumberHasDecimal = (num: number) => {
  return num % 1 !== 0;
};

export const metricsFormat: { [key: string]: (val: number) => string } = {
  percentage: (val: number) => `${val > 0 ? '+' : ''}${val.toFixed(1)}%`,
  currency: (val: number) => currencyFormat(val).split('.')[0],

  months: (val: number) =>
    checkIfNumberHasDecimal(val) ? `${val.toFixed(1)}` : val.toString()
};

const currentDate = new Date().toISOString();

export const defaultMetrics = {
  'profit-growth': {
    [currentDate]: {
      date: [currentDate],
      key: 'profit-growth',
      name: 'Growth',
      values: [
        {
          value: 0,
          context: '',
          format: 'percentage',
          primary: true,
          description:
            'Your growth rate is calculated by the percentage change of profit within the specific time period. Any value above 1% ensures long term growth.',
          is_extrapolated: false
        }
      ]
    }
  },
  'profit-per-subscriber': {
    [currentDate]: {
      date: [currentDate],
      key: 'profit-per-subscriber',
      name: 'Profit',
      values: [
        {
          value: 0,
          context: '',
          format: 'currency',
          primary: true,
          description:
            'Your average profit calculation  encompasses profit made across plans and bolt-ons per subscriber. Adding attractive bolt-ons can help increase it.',
          is_extrapolated: false
        }
      ]
    }
  },
  'lifetime-value': {
    [currentDate]: {
      date: [currentDate],
      key: 'lifetime-value',
      name: 'Lifetime Value',
      values: [
        {
          value: 0,
          context: '',
          format: 'currency',
          primary: true,
          description:
            'Lifetime value (LTV) is the average number of months a subscriber stays on your network and the profit made over this time.',
          is_extrapolated: false
        }
      ]
    }
  },
  'run-rate': {
    [currentDate]: {
      date: [currentDate],
      key: 'run-rate',
      name: 'Run-Rate',
      values: [
        {
          value: 0,
          context: '',
          format: 'currency',
          primary: true,
          description:
            'Your run-rate calculation is a forecast of the total profit generated over the next 12 months based on on your current growth and trajectory.',
          is_extrapolated: false
        }
      ]
    }
  }
};
