import clsx from 'clsx';
import { PropsWithChildren } from 'react';
import { Link } from 'react-router-dom';
import ArrowRightCircleIcon from 'src/icons/ArrowRightCircle';
import ShareIcon from 'src/icons/Share';
import ShareGradientIcon from 'src/icons/ShareGradient';

interface Props {
  to: string;
}

const ActionLink = ({ children, to }: PropsWithChildren<Props>) => {
  return (
    <Link
      to={to}
      className={clsx(
        'action-link',
        'flex items-center justify-between gap-3',
        'group relative w-full h-13 bg-black/20 rounded-xl block size-full py-3.5 px-4 transition',
        'hover:bg-transparent'
      )}
    >
      <span
        className={clsx(
          'absolute left-4 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 rounded-xl p-2 transition-all'
        )}
      >
        <ShareIcon className="w-5 h-5 max-xl:w-3 max-xl:h-3" />
      </span>
      <span className="group-hover:opacity-0 bg-white/10 rounded-xl p-2 transition-all">
        <ShareGradientIcon />
      </span>

      <span className="text-xl grow">
        {children}
      </span>

      <ArrowRightCircleIcon className="w-8 h-8 min-w-[23px]" />
    </Link>
  );
};

export default ActionLink;
