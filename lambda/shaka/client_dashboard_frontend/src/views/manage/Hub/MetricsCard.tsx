import Typography, {
  TypographyColor,
  TypographyShade,
  TypographySize
} from 'src/components/Typography';
import { metricsFormat } from './helpers';
import { MetricsData } from 'src/types/metrics';
import { InfoIcon } from 'src/icons/Info';
import Tooltip from 'src/components/Tooltip';

interface Props {
  data: MetricsData;
}

const MetricsCard = ({ data }: Props) => {
  const secondaryValue = data.values.find((value) => !value.primary);
  const primaryValue =
    data.values.find((value) => value.primary) || secondaryValue;

  const isExtrapolated = primaryValue?.is_extrapolated;
  const isSecondaryValue = secondaryValue && secondaryValue.value !== null;

  return (
    <div className="flex flex-col gap-4 relative rounded-3xl pt-10 p-7 z-[1] bg-[url('/hub/metrics.png')] bg-full">
      <div>
        <Typography size={TypographySize.CardTitle}>{data.name}</Typography>
      </div>
      <div className="grow">
        <Typography size={TypographySize.BodyM} lineHeight={2}>
          {primaryValue?.description}
        </Typography>
      </div>
      <div className="text-center text-[#9A5EB2] mt-4 flex justify-evenly">
        {isSecondaryValue && (
          <div className="text-[#827FC4]">
            <div className='desktop:text-5xl text-4xl'>
              <Typography
                size={TypographySize.Inherit}
                color={TypographyColor.Inherit}
                as="p"
                bold
              >
                {metricsFormat[secondaryValue.format](
                  Number(secondaryValue.value)
                )}
              </Typography>
            </div>
            <Typography
              size={TypographySize.BodyS}
              shade={TypographyShade.Light}
              trackingWide
            >
              {secondaryValue.context}
            </Typography>
          </div>
        )}
        <Tooltip
          text="Forecasted value for this period"
          hidden={!isExtrapolated}
          bold
        >
          <div className="desktop:text-5xl text-4xl">
            <Typography
              size={TypographySize.Inherit}
              color={TypographyColor.Inherit}
              as="p"
              bold
            >
              {metricsFormat[primaryValue!.format](Number(primaryValue?.value))}
            </Typography>
          </div>
          <div className="flex gap-2 items-center justify-center">
            <Typography
              size={TypographySize.BodyS}
              color={TypographyColor.Inherit}
              trackingWide
            >
              {primaryValue?.context}
            </Typography>
            {isExtrapolated && <InfoIcon className="w-5 h-5" />}
          </div>
        </Tooltip>
      </div>
    </div>
  );
};
export default MetricsCard;
