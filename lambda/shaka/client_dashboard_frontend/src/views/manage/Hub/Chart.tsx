import { DateTime } from 'luxon';
import { useMemo } from 'react';
import { AxisOptions, Chart as ReactChart } from 'react-charts';
import { formatNumber } from 'src/helper';

export type ChartData = {
  date: string;
  value?: number | null;
};

const findMax = (data: ChartData[]) => {
  return data.reduce((acc, curr) => {
    return Math.max(acc, curr.value || 0);
  }, 0);
};

const findMin = (data: ChartData[]) => {
  const minVal = data.reduce((acc, curr) => {
    return Math.min(acc, curr.value || 0);
  }, Infinity);

  return minVal < 0 ? minVal : 0;
};

const shortThousands = (value: number) => {
  return formatNumber(value, {
    notation: 'compact',
    style: 'currency',
    currency: 'GBP'
  });
};

interface Props {
  charts: {
    data: ChartData[];
  }[];
}

export default function Chart({ charts }: Props) {
  const primaryAxis = useMemo<AxisOptions<ChartData>>(
    () => ({
      getValue: (datum) =>
        DateTime.fromISO(datum.date).toLocaleString({ month: 'short' }),
      tickCount: charts[0].data.length
    }),
    []
  );

  const secondaryAxes = useMemo<AxisOptions<ChartData>[]>(
    () => [
      {
        getValue: (datum) => Number((datum.value || 0)!.toFixed(2)),
        show: false,
        elementType: 'area',
        hardMin: findMin(charts[0].data) * 1.1,
        hardMax: findMax(charts[0].data) * 1.3
      }
    ],
    []
  );

  return (
    <>
      <ReactChart<ChartData>
        options={{
          data: charts,
          primaryAxis,
          secondaryAxes,
          dark: true,
          primaryCursor: {
            showLabel: false
          },
          secondaryCursor: {
            show: false
          },
          interactionMode: 'closest',

          tooltip: {
            render: ({ focusedDatum }) => {
              return (
                <div className="tooltiptext tooltip-chart font-semibold">
                  {shortThousands(focusedDatum?.secondaryValue)}
                </div>
              );
            }
          },

          getDatumStyle: () => ({
            opacity: 1,
            circle: {
              r: 6,
              stroke: 'white',
              fill: '#F36699',
              strokeWidth: 2
            }
          }),

          getSeriesStyle: () => ({
            color: `url(#2)`,
            strokeWidth: 3,
            line: {
              stroke: 'url(#1)'
            }
          }),
          renderSVG: () => (
            <defs>
              <linearGradient id="1" x1="0" x2="0" y1="0" y2="1">
                <stop offset="50%" stopColor="#607BB5" />
                <stop offset="100%" stopColor="#2E215C" />
              </linearGradient>
              <linearGradient id="2" x1="0" x2="0" y1="0" y2="1">
                <stop offset="30%" stopColor="#3C3F4E" stopOpacity="0.7" />
                <stop offset="140%" stopColor="#000000" stopOpacity="0" />
              </linearGradient>
            </defs>
          )
        }}
      />
    </>
  );
}
