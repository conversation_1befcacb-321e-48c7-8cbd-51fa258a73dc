import Card from 'src/components/Card';
import InfoCard from './InfoCard';
import Status, { StatusColor } from 'src/components/Status';
import Typography, {
  TypographyColor,
  TypographySize
} from 'src/components/Typography';
import useBeams from 'src/hooks/useBeams';
import { useParams } from 'react-router-dom';
import Link, { LinkColor } from 'src/components/Link';
import { ROUTES } from 'src/config/navigation';
import { BeamStatus } from 'src/types/sms';
import Warning from '../Warning';
import { getFormattedDate } from '../heplers';
import { getSMSUnits } from 'src/helper';
import { formatPercent } from 'src/helper/numbers';

export default function BeamDetails() {
  const { beamId } = useParams();
  const { beamsById = {}, isLoading } = useBeams({
    loadOnMount: true
  });

  const { message, status, title, delivery_report } =
    beamsById[Number(beamId)] ?? {};
  const {
    delivery_rate,
    send_date,
    sender,
    total_delivered,
    total_pending,
    total_sent
  } = delivery_report || {};
  const isErrored = status === BeamStatus.ERRORED;
  const metrics = [
    {
      title: 'TOTAL SENT',
      value: isErrored ? '-' : total_sent ?? '0'
    },
    {
      title: 'DELIVERED',
      value: isErrored ? '-' : total_delivered ?? '0'
    },
    { title: 'PENDING', value: isErrored ? '-' : total_pending ?? '0' },
    {
      title: 'DELIVERY RATE',
      value: isErrored ? '-' : formatPercent(delivery_rate),
      color: TypographyColor.Success
    }
  ];

  return (
    <Card
      subtitle={title}
      actions={
        <Link
          to={{
            pathname: ROUTES.BEAMS_ADD,
            search: `?beamId=${beamId}`
          }}
          color={LinkColor.GrayGradient}
        >
          Duplicate beam
        </Link>
      }
      isLoading={isLoading}
    >
      <div className="grid grid-cols-5 gap-14">
        <div className="col-span-3 space-y-10">
          <InfoCard classes="whitespace-pre-wrap">
            <Typography>{message}</Typography>
          </InfoCard>
          <InfoCard classes="grid 2xl:grid-cols-4 grid-cols-2 gap-5">
            {metrics.map(({ title, value, color }) => (
              <InfoCard key={title}>
                <div className="flex items-center justify-center h-[90px]">
                  <div className="mb-3">
                    <Typography bold color={color}>
                      {value}
                    </Typography>
                  </div>
                  <div className="absolute bottom-3 left-0 w-full px-1.5 text-center">
                    <Typography
                      size={TypographySize.Caption}
                      lineHeight="tight"
                      uppercase
                    >
                      {title}
                    </Typography>
                  </div>
                </div>
              </InfoCard>
            ))}
          </InfoCard>
        </div>
        <div className="col-span-2 flex flex-col gap-10">
          <div className="flex gap-4">
            <Status status="Text message" color={StatusColor.Success} />
            <Status status="WhatsApp" color={StatusColor.Error} />
          </div>
          <Typography>
            From: <Typography bold>{sender}</Typography>
          </Typography>
          {isErrored && <Warning />}
          {!isErrored && send_date && (
            <Typography>Sent on {getFormattedDate(send_date)}</Typography>
          )}
          <Typography>
            Length: {message?.length} characters ({getSMSUnits(message) || 0}{' '}
            units)
          </Typography>
        </div>
      </div>
      <div className="mt-24 text-center">
        <Link to={ROUTES.BEAMS} color={LinkColor.Pink}>
          Done
        </Link>
      </div>
    </Card>
  );
}
