import * as yup from 'yup';
import { Inputs } from './types';
import { BeamStatus } from 'src/types/sms';

export const schema = yup
  .object({
    title: yup.string().min(0).required('Title is required'),
    message: yup.string().min(0).required('Message is required'),
    status: yup.string().optional(),
    send_on: yup.string().when('status', {
      is: BeamStatus.TO_SEND,
      then: (schema) =>
        schema
          .required('Publish date is required when specific date is selected')
          .test('minDate', 'Date must be in the future', (value) => {
            if (!value) return true;
            return new Date(value) > new Date();
          }),
      otherwise: (schema) => schema.optional()
    })
  })
  .required() as yup.ObjectSchema<Inputs>;
