import Typography, {
  TypographyColor,
  TypographySize
} from 'src/components/Typography';
import WarningIcon from 'src/icons/Warning';

const errorText = 'Error: Beam not sent successfully';

export default function Warning() {
  return (
    <div className="flex items-center gap-2">
      <WarningIcon className="w-4 h-4" />
      <Typography color={TypographyColor.Error} size={TypographySize.BodyS}>
        {errorText}
      </Typography>
    </div>
  );
}
