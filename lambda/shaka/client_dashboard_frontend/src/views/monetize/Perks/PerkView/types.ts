import {
  BoltOnDetails,
  DiscountDetails,
  Eligibility,
  VoucherDetails
} from 'src/types/perks';

type YesNo = 'yes' | 'no';

export enum PerkStatus {
  Inactive = 'inactive',
  Active = 'active'
}

export type Inputs = {
  status: 'enabled' | 'disabled';
  name: string;
  cost?: number;
  eligibility_type: Eligibility;
  eligibility: {
    amount: string;
    multiple_redemptions: YesNo;
    target_group?: string;
  };
  image: string;
  description?: string;
  use_points?: YesNo;
  pointsCost?: string;
  eligibility_target?: 'everyone' | 'specific';
  availability: PerkStatus;
  availability_date?: string;
  redemption_limit?: number;
  redemption: 'limited' | 'unlimited';
  featured: boolean;
} & (
  | {
      type: 'discount';
      discount_details: DiscountDetails;
    }
  | {
      type: 'bolt_on';
      bolt_on_details: BoltOnDetails;
    }
  | {
      type: 'voucher';
      voucher_details: VoucherDetails;
    }
);
