import { Controller, FormProvider, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';
import Card from 'src/components/Card';
import usePerks from 'src/hooks/usePerks';
import { selectorPerkById, usePerksStore } from 'src/store/perks';
import { Inputs, PerkStatus } from './types';
import Button, { ButtonColor } from 'src/components/Button';
import { useEffect } from 'react';
import Input from 'src/components/form/Input';
import InputWrapper from 'src/components/form/WrapperWithLabel';
import RadioButtonGroup from 'src/components/form/RadioButtonGroup';
import { statusOptions, yesNoOptions, yesNoOptionsReverse } from './constants';
import ImageDrop from './components/ImageDrop';
import Textarea from 'src/components/form/Textarea';
import Typography, { TypographyShade } from 'src/components/Typography';
import Eligibility from './components/Eligibility';
import PerkType from './components/PerkType';
import Link from 'src/components/Link';
import { ROUTES } from 'src/config/navigation';
import RedemptionLimit from './components/RedemptionLimit';
import { convertToForm, convertToPayload, fieldsTitles } from './helper';
import { createPerk, updatePerk } from 'src/api/perks';
import Status, { StatusColor } from 'src/components/Status';
import { schema } from './schema';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAsync } from 'src/api/useApi';
import NumberInput from 'src/components/form/NumberInput';
import Availability from 'src/components/Availability';
import Dropdown from 'src/components/form/Dropdown';
import Checkbox from 'src/components/form/Checkbox';
import ErrorText from 'src/components/ErrorText';

const defaultValues: Inputs = {
  name: '',
  image: '',
  description: '',
  eligibility_type: 'tenure',
  status: 'enabled',
  use_points: 'no',
  eligibility: {
    amount: '',
    multiple_redemptions: 'no'
  },
  type: 'discount',
  discount_details: {
    discount_type: 'percentage',
    discount_amount: '0',
    discount_percentage: '5',
    discount_duration_months: 1
  },
  availability: PerkStatus.Active,
  redemption: 'unlimited',
  featured: false
};

export default function PerkView() {
  const { perkId } = useParams();
  const { isLoading, loadPerks } = usePerks();
  const perkById = usePerksStore(selectorPerkById(perkId));
  const navigate = useNavigate();

  const { run: doUpdatePerk, error: updateError } = useAsync(updatePerk);
  const { run: doCreatePerk, error: createError } = useAsync(createPerk);

  const responseError = updateError || createError;

  const subtitle = perkId ? 'Edit perk' : 'Create a new perk';
  const submitButtonLabel = perkId ? 'Save changes' : 'Create perk';

  const methods = useForm<Inputs>({
    defaultValues,
    resolver: yupResolver(schema)
  });

  const {
    handleSubmit,
    register,
    control,
    watch,
    reset,
    formState: { errors }
  } = methods;

  const watchUsePoints = watch('use_points');
  const watchStatus = watch('status');
  const watchEligibilityType = watch('eligibility_type');
  const watchAvailabilityDate = watch('availability_date');

  useEffect(() => {
    if (perkById && !isLoading) {
      reset(convertToForm(perkById));
    }
  }, [perkById, isLoading]);

  const returnToPerks = () => {
    loadPerks();
    navigate(ROUTES.PERKS);
  };

  const onSubmit = (data: Inputs) => {
    const payload = convertToPayload(data);

    if (perkId) {
      doUpdatePerk(perkId, payload).then(() => {
        returnToPerks();
      });
    } else {
      doCreatePerk(payload).then(() => {
        returnToPerks();
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Card
        title="Perk"
        subtitle={subtitle}
        isLoading={isLoading}
        actions={
          perkId &&
          (watchStatus === 'enabled' ? (
            <Status status="Active" color={StatusColor.Success} />
          ) : (
            <Status status="Inactive" color={StatusColor.Error} />
          ))
        }
        bottomActions={
          <>
            <Link to={ROUTES.PERKS}>Cancel</Link>
            <Button type="submit" color={ButtonColor.Pink}>
              {submitButtonLabel}
            </Button>
          </>
        }
      >
        <FormProvider {...methods}>
          <div className="grid grid-cols-2 grid-rows-3 gap-x-12 h-[430px] mb-4">
            <div className="row-span-1">
              <InputWrapper label="Name" error={errors.name?.message}>
                <Input
                  {...register('name')}
                  placeholder="Type in the name of your perk"
                />
              </InputWrapper>
            </div>

            <div className="row-span-3">
              <InputWrapper
                label="Upload perk image"
                error={errors.image?.message}
              >
                <Controller
                  name="image"
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <ImageDrop src={value} onChange={onChange} />
                  )}
                />
              </InputWrapper>
            </div>

            <div className="row-span-3 mt-7">
              <InputWrapper label="Description" subLabel="(optional)">
                <Textarea
                  name="description"
                  control={control}
                  height={230}
                  placeholder="Add a description of the perk if you wish"
                />
              </InputWrapper>
            </div>

            <InputWrapper label="Your cost base" subLabel="(optional)">
              <NumberInput
                {...register('cost')}
                placeholder="Input the unitary cost per perk in £"
              />
            </InputWrapper>
          </div>

          <Eligibility />

          <div className="my-32 space-y-14">
            <InputWrapper
              label="Perk can also be redeemed for points?"
              error={errors.pointsCost?.message || errors.use_points?.message}
            >
              <Controller
                name="use_points"
                control={control}
                render={({ field: { value = 'no', onChange, ref } }) => (
                  <div className="flex gap-10 relative">
                    <input ref={ref} className="absolute opacity-0 size-0" />
                    <RadioButtonGroup
                      options={yesNoOptionsReverse}
                      horizontal
                      value={value}
                      onChange={onChange}
                    />
                    {watchUsePoints === 'yes' && (
                      <div className="inline-flex items-center gap-4">
                        <NumberInput
                          {...register('pointsCost')}
                          placeholder="Enter point amount"
                          integerOnly
                        />
                        <Typography shade={TypographyShade.Dark}>
                          points
                        </Typography>
                      </div>
                    )}
                  </div>
                )}
              />
            </InputWrapper>

            <div className="w-[314px]">
              <InputWrapper label="Can be redeemed multiple times">
                <Dropdown
                  control={control}
                  name="eligibility.multiple_redemptions"
                  options={yesNoOptions}
                  shape="square"
                  disabled={
                    watchEligibilityType !== 'no_free' &&
                    watchUsePoints === 'no'
                  }
                />
              </InputWrapper>
            </div>
          </div>

          <PerkType />

          <div className="my-20 space-y-14">
            <Availability min={watchAvailabilityDate} />
          </div>

          <RedemptionLimit />

          <div className="mb-8">
            <InputWrapper label="Status">
              <Controller
                name="status"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <div className="w-min">
                    <RadioButtonGroup
                      horizontal
                      value={value}
                      options={statusOptions}
                      onChange={onChange}
                    />
                  </div>
                )}
              />
            </InputWrapper>
          </div>

          <div className="mb-8 mt-12">
            <InputWrapper>
              <div className="inline-block rounded-xl bg-white/[0.05] px-5 py-3 w-1/3">
                <Controller
                  name="featured"
                  control={control}
                  render={({ field: { value, onChange } }) => (
                    <Checkbox
                      checked={value}
                      label="Featured perk"
                      name="featured"
                      onChange={onChange}
                    />
                  )}
                />
              </div>
            </InputWrapper>
          </div>

          {responseError && (
            <ErrorText titles={fieldsTitles}>{responseError}</ErrorText>
          )}
        </FormProvider>
      </Card>
    </form>
  );
}
