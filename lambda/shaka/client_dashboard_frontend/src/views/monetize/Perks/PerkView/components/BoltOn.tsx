import MultiDropdown from 'src/components/form/MultiDropdown';
import { Controller, useFormContext } from 'react-hook-form';
import useBoltonList from 'src/hooks/useBoltonList';
import { useMemo } from 'react';

export default function BoltOn() {
  const { activeBoltOns, isLoading } = useBoltonList();

  const boltOnsOptions = useMemo(() => {
    return activeBoltOns.map((boltOn) => ({
      value: boltOn.id,
      label: boltOn.name
    }));
  }, [activeBoltOns]);

  const { control } = useFormContext();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="mt-8">
      {activeBoltOns.length === 0 ? (
        <div className="text-center">No active bolt-ons available</div>
      ) : (
        <Controller
          name="bolt_on_details.bolt_on"
          control={control}
          render={({ field: { value, onChange } }) => (
            <MultiDropdown
              options={boltOnsOptions}
              onChange={onChange}
              selectedOptions={value}
              hideNewOptionButton
            />
          )}
        />
      )}
    </div>
  );
}
