import { ColumnDef } from '@tanstack/react-table';
import Table from 'src/components/Table';
import { getSubscriberInfoRoute } from 'src/config/navigation';
import { useNavigate } from 'react-router-dom';
import { DateTime } from 'luxon';
import { PerkSubscriber } from 'src/types/perks';

export const COLUMNS = {
  fullName: 'Subscriber',
  redemption_date: 'REDEMPTION DATE',
  quantity: 'QTY. REMAINING'
};

type Props = {
  data: PerkSubscriber[];
};

const columns: ColumnDef<PerkSubscriber, React.ReactNode>[] = [
  {
    id: 'full_name',
    header: () => <div className="text-left">{COLUMNS.fullName}</div>,
    accessorFn: (row) => row.user.full_name,
    cell: (info) => <div className="text-left">{info.getValue()}</div>
  },
  {
    accessorKey: 'redemption_date',
    header: COLUMNS.redemption_date,
    cell: (info) =>
      DateTime.fromISO(info.getValue() as string).toLocaleString(
        DateTime.DATETIME_SHORT
      )
  },
  {
    accessorKey: 'quantity',
    header: COLUMNS.quantity,
    cell: (info) => {
      return info.row.original.is_unlimited ? '∞' : info.getValue();
    }
  }
];

export default function SubscribersTable({ data }: Props) {
  const navigate = useNavigate();

  return (
    <Table
      columns={columns}
      data={data}
      squareRow
      onRowClick={(row) =>
        navigate(getSubscriberInfoRoute(row?.original.user.id))
      }
      withPagination
      pageSize={5}
    />
  );
}
