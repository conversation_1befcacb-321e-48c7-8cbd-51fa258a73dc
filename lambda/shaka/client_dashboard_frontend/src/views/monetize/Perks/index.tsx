import Card from 'src/components/Card';
import Link, { LinkColor } from 'src/components/Link';
import Typography from 'src/components/Typography';
import { ROUTES } from 'src/config/navigation';
import usePerks from 'src/hooks/usePerks';
import AddIcon from 'src/icons/Add';
import PerkListItem from './components/PerkListItem';

export default function Perks() {
  const { isLoading, perks } = usePerks();

  const inactivePerks = perks.filter((perk) => !perk.enabled);
  const activePerks = perks.filter((perk) => perk.enabled);

  return (
    <Card
      title="Perks"
      subtitle="All perks"
      isLoading={isLoading}
      actions={
        <Link
          to={ROUTES.PERKS_ADD}
          color={LinkColor.GrayGradient}
          icon={<AddIcon />}
        >
          Add new perk
        </Link>
      }
    >
      {perks.length === 0 ? (
        <div className="flex flex-col items-center justify-center gap-6 h-64">
          <Typography>No existing perk</Typography>
          <Link to={ROUTES.PERKS_ADD} className="ml-2">
            Create a perk
          </Link>
        </div>
      ) : (
        <div className="space-y-10">
          <div className="grid grid-cols-3 gap-4 gap-y-6">
            {activePerks.map((perk) => (
              <PerkListItem key={perk.id} {...perk} />
            ))}
          </div>
          {inactivePerks.length > 0 && (
            <>
              <div>
                <Typography>Inactive perks</Typography>
              </div>
              <div className="grid grid-cols-3 gap-4 gap-y-6">
                {inactivePerks.map((perk) => (
                  <PerkListItem key={perk.id} {...perk} inactive />
                ))}
              </div>
            </>
          )}
        </div>
      )}
    </Card>
  );
}
