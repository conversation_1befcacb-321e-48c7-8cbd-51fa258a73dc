import {
  ReactNode,
  createContext,
  useContext,
  useEffect,
  useState
} from 'react';
import { fetchBranding } from 'src/api/branding';
import { useAsync } from 'src/api/useApi';
import useClientData from 'src/hooks/useClient';
import { AuthContext } from './AuthContext';

type Props = {
  children: ReactNode;
};

export type Branding = {
  id: number;
  headings_font: string;
  paragraph_font: string;
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  text_color: string;
  link_color: string;
  logo: string;
  client: number;
};

export const BrandingContext = createContext<{
  branding: Branding | null;
  setBranding: React.Dispatch<React.SetStateAction<Branding | null>>;
  isLoading: boolean;
}>({
  branding: null,
  setBranding: () => {},
  isLoading: true
});

export const BrandingContextProvider = ({ children }: Props) => {
  const [branding, setBranding] = useState<Branding | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { run: doFetchBranding } = useAsync(fetchBranding);
  const { isLoggedIn } = useContext(AuthContext);
  const { clientData } = useClientData();

  useEffect(() => {
    if (!isLoggedIn) return;

    const isClientBrandingEmpty = clientData && !clientData.branding;

    if (isClientBrandingEmpty) {
      setIsLoading(false);

      return;
    }

    if (clientData?.branding) {
      doFetchBranding(clientData.branding)
        .then((res: Branding) => {
          setBranding(res);
        })
        .catch(() => {
          setBranding(null);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [clientData, isLoggedIn]);

  return (
    <BrandingContext.Provider
      value={{
        branding,
        setBranding,
        isLoading
      }}
    >
      {children}
    </BrandingContext.Provider>
  );
};
