import { useAsync } from 'src/api/useApi';
import { create, StoreApi, UseBoundStore } from 'zustand';
import { useShallow } from 'zustand/react/shallow';

type Store<T> = { isLoaded: boolean; data: T };
type Selector<T, R> = (state: T) => R;

export const createStore = <T,>(defaultData: T) =>
  create(() => ({ data: defaultData, isLoaded: false }));

export default function useStore<T>({
  store,
  fetchFn,
  fetchOnMount = true,
  fetchProps
}: {
  store: UseBoundStore<StoreApi<Store<T>>>;
  fetchFn: () => Promise<T>;
  fetchOnMount?: boolean;
  fetchProps?: any;
}) {
  const state = store(useShallow((state: Store<T>) => state.data));

  const isLoaded = store(useShallow((state: Store<T>) => state.isLoaded));

  const setState = (data: T) => store.setState({ data, isLoaded: true });

  const createSelector = <R,>(selector: Selector<Store<T>, R>) =>
    store(selector);

  const { run: fetch, isLoading } = useAsync(fetchFn, {
    fetchOnMount: fetchOnMount && !isLoaded,
    setToStore: setState,
    props: fetchProps
  });

  return {
    isLoading: isLoading && !isLoaded,
    fetch,
    state,
    setState,
    createSelector
  };
}
