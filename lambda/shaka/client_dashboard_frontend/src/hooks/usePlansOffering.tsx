import { fetchPlanOfferingSet } from 'src/api/planOfferingSet';
import { useAsync } from 'src/api/useApi';
import {
  selectorPlanComponentOfferingLoaded,
  setPlansOffering,
  usePlansOfferingStore
} from 'src/store/plansOfferings';

export default function usePlansOffering() {
  const isPlanOfferingLoaded = usePlansOfferingStore(
    selectorPlanComponentOfferingLoaded
  );

  const { isLoading } = useAsync(fetchPlanOfferingSet, {
    fetchOnMount: !isPlanOfferingLoaded,
    setToStore: setPlansOffering
  });

  return {
    isLoading: isLoading && !isPlanOfferingLoaded
  };
}
