@import url('https://fonts.googleapis.com/css2?family=Baloo+2:wght@400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Baloo 2', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color: white;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  min-height: 100%;

  background-image: radial-gradient(50% 350px at 50% 30px, rgba(52, 24, 58, 0.67) 35%, #073AFF00 100%), radial-gradient(35% 15% at 18% 103%, rgba(52, 24, 58, 0.67) 9%, #073AFF00 100%), linear-gradient(62deg, #0A0C16 63%, #12163B 108%);
  background-size: 100% 100%;


  --gradient-tooltip-1: linear-gradient(90deg, transparent 5%, rgb(25 27 46) 30%, rgb(25 27 46) 70%, transparent 95%);
  --gradient-tooltip-2: radial-gradient(49% 81% at 50% 50%, #0A0C16 0%, transparent 100%);
  --gradient-tooltip-3: radial-gradient(65% 84% at 2% -9%, #6441892B 0%, #FF000000 98%),radial-gradient(109% 112% at 7% 15%, #607BB524 1%, #FF000000 69%),radial-gradient(142% 91% at 111% 84%, #0e0f17 0%, #0e0f17 100%);
  --gradient-blue: linear-gradient(102.2deg, #607BB5 19%, #2E215C 100%);

  --gradient-pink: linear-gradient(90deg, #EF8DF8, #694288);
  --gradient-rainbow: linear-gradient(45deg, #C55BBA, #607BB5DB, transparent);
  --gradient-plan: linear-gradient(0deg, #5F80BA 55px, transparent 55px);
  --gradient-button: linear-gradient(253.21deg, #2E215C 13.83%, #567AB0 47.41%, #F18DFA 81%);
  --gradient-divider: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .07) 30%, rgba(255, 255, 255, 0.07) 70%, rgba(255, 255, 255, 0) 100%);

  --gradient-card-1: linear-gradient(180deg, transparent, rgb(112, 202, 253, 0.05)),
    linear-gradient(180deg,
      #151923, #151923);
  --gradient-card-2: radial-gradient(25% 25% at 68% 73%, #659cefa1 0%, #FFFCFC00 100%), radial-gradient(100% 100% at 71% 33%, #ef8df8cf 8%, #FFFFFF00 49%), radial-gradient(75% 75% at 24% 22%, #761f81 0%, #FFFFFF00 68%), radial-gradient(35% 35% at 27% 72%, #5f52f7b8 0%, #FFFFFF00 100%), linear-gradient(125deg, #d9d9d903 1%, #d9d9d903 100%);

  --gradient-card-3:
    radial-gradient(10% 120px at 80% 130px, #6441891f 0%, #2C101000 100%),
    radial-gradient(25% 100px at 100% 50px, rgba(255, 255, 255, 0.1) -10%, rgba(255, 255, 255, 0.04) 50%, #FFFFFf00 100%),
    radial-gradient(15% 350px at 45% 200px, #52ecf70d 0%, #FFFFFF00 100%),
    radial-gradient(10% 200px at 0% 250px, #ffffff0a 0%, #FF000000 100%),
    radial-gradient(25% 400px at 45% 1000px, #52ecf719 0%, #FFFFFF00 100%),
    radial-gradient(10% 150px at 0% 900px, #ffffff0a 0%, #FF000000 100%);

  --gradient-card-4:
    radial-gradient(20% 50% at 70% 30%, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, #FFFFFf00 150%),
    radial-gradient(10% 100px at 100% 100%, rgba(255, 255, 255, 0.08) 0, rgba(255, 255, 255, 0.05) 50%, #FFFFFf00 180%);


  --gradient-actions: linear-gradient(91.65deg, #3A4E8D -3.55%, #AB79D1 26.25%, #9A61A9 57.15%, #2C1638 104.33%);
  ;
  --gradient-subscriber: linear-gradient(141.56deg, #7C4B84 -3.81%, #5D4E99 52.7%, #354A73 128.75%);

  --gradient-bolton: radial-gradient(60% 50% at 50% 70%, #70CAFD4A 1%, #FF000000 99%), radial-gradient(140% 45% at 110% 40%, #13161fcc 0%, #13161fcc 100%);

}

.background-mystic-gradient {
  background: linear-gradient(251deg, #2e215c 7.93%, #567ab0 70.56%);
}

.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 20px;
  font-weight: 600;
}

.swiper-wrapper {
  align-items: stretch;
}

.swiper-slide {
  height: auto;
}

.radio {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

[type='checkbox'] {
  --tw-shadow: 0 0 #0000;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-origin: border-box;
  border-color: #6b7280;
  border-width: 1px;
  display: inline-block;
  flex-shrink: 0;
  height: 1rem;
  padding: 0;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  vertical-align: middle;
  width: 1rem;
  background-color: #fff;
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: 0.55em 0.55em;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3E%3Cpath stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M1 5.917 5.724 10.5 15 1.5'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-color: currentColor;
  background-size: 0.55em 0.55em;
  border-color: transparent;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

[type='checkbox'].light:checked {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg aria-hidden='true' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 12'%3E%3Cpath stroke='black' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M1 5.917 5.724 10.5 15 1.5'/%3E%3C/svg%3E");
}

.indicator {
  position: absolute;
  top: 0;
  height: 100%;
  transition: left 0.2s ease;
}

input[type="datetime-local"]::-webkit-calendar-picker-indicator,
input[type="date"]::-webkit-calendar-picker-indicator {
  color: transparent;
  background: transparent;
}


.select {
  position: relative;
}

.select:after {
  content: '';
  right: 20px;
  top: 16px;
  height: 10px;
  width: 10px;
  transform: rotate(45deg);
  border-right: 2px solid rgb(107, 114, 128);
  border-bottom: 2px solid rgb(107, 114, 128);
  position: absolute;
  pointer-events: none;
}

select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.action-link:after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: var(--gradient-actions);
  opacity: 0;
  z-index: -1;
  border-radius: 12px;
  transition: opacity 0.25s;
}

.action-link:hover:after {
  opacity: 1;
}

.tooltip {
  position: relative;
}

.tooltiptext {
  background-image: radial-gradient(100% 100% at 59% 0%, #607bb599 1%, #FF000000 99%), linear-gradient(270deg, #644776 0%, #383940 100%);
  color: #fff;
  text-align: center;
  border-radius: 16px;
  padding: 4px 12px;
  position: absolute;
  z-index: 1;
  top: 0;
  left: 50%;
  transform: translate(-50%, -150%);
  min-width: 50px;
}

.tooltiptext.near {
  transform: translate(-50%, -110%);
}

.tooltiptext.flat,
.tooltiptext.flat-white {
  background: #2b3039;
  border-radius: 10px;
}

.tooltiptext.flat span {
  color: #66E29F;
}

.tooltiptext.flat-white span {
  color: white;
}

.tooltiptext::after {
  content: "";
  position: absolute;
  top: 99%;
  left: 50%;
  margin-left: -8px;
  border-width: 8px;
  border-style: solid;
  border-color: #514261 transparent transparent transparent;
}

.tooltiptext.flat::after,
.tooltiptext.flat-white::after {
  border-color: #2b3039 transparent transparent transparent;
}


/* Chart */
.tooltiptext.tooltip-chart {
  position: initial;
  transform: translate(calc(-50% - 16px), -110%);
}

.tickLabel {
  fill: white !important;
  font-size: 12px !important;
  text-transform: uppercase;
  opacity: 0.65;
}

.Cursor div:first-of-type {
  background: transparent !important;
}

.Cursor div:first-of-type::after {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 100%;
  background: #95959520;
}

.table {
  border-collapse: separate;
  border-spacing: 0 7px;
  border-right: hidden;
}

thead::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, .15);
}

/* Hide number input arrows */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

/* otp */

.otp input {
  border-radius: 10px;
  width: 52px !important;
  height: 48px;
  background: transparent;
}

.otp input:focus {
  outline: solid 2px #EF8DF8;
}