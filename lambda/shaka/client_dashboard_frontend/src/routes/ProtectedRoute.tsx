import { useContext } from 'react';
import { Navigate } from 'react-router-dom';
import { ROUTES } from 'src/config/navigation';
import { AuthContext } from 'src/contexts/AuthContext';

const ProtectedRoute = ({ children }: any) => {
  const { isLoggedIn } = useContext(AuthContext);

  if (!isLoggedIn) {
    return <Navigate to={ROUTES.LOGIN} />;
  }

  return children;
};

export default ProtectedRoute;
