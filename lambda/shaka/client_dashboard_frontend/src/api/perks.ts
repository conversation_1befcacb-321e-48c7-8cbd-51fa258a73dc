import { Perk, PerkHistory, PerkSubscriber } from 'src/types/perks';
import api from '.';

export type PerkPayload = Omit<
  Perk,
  'redemptions_amount' | 'remaining_quantity' | 'total_cost'
>;

export const fetchPerks = (): Promise<Perk[]> =>
  api.get('/perks').then((res) => res.data);

export const deletePerk = (id: string): Promise<Perk[]> =>
  api.delete('/perks/' + id).then((res) => res.data);

export const createPerk = (payload: PerkPayload) =>
  api.post('/perks/', payload).then((res) => res.data);

export const updatePerk = (id: string | number, payload: PerkPayload) =>
  api.put(`/perks/${id}/`, payload).then((res) => res.data);

export const getPerkHistory = (id: string): Promise<PerkHistory[]> =>
  api.get(`/frontend/perks/${id}/history/`).then((res) => res.data);

export const getPerkSubscribers = (id: string): Promise<PerkSubscriber[]> =>
  api.get(`/frontend/perks/${id}/subscribers/`).then((res) => res.data);
