import { create } from 'zustand';
import { Campaign } from 'src/types/campaign';

type State = { campaigns: Campaign[]; isLoaded: boolean };
type Action = {
  setCampaigns: (campaigns: Campaign[]) => void;
};

type CampaignsStore = State & Action;

const useCampaignsStore = create<CampaignsStore>((set) => ({
  campaigns: [],
  isLoaded: false,
  setCampaigns: (campaigns: Campaign[]) =>
    set(() => ({ campaigns, isLoaded: true }))
}));

const selectorCampaigns = (state: CampaignsStore) => state.campaigns;
const selectorCampaignsLoaded = (state: CampaignsStore) => state.isLoaded;

const selectorCampaignById = (state: CampaignsStore) =>
  state.campaigns.reduce(
    (acc, campaign) => {
      acc[campaign.id] = campaign;
      return acc;
    },
    {} as Record<number, Campaign>
  );

const setCampaigns = (campaigns: Campaign[]) =>
  useCampaignsStore.setState(() => ({ campaigns, isLoaded: true }));

export {
  useCampaignsStore,
  selectorCampaigns,
  selectorCampaignById,
  selectorCampaignsLoaded,
  setCampaigns
};
