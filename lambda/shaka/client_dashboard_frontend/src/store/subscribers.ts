import { SortingState } from '@tanstack/react-table';
import { Subscriber } from 'src/types/subscribers';
import { create } from 'zustand';

type Subscribers = Record<number, Subscriber[]>;

type State = {
  subscribers: Subscribers;
  isLoaded: boolean;
  count: number;
  ordering: SortingState;
};

const useSubscribersStore = create<State>(() => ({
  subscribers: {},
  count: 0,
  ordering: [],
  isLoaded: false
}));

const selectorSubscribers = (state: State) => state.subscribers;
const selectorSubscribersCount = (state: State) => state.count;
const selectorSubscribersOrdering = (state: State) => state.ordering;
const selectorSubscribersLoaded = (state: State) => state.isLoaded;

const setSubscribers = (subscribers: Subscribers) =>
  useSubscribersStore.setState(() => ({ subscribers, isLoaded: true }));

const setSubscribersCount = (count: number) =>
  useSubscribersStore.setState(() => ({ count }));

const setSubscribersOrdering = (ordering: SortingState) =>
  useSubscribersStore.setState(() => ({ ordering }));

export {
  setSubscribers,
  selectorSubscribersCount,
  selectorSubscribersOrdering,
  useSubscribersStore,
  selectorSubscribers,
  setSubscribersCount,
  setSubscribersOrdering,
  selectorSubscribersLoaded
};
