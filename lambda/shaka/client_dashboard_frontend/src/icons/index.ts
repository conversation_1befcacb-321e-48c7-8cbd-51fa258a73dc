import ArrowDownCircle from './ArrowDownCircle';
import <PERSON><PERSON>hart from './BarChart';
import Branding from './Branding';
import Bolt from './Bolt';
import Code from './Code';
import Home from './Home';
import People from './People';
import Phone from './Phone';
import <PERSON><PERSON><PERSON> from './PieChart';
import Profile from './Profile';
import Radar from './Radar';
import Spanner from './Spanner';
import Image from './Image';
import ShareGradientIcon from './ShareGradient';
import ShareIcon from './Share';
import Present from './Present';
import Export from './Export';
import Campaign from './Campaign';
import Sms from './Sms';
import Navigation from './Navigation';

export default {
  ArrowDownCircle,
  BarChart,
  Branding,
  Bolt,
  Code,
  Home,
  People,
  Phone,
  PieChart,
  Profile,
  Radar,
  Spanner,
  Image,
  ShareGradientIcon,
  ShareIcon,
  Present,
  Export,
  Campaign,
  Sms,
  Navigation
} as Record<string, (props: any) => JSX.Element>;
