type Props = {
  fill?: string;
};

export default function RadarIcon({ fill = '#9698AB' }: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <g clipPath="url(#clip0_747_1833)">
        <path
          d="M5.09666 16.7392C4.03335 15.9666 3.16817 14.9531 2.57198 13.7818C1.97579 12.6104 1.66555 11.3144 1.66666 10.0001C1.66666 5.39758 5.39749 1.66675 9.99999 1.66675C14.6025 1.66675 18.3333 5.39758 18.3333 10.0001C18.3344 11.3144 18.0242 12.6104 17.428 13.7818C16.8318 14.9531 15.9666 15.9666 14.9033 16.7392L14.0575 15.2901C15.1628 14.4422 15.9748 13.2694 16.3793 11.9364C16.7839 10.6034 16.7607 9.17714 16.313 7.85797C15.8654 6.53881 15.0157 5.39302 13.8834 4.58155C12.7511 3.77008 11.393 3.33369 9.99999 3.33369C8.60694 3.33369 7.24888 3.77008 6.11658 4.58155C4.98427 5.39302 4.13461 6.53881 3.68694 7.85797C3.23927 9.17714 3.21609 10.6034 3.62064 11.9364C4.02519 13.2694 4.83716 14.4422 5.94249 15.2901L5.09666 16.7392ZM6.79166 13.8351C6.00418 13.1763 5.43861 12.291 5.17179 11.2996C4.90496 10.3081 4.94983 9.25856 5.30028 8.2935C5.65073 7.32844 6.28979 6.49466 7.13061 5.90545C7.97144 5.31624 8.97327 5.00016 9.99999 5.00016C11.0267 5.00016 12.0285 5.31624 12.8694 5.90545C13.7102 6.49466 14.3493 7.32844 14.6997 8.2935C15.0502 9.25856 15.095 10.3081 14.8282 11.2996C14.5614 12.291 13.9958 13.1763 13.2083 13.8351L12.35 12.3634C12.8182 11.898 13.1376 11.3041 13.2678 10.657C13.398 10.0098 13.3332 9.3386 13.0815 8.72836C12.8298 8.11812 12.4025 7.59635 11.854 7.2292C11.3054 6.86205 10.6601 6.66606 9.99999 6.66606C9.33988 6.66606 8.69462 6.86205 8.14603 7.2292C7.59744 7.59635 7.17021 8.11812 6.9185 8.72836C6.66679 9.3386 6.60194 10.0098 6.73217 10.657C6.86239 11.3041 7.18183 11.898 7.64999 12.3634L6.79166 13.8351ZM9.16666 10.8334H10.8333L11.6667 18.3334H8.33333L9.16666 10.8334Z"
          fill={fill}
        />
      </g>
      <defs>
        <clipPath id="clip0_747_1833">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
