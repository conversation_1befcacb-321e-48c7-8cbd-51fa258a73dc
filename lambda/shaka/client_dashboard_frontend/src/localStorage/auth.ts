import { jwtDecode } from 'jwt-decode';
import { LoginData } from 'src/types/auth';

export const getExpiresAt = (token: string) => {
  const userData = jwtDecode(token);
  const expirySeconds = userData.exp || new Date().getTime() / 1000;

  return (expirySeconds * 1000).toString();
};

const setAuthSessionKeys = (data: LoginData) => {
  const expiresAt = getExpiresAt(data.access_token);

  localStorage.setItem('id_token', data.id_token);
  localStorage.setItem('access_token', data.access_token);
  localStorage.setItem('refresh_token', data.refresh_token);
  localStorage.setItem('expires_at', expiresAt);
};

const setAccessTokenKey = (accessToken: string) => {
  localStorage.setItem('access_token', accessToken);
};

const getAccessTokenKay = () => {
  return localStorage.getItem('access_token');
};

const getSessionKeys = () => {
  return {
    idToken: localStorage.getItem('id_token'),
    accessToken: localStorage.getItem('access_token'),
    refreshToken: localStorage.getItem('refresh_token'),
    expiresAt: localStorage.getItem('expires_at')
  };
};

const deleteAuthSessionKeys = () => {
  localStorage.removeItem('id_token');
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('expires_at');
};

export {
  getSessionKeys,
  setAuthSessionKeys,
  setAccessTokenKey,
  getAccessTokenKay,
  deleteAuthSessionKeys
};
