import logging
import os
import pathlib
import tempfile
from datetime import datetime

import boto3
from paramiko import SFTPClient, Transport

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def is_cdr_file(potential_filename):
    return potential_filename.startswith('cdr') and potential_filename.endswith('.csv.gz')

def key_to_key_prefix(key):
    return '/'.join(key.split('/')[:-1])

def key_to_filename(key):
    return key.split('/')[-1]

def cdr_filename_to_key(cdr_filename):
    without_extension = cdr_filename.split('.')[0]
    _, data_type, __, _, _, _, _, date_portion = without_extension.split('_')
    date = datetime.strptime(date_portion, '%Y%m%d%H%M%S')
    return f'{data_type.lower()}/{date.year}/{date.month:02}/{date.day:02}/{cdr_filename}'

def lambda_handler(_, __):
    sftp_dir = pathlib.Path(os.environ['SFTP_DIR'])
    s3_bucket_name = os.environ['S3_BUCKET_NAME']

    s3 = boto3.resource('s3')
    bucket = s3.Bucket(s3_bucket_name)

    already_copied_objects = {}

    def has_already_uploaded(potential_cdr_file):
        key_prefix = key_to_key_prefix(cdr_filename_to_key(potential_cdr_file))
        if key_prefix not in already_copied_objects:
            already_copied_objects[key_prefix] = {key_to_filename(obj.key) for obj in bucket.objects.filter(Prefix=key_prefix)}
            logger.info('Fetched existing %s', already_copied_objects[key_prefix])
        return potential_cdr_file in already_copied_objects[key_prefix]

  # use the filename to open a file for writing, or run a os.system() command, etc.

    with tempfile.TemporaryDirectory() as tempdir:
        def copy_cdr_file_to_s3(folder, cdr_file):
            local_filename = pathlib.Path(tempdir) / cdr_file
            sftp.get(str(sftp_dir / folder / cdr_file), local_filename)
            with open(local_filename, 'rb') as f:
                bucket.put_object(Key=cdr_filename_to_key(cdr_file), Body=f)
            os.remove(local_filename)

        with Transport((os.environ['SFTP_HOST'], int(os.environ['SFTP_PORT']))) as transport:
            transport.connect(username=os.environ['SFTP_USERNAME'], password=os.environ['SFTP_PASSWORD'])
            with SFTPClient.from_transport(transport) as sftp:
                for folder in ['data', 'sms', 'voice']:
                    logger.debug('Checking %s', folder)
                    remote_files = sftp.listdir(str(sftp_dir / folder))
                    for potential_cdr_file in remote_files:
                        if is_cdr_file(potential_cdr_file):
                            if not has_already_uploaded(potential_cdr_file):
                                logger.info('Copying %s', potential_cdr_file)
                                copy_cdr_file_to_s3(folder, potential_cdr_file)
                            else:
                                logger.info('Not copying %s', potential_cdr_file)

    # # Filter only .gz files in the remote directory
    # gz_files = [file_name for file_name in remote_files if file_name.lower().endswith('.gz')]

    # for file_name in gz_files:
    #     print(file_name)
