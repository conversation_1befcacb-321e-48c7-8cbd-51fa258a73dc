interface SelectableCardProps {
  itemId: string | number;
  currentSelectedId: string | number | null;
  onSelectionChange: (itemId: string | number) => void;
  value: number;
  price: number | string;
  className?: string;
  selectedClassName?: string;
}

export function SelectableCard({
  itemId,
  currentSelectedId,
  onSelectionChange,
  value,
  price,
  className = 'border-border cursor-pointer rounded border px-4 py-2 text-center hover:bg-[#f3f3f3]',
  selectedClassName = 'outline-primary outline-[1px]'
}: SelectableCardProps) {
  const isSelected = currentSelectedId === itemId;

  const handleCardClick = () => {
    onSelectionChange(itemId);
  };

  const combinedClassName = `${className} ${isSelected ? selectedClassName : ''}`;

  return (
    <button
      type="button"
      onClick={handleCardClick}
      className={combinedClassName}
      aria-pressed={isSelected}
    >
      <div className="text-[18px] font-bold">{value}GB data</div>
      <div>£{price}</div>
    </button>
  );
}
