import React, { useState, useEffect } from 'react';
import { CloseIcon, ErrorIcon, InfoIcon } from '@/icons/icons';

const alertConfig = {
  error: {
    icon: <ErrorIcon />,
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    textColor: 'text-red-800',
    iconColor: 'text-red-500',
    errorMsgColor: 'text-red-800',
    role: 'alert',
    ariaLabel: 'Error messages'
  },
  warning: {
    icon: <ErrorIcon />,
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    textColor: 'text-yellow-800',
    iconColor: 'text-yellow-500',
    role: 'alert',
    ariaLabel: 'Warning messages'
  },
  success: {
    icon: <InfoIcon />,
    bgColor: 'bg-mint-subtle',
    borderColor: 'border-success-border',
    textColor: 'text-primary',
    iconColor: 'text-primary',
    errorMsgColor: 'text-primary',
    role: 'status',
    ariaLabel: 'Success messages'
  },
  info: {
    icon: <ErrorIcon />,
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    textColor: 'text-blue-800',
    iconColor: 'text-blue-500',
    role: 'status',
    ariaLabel: 'Information messages'
  }
};

export function FormAlert({
  messages,
  type = 'error',
  title,
  dismissible = true,
  onDismiss = () => {},
  autoHide = false,
  autoHideDelay = 5000,
  className = ''
}) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (autoHide && messages.length > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoHideDelay);
      return () => clearTimeout(timer);
    }
  }, [autoHide, autoHideDelay, messages]);

  const handleDismiss = () => {
    setIsVisible(false);
    if (onDismiss) onDismiss();
  };

  if (!isVisible || messages.length === 0) return null;

  const config = alertConfig[type];
  const singleErrorMessage = messages[0].split(':')[1];

  return (
    <div
      role={config.role}
      aria-live={
        type === 'error' || type === 'warning' ? 'assertive' : 'polite'
      }
      aria-label={config.ariaLabel}
      className={`${config.bgColor} ${config.borderColor} ${config.textColor} rounded-lg border p-2 lg:p-4 ${className}`}
    >
      <div className="flex items-start gap-3">
        <div className="hidden lg:block">{config.icon}</div>
        <div className="min-w-0 flex-1">
          {title && <h3 className="mb-2 font-medium lg:text-sm">{title}</h3>}
          {messages.length === 1 ? (
            <ul className="text-default">
              {/*<ul className="text-xxxs list-inside list-disc">*/}
              <li className={`leading-5 text-balance ${config.errorMsgColor}`}>
                {singleErrorMessage}
              </li>
            </ul>
          ) : (
            <ul className="text-xxxs space-y-1 text-pretty">
              {messages.map((error: string, index: number) => {
                const message = error.split(':')[1];
                return (
                  <li key={index} className="leading-5">
                    {message}
                  </li>
                );
              })}
            </ul>
          )}
        </div>
        {dismissible && (
          <button
            onClick={handleDismiss}
            className={`${config.iconColor} -m-1 cursor-pointer rounded p-1 transition-opacity hover:opacity-75 focus:ring-2 focus:ring-current focus:ring-offset-1 focus:outline-none`}
            aria-label="Dismiss alert"
          >
            <CloseIcon className="h-4 w-4" />
          </button>
        )}
      </div>
    </div>
  );
}
