'use client';

import React from 'react';
import { Appearance, loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';
import {
  EmbeddedCheckoutProvider,
  EmbeddedCheckout
} from '@stripe/react-stripe-js';
import { getClientConfig } from '@/client-config/client-config';
import { StripeMode } from './types';

interface StripeProviderProps extends React.PropsWithChildren {
  clientSecret: string;
  mode: StripeMode;
  options?: {
    appearance?: Appearance;
    returnUrl?: string;
  };
}

const stripePromise = loadStripe(getClientConfig().stripePublicKey);

export function StripeProvider({
  children,
  clientSecret,
  mode,
  options
}: StripeProviderProps) {
  if (mode === 'embedded') {
    return (
      <EmbeddedCheckoutProvider
        stripe={stripePromise}
        options={{
          clientSecret,
          ...options
        }}
      >
        {children || <EmbeddedCheckout />}
      </EmbeddedCheckoutProvider>
    );
  }

  return (
    <Elements
      stripe={stripePromise}
      options={{
        clientSecret,
        ...options
      }}
    >
      {children}
    </Elements>
  );
}
