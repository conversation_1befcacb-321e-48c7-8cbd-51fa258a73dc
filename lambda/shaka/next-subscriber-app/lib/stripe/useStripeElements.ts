'use client';

import { useState } from 'react';
import { Stripe, PaymentIntent, StripeElements } from '@stripe/stripe-js';
import { StripeError } from '@stripe/stripe-js';
import { API_ENDPOINTS } from '@/auth/api/endpoints';

interface UseStripeElementsProps {
  stripe: Stripe | null;
  elements: StripeElements | null;
  clientSecret: string;
  onSuccess?: (paymentIntent: PaymentIntent) => void;
  onError?: (error: any) => void;
  returnUrl?: typeof API_ENDPOINTS.stripe.successUrl;
}

export function useStripeElements({
  stripe,
  elements,
  clientSecret,
  onSuccess,
  returnUrl,
  onError
}: UseStripeElementsProps) {
  // react query !!
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<StripeError | string | null>(null);

  const confirmPayment = async () => {
    if (!stripe || !elements) return;

    setLoading(true);
    setError(null);

    // try {
    // const result = await stripe.confirmPayment({
    //   elements,
    //   confirmParams: { return_url: window.location.href },
    //   clientSecret
    // });

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: returnUrl
      }
    });

    // if (result.error) {
    //   setError(result.error);
    //   onError?.(result.error);
    // } else if (result.paymentIntent) {
    //   onSuccess?.(result.paymentIntent);
    // }
    // } catch (error: StripeError | unknown) {
    // if (error instanceof AxiosError) {
    //   setError(error.response?.data?.message);
    //   onError?.(error);
    // }

    if (error.type === 'card_error' || error.type === 'validation_error') {
      onError?.(error);
      setError(error.message);
    } else {
      onError?.(error);
      setError('An unexpected error occurred.');
    }

    setLoading(false);

    // } finally {
    //   setLoading(false);
    // }
  };

  return { confirmPayment, loading, error };
}
