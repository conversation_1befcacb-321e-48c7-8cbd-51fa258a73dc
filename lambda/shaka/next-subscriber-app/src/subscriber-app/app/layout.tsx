import React from 'react';
import { Metada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from 'next/font/google';
import { headers } from 'next/headers';
import './globals.css';
import { getClientConfig } from '@/client-config/client-config';
import { AuthProvider } from '@/auth/context/auth-context';
import { clientConfig } from '@/src/subscriber-app/clients/active/client-config';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin']
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin']
});

export async function generateMetadata() {
  const dynamicConfig = clientConfig;
  const config = getClientConfig();
  const headersList = headers();
  const list = await headersList;
  const host = list.get('host');
  const protocol = list.get('x-forwarded-proto') || 'https';
  const siteUrl = `${protocol}://${host}`;

  const metadata: Metadata = {
    title: dynamicConfig.title,
    description: dynamicConfig.metaDescription,
    keywords: [],
    robots: 'noindex, nofollow',
    openGraph: {
      title: config.name,
      description: '',
      url: siteUrl,
      siteName: '',
      images: [
        {
          url: `${siteUrl}/images/og-image.jpg`,
          width: 800,
          height: 600
        }
      ],
      type: 'website'
    }
  };

  return metadata;
}

// load from config ????
// const GA_TRACKING_ID = process.env.NEXT_GOOGLE_TRACKING_ID || "";

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {/*<Script*/}
        {/*  strategy="afterInteractive"*/}
        {/*  src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}*/}
        {/*/>*/}
        {/*<Script id="google-analytics" strategy="afterInteractive">*/}
        {/*  {`*/}
        {/*      window.dataLayer = window.dataLayer || [];*/}
        {/*      function gtag(){dataLayer.push(arguments);}*/}
        {/*      gtag('js', new Date());*/}
        {/*      gtag('config', '${GA_TRACKING_ID}', {*/}
        {/*        page_path: window.location.pathname,*/}
        {/*      });*/}
        {/*    `}*/}
        {/*</Script>*/}
        <AuthProvider>{children}</AuthProvider>
      </body>
    </html>
  );
}
