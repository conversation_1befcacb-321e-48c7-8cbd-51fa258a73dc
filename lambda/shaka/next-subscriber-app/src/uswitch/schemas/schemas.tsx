import { z } from 'zod';
import { UK_PUBLIC_HOLIDAYS } from '@/utils/constants';

export const pacCodeSchema = z
  .string()
  .trim()
  .refine((value) => /^PAC\d{6}$/i.test(value), {
    message:
      'PAC code must be in the format PAC followed by 6 digits (e.g., PAC123456)'
  });

export const ukPhoneNumberSchema = z
  .string()
  .trim()
  .refine(
    (value) => {
      const cleanedNumber = value.replace(/[\s\-()]/g, '');

      // Check for UK mobile format (07XXXXXXXXX)
      const isMobile = /^(07\d{9})$/.test(cleanedNumber);

      // Check for international format (+447XXXXXXXX)
      const isInternational = /^(\+44(7)\d{9})$/.test(cleanedNumber);

      return isMobile || isInternational;
    },
    {
      message:
        'Please enter a valid UK mobile (07XXXXXXXXX) or international number (+447XXXXXXXX)'
    }
  );

// Function to check if a date is a valid switching date
// (not a weekend or UK public holiday)
export const isValidPortingDate = (date: Date): boolean => {
  // Check if it's a weekend
  const day = date.getDay();
  if (day === 0 || day === 6) {
    return false; // Weekend (Sunday = 0, Saturday = 6)
  }

  // Format date as YYYY-MM-DD for holiday checking
  const formattedDate = date.toISOString().split('T')[0];

  // Check if it's a public holiday
  return !UK_PUBLIC_HOLIDAYS.includes(formattedDate);
};

const isEmptyOrUndefined = (value: string | undefined): boolean => {
  return value === undefined || value === null || value === '';
};

// Complete form schema for number porting
export const numberPortingSchema = z
  .object({
    pacCode: pacCodeSchema,
    phoneNumber: ukPhoneNumberSchema,
    // Switching date is optional but if provided must be valid
    day: z.string().optional(),
    month: z.string().optional(),
    year: z.string().optional()
  })
  .superRefine((data, ctx) => {
    // Check if any date field has a non-empty value
    const hasDay = !isEmptyOrUndefined(data.day);
    const hasMonth = !isEmptyOrUndefined(data.month);
    const hasYear = !isEmptyOrUndefined(data.year);

    // Only validate date fields if at least one has a non-empty value
    const hasAnyDateField = hasDay || hasMonth || hasYear;

    if (hasAnyDateField) {
      // If any date field is provided with a value, all must be provided with values
      if (!hasDay) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Day is required when providing a switching date',
          path: ['day']
        });
      }

      if (!hasMonth) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Month is required when providing a switching date',
          path: ['month']
        });
      }

      if (!hasYear) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Year is required when providing a switching date',
          path: ['year']
        });
      }

      // Only validate the date if all fields have values
      if (hasDay && hasMonth && hasYear) {
        try {
          const day = parseInt(data.day!, 10);
          const month = parseInt(data.month!, 10) - 1; // JS months are 0-indexed
          const year = parseInt(data.year!, 10);

          const date = new Date(year, month, day);

          // Check if the date is valid
          const isValidDate =
            date.getFullYear() === year &&
            date.getMonth() === month &&
            date.getDate() === day;

          if (!isValidDate) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Please select a valid date',
              path: ['day']
            });
          } else if (!isValidPortingDate(date)) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message:
                'Please select a valid switching date (must be a business day)',
              path: ['day']
            });
          }
        } catch (e) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Please select a valid date',
            path: ['day']
          });
        }
      }
    }
  });

export type NumberPortingFormData = z.infer<typeof numberPortingSchema>;

export const validateNumberPortingForm = (
  formData: unknown
): {
  success: boolean;
  data?: NumberPortingFormData;
  error?: z.ZodError;
} => {
  try {
    // Clean up empty string values for date fields
    if (typeof formData === 'object' && formData !== null) {
      const data = formData as Record<string, unknown>;

      // Convert empty strings to undefined for optional fields
      if (data.day === '') data.day = undefined;
      if (data.month === '') data.month = undefined;
      if (data.year === '') data.year = undefined;
    }

    const validatedData = numberPortingSchema.parse(formData);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, error };
    }
    throw error;
  }
};
