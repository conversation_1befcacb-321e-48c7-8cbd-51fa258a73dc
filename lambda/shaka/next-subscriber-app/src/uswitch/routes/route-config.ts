// EXAMPLE
export const ROUTES_CONFIG = {
  login: {
    path: '/login',
    name: 'Login'
  },
  signup: {
    path: '/signup',
    name: 'Signup'
  },
  'successful-payment': {
    path: '/signup/payment/success',
    name: 'Success'
  },
  'payment-error': {
    path: '/signup/payment/failure',
    name: '<PERSON>rror'
  },
  dashboard: {
    path: '/',
    name: 'Dashboard'
  },
  'plan-selection': {
    path: '/signup/plan-selection',
    name: 'Explore Plans'
  },
  'number-porting': {
    path: '/signup/number-porting',
    name: 'Number Porting'
  },
  payment: {
    path: '/signup/payment',
    name: 'Payment'
  }
} as const;

export type Route = (typeof ROUTES_CONFIG)[keyof typeof ROUTES_CONFIG]['path'];
