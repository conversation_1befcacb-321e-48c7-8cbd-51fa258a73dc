'use client';

import { StripeReturn } from '@/components/stripe-return/stripe-return';
import { ROUTES_CONFIG } from '@/src/uswitch/routes/route-config';

export default function PaymentReturnPage() {
  // ideally paraller route
  return (
    <StripeReturn
      successRedirectUrl={ROUTES_CONFIG['successful-payment'].path}
      errorRedirectUrl={ROUTES_CONFIG['payment-error'].path}
      loadingComponent={
        <div className="mx-auto max-w-md p-6 text-center">
          <h2 className="mb-4 text-xl font-semibold">
            Processing your payment
          </h2>
          <p>Please wait while we confirm your payment...</p>
        </div>
      }
    />
  );
}
