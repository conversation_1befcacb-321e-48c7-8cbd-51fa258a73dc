'use client';

import { ConditionalWrapper } from '@/src/uswitch/app/signup/_components/conditional-wrapper/conditional-wrapper';
import React, { Fragment } from 'react';
import { FormAlert } from '@/components/dismissable-alert/dismissable-alert';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import {
  AfterPaymentOrderSummary,
  OrderSummaryDesktop,
  OrderSummaryRow,
  TotalCost
} from '@/src/uswitch/app/signup/_components/order-summary/order-summary';
import { FullDetailsButton } from '@/src/uswitch/app/signup/_components/full-details-button/full-details-button';
import Modal from '@/components/modal/modal';
import { CameraIcon, CloseIcon, SimIcon, WifiIcon } from '@/icons/icons';
import Image from 'next/image';
import androidIcon from 'public/images/android-icon.png';
import appleIcon from 'public/images/apple-icon.png';

const email = '<EMAIL>';

export default function SuccessPage() {
  // order summary:
  // how to get all the details ?
  // Pay btn that triggers router ? LS ? Server ?
  return (
    // <RequireAuth>
    <div className="mx-auto max-w-[827px]">
      <div className="flex flex-col gap-2">
        <ConditionalWrapper className="px-6 pt-6 lg:p-6">
          <h1 className="mb-4 lg:mb-0">Order confirmed</h1>
          <FormAlert
            className="mb-4 lg:my-6"
            title=""
            type="success"
            messages={[
              `x:We’ve sent a confirmation to ${email}. If you don't hear from us shortly, please check your spam folder.`
            ]}
            dismissible={false}
          />
          <h2 className="mb-4">Your eSIM QR code</h2>
          <div className="flex flex-wrap items-center gap-4 lg:gap-8">
            <div className="border-success-border w-full rounded-lg border-2 p-6 lg:h-[227px] lg:w-[227px]">
              QR CODE HERE
            </div>
            {/*COMP*/}
            <ul className="text-xxs flex flex-col gap-4">
              <li className="flex items-center gap-4">
                <WifiIcon />
                <p className="font-bold text-balance opacity-100">
                  Make sure you are connected to the internet
                </p>
              </li>
              <li className="flex items-center gap-4">
                <CameraIcon />
                <p className="font-bold opacity-100">
                  Scan the QR code with your phone
                </p>
              </li>
              <li className="flex items-center gap-4">
                <SimIcon />
                <p className="font-bold opacity-100">
                  Click “Add eSIM” and finish the set-up process
                </p>
              </li>
            </ul>
            {/*COMP*/}
          </div>
          <Divider className="my-6" />
          {/*COMP*/}

          <ESIMInstallation />
          {/*COMP*/}
        </ConditionalWrapper>

        <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />

        <ConditionalWrapper className="px-6 lg:my-4 lg:p-6">
          <h2 className="mb-6">Need some help?</h2>
          <HelpSection />
        </ConditionalWrapper>

        <Divider className="mx-auto block w-[calc(100%-48px)] lg:hidden" />

        <ConditionalWrapper className="px-6 lg:p-6">
          <AfterPaymentOrderSummary orderItems={[]} cardLastThreeDigits={123} />

          <Divider />
          <div className="my-4">
            <p className="text-xxxs inline opacity-100">Full </p>
            <FullDetailsButton
              className="text-xxxs"
              text="terms and conditions"
            >
              {({ isOpen, setIsOpen }) =>
                isOpen && (
                  <Modal onOpenChange={setIsOpen} open={isOpen}>
                    <Modal.Overlay />
                    <Modal.Content className="w-full rounded-lg p-6 lg:max-w-2xl">
                      <div className="mb-4 flex justify-end">
                        <Modal.Close>
                          <CloseIcon />
                        </Modal.Close>
                      </div>
                      <Modal.Title className="mb-6 text-xl font-semibold">
                        hello
                      </Modal.Title>
                      <Modal.Description>hi</Modal.Description>
                    </Modal.Content>
                  </Modal>
                )
              }
            </FullDetailsButton>
            <p className="text-xxxs inline opacity-100"> apply.</p>
          </div>
        </ConditionalWrapper>
      </div>
    </div>
    // </RequireAuth>
  );
}

function HelpSection() {
  return (
    <div className="flex flex-wrap gap-6 lg:gap-8">
      <HelpSectionCard
        title="Email support"
        status="available"
        text="Responds within around 10 minutes"
        email="<EMAIL>"
        buttonText="Email us"
      />
      <HelpSectionCard
        title="Online chat"
        status="available"
        text="Responds within around 5 minutes"
        buttonText="Chat with us"
      />
    </div>
  );
}

interface HelpSectionCardProps {
  title: string;
  status: 'available' | 'x';
  text: string;
  email?: string;
  buttonText: string;
  onClick?: () => void;
}

const helpSectionCardStatus = {
  available: {
    color: 'bg-success-border',
    text: 'Available'
  }
};

function HelpSectionCard({
  title,
  status,
  text,
  email,
  buttonText,
  onClick
}: HelpSectionCardProps) {
  const config = helpSectionCardStatus[status];
  return (
    <div className="lg:basis-[47%]">
      <h3 className="mb-4">{title}</h3>
      <div className="bg-gray-subtle-tint/60 rounded-2 flex flex-col gap-4 p-4">
        {/*is this status comes from api ?*/}
        <div className="flex items-center gap-2">
          <div className={`h-3 w-3 rounded-full ${config.color}`}></div>
          <h3 className="font-bold">{config.text}</h3>
        </div>

        <div className="bg-lemon border-gray-subtle-tint w-fit rounded border p-[6px] font-bold">
          {text}
        </div>
        <Divider className="mt-1! mb-2! lg:-mt-2! lg:mb-2!" />
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-6">
          {email ? <p className="text-default">{email}</p> : <div />}
          {email ? (
            <a
              href="mailto:<EMAIL>"
              className="hover:bg-gray-subtle text-default basis-xs cursor-pointer rounded-[2px] border-2 bg-white p-3 text-center font-bold"
            >
              {buttonText}
            </a>
          ) : (
            <button
              onClick={onClick}
              className="hover:bg-gray-subtle basis-xs cursor-pointer rounded-[2px] border-2 bg-white p-3 font-bold"
            >
              {buttonText}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}

// TODO: once the design is available create a component
const phoneOperatingSystems = {
  android: 'android',
  iOS: 'apple'
} as const;

type PhoneOS =
  (typeof phoneOperatingSystems)[keyof typeof phoneOperatingSystems];

function ESIMInstallation() {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [instructionType, setInstructionType] = React.useState<PhoneOS | null>(
    null
  );

  const handleAppleClick = () => {
    setInstructionType(phoneOperatingSystems.iOS);
    setIsModalOpen(true);
  };

  const handleAndroidClick = () => {
    setInstructionType(phoneOperatingSystems.android);
    setIsModalOpen(true);
  };

  const handleModalClose = (open: boolean) => {
    setIsModalOpen(open);
    if (!open) {
      setInstructionType(null);
    }
  };

  return (
    <>
      <div className="flex flex-wrap gap-4">
        <div className="lg:basis-[40%]">
          <h3 className="mb-2">Video instructions</h3>
          <p>
            Watch a brief instructional video to help you get your new eSIM
            installed
          </p>
        </div>
        <div className="flex flex-wrap items-end gap-4 lg:ml-auto">
          <button
            onClick={handleAppleClick}
            className="hover:bg-gray-subtle flex grow cursor-pointer items-center justify-center gap-2 rounded-[2px] border-2 p-3"
          >
            <Image width={20} height={20} src={appleIcon} alt="Apple icon" />
            <span>iPhone instructions</span>
          </button>
          <button
            onClick={handleAndroidClick}
            className="hover:bg-gray-subtle flex grow cursor-pointer items-center justify-center gap-2 rounded-[2px] border-2 p-3"
          >
            <Image
              width={23}
              height={23}
              src={androidIcon}
              alt="Android icon"
            />
            <span>Android instructions</span>
          </button>
        </div>
      </div>

      {isModalOpen && (
        <Modal onOpenChange={handleModalClose} open={isModalOpen}>
          <Modal.Overlay />
          <Modal.Content className="w-full rounded-lg p-6 lg:max-w-4xl">
            <div className="mb-4 flex justify-end">
              <Modal.Close>
                <CloseIcon />
              </Modal.Close>
            </div>
            <Modal.Title className="mb-6 text-xl font-semibold">
              {instructionType === phoneOperatingSystems.iOS
                ? 'iPhone eSIM Installation'
                : 'Android eSIM Installation'}
            </Modal.Title>
            <Modal.Description>
              {instructionType === phoneOperatingSystems.iOS ? (
                <AppleInstructionContent />
              ) : (
                <AndroidInstructionContent />
              )}
            </Modal.Description>
          </Modal.Content>
        </Modal>
      )}
    </>
  );
}

function AppleInstructionContent() {
  return <>Apple eSIM Installation Video</>;
}

function AndroidInstructionContent() {
  return <>Android eSIM Installation Video</>;
}
