import { PlanState } from '@/src/uswitch/utils/constants';

export const ACTIONS = {
  INCREMENT_BASIC_PLAN: 'INCREMENT_BASIC_PLAN',
  DECREMENT_BASIC_PLAN: 'DECREMENT_BASIC_PLAN',
  INCREMENT_FAMILY_PLAN: 'INCREMENT_FAMILY_PLAN',
  DECREMENT_FAMILY_PLAN: 'DECREMENT_FAMILY_PLAN',
  SET_SELECTED_ROAMING_PACKAGE: 'SET_SELECTED_ROAMING_PACKAGE'
} as const;

type ActionType =
  | { type: typeof ACTIONS.INCREMENT_BASIC_PLAN }
  | { type: typeof ACTIONS.DECREMENT_BASIC_PLAN }
  | { type: typeof ACTIONS.INCREMENT_FAMILY_PLAN }
  | { type: typeof ACTIONS.DECREMENT_FAMILY_PLAN }
  | { type: typeof ACTIONS.SET_SELECTED_ROAMING_PACKAGE; payload: number };

export const initialState: PlanState = {
  basicPlanQuantity: 1,
  bigFamilyPlanQuantity: 0,
  selectedRoamingPackageId: 0
};

export function planReducer(state: PlanState, action: ActionType): PlanState {
  switch (action.type) {
    case ACTIONS.INCREMENT_BASIC_PLAN:
      return {
        ...state,
        basicPlanQuantity: state.basicPlanQuantity + 1
      };
    case ACTIONS.DECREMENT_BASIC_PLAN:
      return {
        ...state,
        basicPlanQuantity:
          state.basicPlanQuantity > 1 ? state.basicPlanQuantity - 1 : 1
      };
    case ACTIONS.INCREMENT_FAMILY_PLAN:
      return {
        ...state,
        bigFamilyPlanQuantity:
          state.bigFamilyPlanQuantity < 5 ? state.bigFamilyPlanQuantity + 1 : 5
      };
    case ACTIONS.DECREMENT_FAMILY_PLAN:
      return {
        ...state,
        bigFamilyPlanQuantity:
          state.bigFamilyPlanQuantity > 0 ? state.bigFamilyPlanQuantity - 1 : 0
      };
    case ACTIONS.SET_SELECTED_ROAMING_PACKAGE:
      return {
        ...state,
        selectedRoamingPackageId: action.payload
      };
    default:
      return state;
  }
}
