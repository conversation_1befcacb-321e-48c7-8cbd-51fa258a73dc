import React, { Fragment, useState } from 'react';
import { Divider } from '@/src/uswitch/app/signup/_components/divider/divider';
import { PlainCard } from '@/components/plain-card/plain-card';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import {
  OrderItemWithQuantity,
  PlanState
} from '@/src/uswitch/utils/constants';
import { createOrderSummary } from '@/src/uswitch/utils/helpers';
import { ChevronDown, ChevronUp } from '@/icons/icons';

// same pattern in layout instead of duplication
// make sure item is removed from list when quantity is 0 !!!
interface OrderSummaryProps {
  state: PlanState;
}

export function OrderSummary({ state }: OrderSummaryProps) {
  const isMobile = useMediaQuery(1024);
  const { orderItems, totalCost } = createOrderSummary(state);

  return isMobile ? (
    <OrderSummaryMobile orderItems={orderItems} amount={totalCost} />
  ) : (
    <PlainCard className="bg-secondary top-1 order-1 block self-start px-4 py-7 lg:sticky lg:order-2">
      <OrderSummaryDesktop orderItems={orderItems} amount={totalCost} />
    </PlainCard>
  );
}

interface OrderSummaryComponentProps {
  amount: number;
  orderItems: OrderItemWithQuantity[];
}

export function OrderSummaryDesktop({
  amount,
  orderItems
}: OrderSummaryComponentProps) {
  return (
    <>
      <h3>Order summary</h3>
      <Divider />
      <ol className="space-y-4">
        {orderItems.map((item) => {
          const showItem = item.quantity > 0 && item.price > 0;
          return (
            <Fragment key={item.id}>
              {showItem && (
                <OrderSummaryRow
                  planName={item.displayName}
                  price={item.price}
                  quantity={item.quantity}
                />
              )}
            </Fragment>
          );
        })}
      </ol>
      <Divider />
      <TotalCost amount={amount} />
    </>
  );
}

function OrderSummaryMobile({
  amount,
  orderItems
}: OrderSummaryComponentProps) {
  const [toggleSummary, setToggleSummary] = useState(false);

  return (
    <div className="bg-blueberry-subtle sticky top-0 p-4">
      <button
        aria-expanded={toggleSummary}
        aria-controls="order-summary-panel"
        onClick={() => setToggleSummary(!toggleSummary)}
        className="text-xxxs flex w-full items-center justify-between leading-6 font-bold"
      >
        Order summary
        {toggleSummary ? <ChevronDown /> : <ChevronUp />}
      </button>
      {toggleSummary && (
        <div aria-hidden={!toggleSummary} hidden={!toggleSummary}>
          <ol className="mt-6 space-y-4">
            {orderItems.map((item) => {
              const showItem = item.quantity > 0 && item.price > 0;
              return (
                <Fragment key={item.id}>
                  {showItem && (
                    <OrderSummaryRowMobile
                      planName={item.displayName}
                      price={item.price}
                      quantity={item.quantity}
                    />
                  )}
                </Fragment>
              );
            })}
          </ol>
          <Divider className="mb-6" />
          <TotalCost amount={amount} />
        </div>
      )}
    </div>
  );
}

interface OrderSummaryRowProps {
  planName: string;
  price: number;
  quantity: number;
}

export function OrderSummaryRow({
  planName,
  price,
  quantity
}: OrderSummaryRowProps) {
  return (
    <li className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <span className="bg-gray-subtle text-xxxs rounded-xs px-[5px] py-[2px] font-semibold">
          {quantity}x
        </span>
        <p className="text-xxxs">{planName}</p>
      </div>
      <strong className="text-default">£{quantity * price}</strong>
    </li>
  );
}

function OrderSummaryRowMobile({
  planName,
  price,
  quantity
}: OrderSummaryRowProps) {
  return (
    <li className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <span className="bg-secondary text-xxxs rounded-xs px-[5px] py-[2px] font-semibold">
          {quantity}x
        </span>
        <p className="text-xxxs">{planName}</p>
      </div>
      <strong className="text-default">£{quantity * price}</strong>
    </li>
  );
}

// extarct  - make more generic ? - used in multiple places
interface TotalCostProps {
  amount: number;
}

export function TotalCost({ amount }: TotalCostProps) {
  return (
    <section
      aria-label="Order total"
      className="flex items-end justify-between"
    >
      <dl className="flex w-full flex-col items-end">
        <div className="flex w-full items-end justify-between">
          <dt className="text-default">Total cost</dt>
          <dd className="text-default font-bold">
            <strong>£{amount} monthly</strong>
          </dd>
        </div>
        <span className="text-xxxs text-right">(including VAT)</span>
      </dl>
    </section>
  );
}

interface AfterPaymentOrderSummaryProps {
  orderItems: OrderItemWithQuantity[];
  cardLastThreeDigits: number;
}

export function AfterPaymentOrderSummary({
  orderItems,
  cardLastThreeDigits
}: AfterPaymentOrderSummaryProps) {
  return (
    <>
      <h3>Order summary</h3>
      <ol className="space-y-4">
        {orderItems.map((item) => {
          const showItem = item.quantity > 0 && item.price > 0;
          return (
            <Fragment key={item.id}>
              {showItem && (
                <OrderSummaryRow
                  planName={item.displayName}
                  price={item.price}
                  quantity={item.quantity}
                />
              )}
            </Fragment>
          );
        })}
      </ol>
      <br />
      <TotalCost amount={12} />
      <br />
      <section
        aria-label="Order total"
        className="flex items-end justify-between"
      >
        <dl className="flex w-full flex-col items-end">
          <div className="flex w-full items-end justify-between">
            <dt className="text-default">Payment details</dt>
            <dd className="text-default font-bold">
              <strong>Card ending</strong>
            </dd>
          </div>
          <span className="text-xxxs text-right">
            **** **** **** {cardLastThreeDigits}
          </span>
        </dl>
      </section>
    </>
  );
}
