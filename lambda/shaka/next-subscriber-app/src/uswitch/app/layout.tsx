import React from 'react';
import './globals.css';
import { Metadata } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import { headers } from 'next/headers';
import { getClientConfig } from '@/client-config/client-config';
import { AuthProvider } from '@/auth/context/auth-context';
import { NuqsAdapter } from 'nuqs/adapters/next/app';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin']
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin']
});

export async function generateMetadata() {
  const config = getClientConfig();
  const headersList = headers();
  const list = await headersList;
  const host = list.get('host');
  const protocol = list.get('x-forwarded-proto') || 'https';
  const siteUrl = `${protocol}://${host}`;

  const metadata: Metadata = {
    title: config.name,
    description: '',
    keywords: [],
    robots: 'noindex, nofollow',
    openGraph: {
      title: config.name,
      description: '',
      url: siteUrl,
      siteName: '',
      images: [
        {
          url: `${siteUrl}/images/og-image.jpg`,
          width: 800,
          height: 600
        }
      ],
      type: 'website'
    }
  };

  return metadata;
}

// const GA_TRACKING_ID = process.env.NEXT_GOOGLE_TRACKING_ID || "";

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {/*<Script*/}
        {/*  strategy="afterInteractive"*/}
        {/*  src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}*/}
        {/*/>*/}
        {/*<Script id="google-analytics" strategy="afterInteractive">*/}
        {/*  {`*/}
        {/*      window.dataLayer = window.dataLayer || [];*/}
        {/*      function gtag(){dataLayer.push(arguments);}*/}
        {/*      gtag('js', new Date());*/}
        {/*      gtag('config', '${GA_TRACKING_ID}', {*/}
        {/*        page_path: window.location.pathname,*/}
        {/*      });*/}
        {/*    `}*/}
        {/*</Script>*/}

        {/*REPLACE WITH SSO !*/}
        <NuqsAdapter>
          <AuthProvider>{children}</AuthProvider>
        </NuqsAdapter>
      </body>
    </html>
  );
}
