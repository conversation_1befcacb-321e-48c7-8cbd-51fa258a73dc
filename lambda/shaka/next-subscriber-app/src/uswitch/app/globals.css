@import 'tailwindcss';
/*important !!! pull all higher level components that use tailwind classes here !*/
@source "../../../components";

@theme {
    /* BREAKPOINTS */
  --breakpoint-custom: 1621px;

  /* COLORS */
  --color-primary: #141414;
  --color-primary-hover: #434343;
  --color-tertiary: #79c3ff;
  --color-secondary: #ffffff;
  --color-secondary-hover: #434343;

  --color-blueberry: #79c3ff;
  --color-blueberry-light: #bddcff;
  --color-blueberry-subtle: #e5f3ff;

  --color-mint: #50f094;
  --color-mint-light: #bff8d3;
  --color-mint-subtle: #e5fded;

  --color-lemon: #FFEA70;
  --color-lemon-light: #fffac1;
  --color-lemon-subtle: #fffac1;

  --color-gray-subtle: #f3f3f3;
  --color-gray-subtle-tint: #1414141A;

  --color-border-subtle: #141414;
  --color-border: #898989;
  --color-placeholder: #727272;
  --color-success-border: #00AB40;

  /* 

  /* FONTS */
  --font-primary: 'Helvetica Neue', ui-sans-serif, system-ui, sans-serif;
  --font-heading: 'Founders Grotesk', ui-sans-serif, system-ui, sans-serif;

  /* FONT WEIGHTS */
  --font-weight-heading: 700;
  --font-weight-body: 400;

  /* FONT SIZES */
  /* 1rem = 16px */
  --text-xxxs: 0.875rem; /* 14px */
  --text-default: 1rem; /* 16px */
  --text-xxs: 1.25rem; /* 20px */
  --text-xs: 1.5rem; /* 24px */
  --text-sm: 1.75rem; /* 28px */
  --text-base: 2.25rem; /* 36px */
  --text-lg: 2.5rem; /* 40px */
  --text-xl: 3rem; /* 48px */
  --text-2xl: 3.5rem; /* 56px */

  /* LINE HEIGHT */
  --line-height-xxs: 20px;
  --line-height-xs: 24px;
  --line-height-sm: 28px;
  --line-height-base: 32px;
  --line-height-large: 40px;
  --line-height-xl: 48px;
  --line-height-2xl: 56px;

  /* SPACING */
  --spacing-1: 4px;
  --spacing-2: 8px;
  --spacing-3: 12px;
  --spacing-4: 16px;
  --spacing-5: 20px;
  --spacing-6: 24px;
  --spacing-7: 32px;
  --spacing-8: 40px;
  --spacing-9: 48px;
  --spacing-10: 64px;
  --spacing-11: 80px;
  --spacing-12: 96px;
  --spacing-13: 104px;
  --spacing-14: 192px;

  /* SHADOWS */
  --shadow-sm: 0px 1px 3px 1px rgba(20, 20, 20, 0.08), 0px 1px 2px 0px rgba(20, 20, 20, 0.16);
  --shadow-md: 0px 1px 3px 0px rgba(20, 20, 20, 0.16), 0px 4px 8px 3px rgba(20, 20, 20, 0.08);
  --shadow-lg: 0px 2px 3px 0px rgba(20, 20, 20, 0.16), 0px 6px 10px 4px rgba(20, 20, 20, 0.08);
  --shadow-xl: 0px 4px 4px 0px rgba(20, 20, 20, 0.16), 0px 8px 12px 6px rgba(20, 20, 20, 0.08);
  --shadow-none: none;

  /* PLAN SECTION SPACING */
  --plan-card-padding: var(--spacing-6);
  --plan-card-gap: var(--spacing-4);

  /* BORDER RADIUS */
  --radius-2: 8px;
}

@layer components {
  .layout {
    @apply lg:bg-gray-subtle mx-auto px-[var(--spacing-6)] py-[var(--spacing-6)] lg:px-[var(--spacing-13)] lg:py-[var(--spacing-7)] xl:py-[var(--spacing-8)];
  }
  .plain-card {
    @apply bg-secondary rounded-lg border-1 border-gray-300 p-2 shadow-[var(--shadow-md)] lg:p-3;
  }
  .uswitchLink {
    @apply text-[var(--text-xs)] font-[var(--font-primary)] leading-6 underline underline-offset-2 hover:decoration-2 focus:outline-2 focus:outline-offset-4 rounded-[1px] cursor-pointer;
  }
  .outline {
    @apply focus-visible:outline-mint focus:outline-none focus-visible:outline-4 focus-visible:outline-offset-3 focus-visible:outline-solid;
  }
  .date-picker {
    @apply sm:grid gap-6 space-y-4 sm:space-y-0;

    grid-template-columns: 1fr;

    @media (min-width: 1024px) and (max-width: 1298px) {
      grid-template-columns: 1fr 1fr;
    }

    @media (min-width: 1299px) and (max-width: 1620px) {
      grid-template-columns: 0.7fr 0.7fr .8fr;
      gap: 1rem;
    }

    @media (min-width: 1621px) {
      grid-template-columns: repeat(3, 1fr);
    }
  }
  .order-confirmation-layout {
    @media (min-width: 1024px) {
      padding-inline: 2vw;
    }

    @media (min-width: 1224px) {
      padding-inline: 10vw;
    }

    @media (min-width: 1620px)  {
    padding-inline: 18vw;
    }

    @media (min-width: 1920px)  {
      padding-inline: 23vw;
    }
  }

  /* TYPOGRAPHY */
  p {
    font-family: var(--font-primary);
    font-size: var(--text-default);
    line-height: var(--line-height-xs);
    font-weight: var(--font-weight-body);
    color: var(--color-primary);
    opacity: .7;
    letter-spacing: 0;
    vertical-align: middle;
  }

  h1 {
    font-family: var(--font-heading);
    font-size: var(--text-lg);
    line-height: var(--line-height-large);
    font-weight: var(--font-weight-heading);
    color: var(--color-primary);
    letter-spacing: 0;
    vertical-align: middle;

    @media (width < 64rem) {
      font-size: var(--text-xs);
      line-height: var(--line-height-base);
    }
  }

  h2 {
    font-family: var(--font-heading);
    font-size: var(--text-xs);
    line-height: var(--line-height-base);
    font-weight: var(--font-weight-heading);
    color: var(--color-primary);
    letter-spacing: 0;
    vertical-align: middle;
  }

  h3 {
    font-family: var(--font-heading);
    font-size: var(--text-xxs);
    line-height: var(--line-height-xs);
    font-weight: var(--font-weight-heading);
    color: var(--color-primary);
    letter-spacing: 0;
    vertical-align: middle;
  }

}

/* TODO: support for light/dark theme just in case !!*/
