/*prefix	Minimum width	CSS*/
/*sm	    40rem (640px)	@media (width >= 40rem) { ... }*/
/*md	    48rem (768px)	@media (width >= 48rem) { ... }*/
/*lg	    64rem (1024px)	@media (width >= 64rem) { ... }*/
/*xl	    80rem (1280px)	@media (width >= 80rem) { ... }*/
/*2xl	    96rem (1536px)	@media (width >= 96rem) { ... }*/

/*
  If you'd like to apply a utility only when a specific breakpoint range is active,
  stack a responsive variant like md with a max-* variant to limit that style to a specific range:
*/


/*Variant	Media query*/
/*max-sm	@media (width < 40rem) { ... }*/
/*max-md	@media (width < 48rem) { ... }*/
/*max-lg	@media (width < 64rem) { ... }*/
/*max-xl	@media (width < 80rem) { ... }*/
/*max-2xl	@media (width < 96rem) { ... }*/

@theme {
    /*SPACING VARS*/
    /*FONTS ???*/
    /*FONT SIZES ?*/
}

/*Use the @apply directive to inline any existing utility classes into your own custom CSS:*/
/* good place to put together border radius, padding for a card styling that can be used globally */

@layer components {
    .card {
        @apply rounded-[var(--card-radius)] p-[var(--card-padding)] shadow-[var(--card-shadow)] bg-[var(--card-bg)];
    }
    .borderr {
        @apply border-2 border-red-700;
    }
}

/* TODO: support for light/dark theme just in case !!*/