export const API_ENDPOINTS = {
  dashboard: '/dashboard',
  auth: {
    login: '/auth/login',
    logout: '/auth/logout',
    refresh: '/auth/refresh-token',
    signup: '/auth/sign-up',
    forgotPassword: '/auth/forgot-password',
    resetPassword: '/auth/reset-password'
  },
  stripe: {
    createCheckoutSession: (planId: string, returnUrl: string) =>
      `/plans/sign-up/${planId}/?ui_mode=custom&return_path=/payment/${returnUrl}/`
    // correlation id ????
  },
  subscriptions: {
    base: '/subscriptions',
    byId: (id: string) => `/subscriptions/${id}`,
    cancel: (id: string) => `/subscriptions/${id}/cancel`,
    pause: (id: string) => `/subscriptions/${id}/pause`,
    resume: (id: string) => `/subscriptions/${id}/resume`
  },
  plans: {
    base: '/plans',
    byId: (id: string) => `/plans/${id}`
  }
} as const;

export type ApiEndpoints = typeof API_ENDPOINTS;

// .get(`/checkout/session/status/?session_id=${sessionId}`)

// api.post(`/plans/sign-up/${planId}/`).then((res) => res.data);
