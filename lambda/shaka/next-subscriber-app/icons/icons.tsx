import React from 'react';

// TODO - make more customisable
export function CoverageIcon() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_93_16620)">
        <path
          d="M7.27158 4.17502C5.52291 5.50283 4.38462 7.66879 4.38462 10.1172C4.38462 12.5656 5.52291 14.7316 7.27158 16.0594L6.45919 17.2344C4.36446 15.6438 3 13.0478 3 10.1172C3 7.18659 4.36446 4.59056 6.45919 3L7.27158 4.17502Z"
          fill="#434343"
        />
        <path
          d="M19.6154 10.1172C19.6154 7.66879 18.4771 5.50283 16.7284 4.17502L17.5408 3C19.6355 4.59056 21 7.18659 21 10.1172C21 13.0478 19.6355 15.6438 17.5408 17.2344L16.7284 16.0594C18.4771 14.7316 19.6154 12.5656 19.6154 10.1172Z"
          fill="#434343"
        />
        <path
          d="M7.15385 10.1172C7.15385 8.90561 7.73824 7.80488 8.6883 7.07462L7.86939 5.90456C6.60364 6.87748 5.76923 8.39389 5.76923 10.1172C5.76923 11.8405 6.60364 13.3569 7.86939 14.3298L8.6883 13.1598C7.73824 12.4295 7.15385 11.3288 7.15385 10.1172Z"
          fill="#434343"
        />
        <path
          d="M15.3117 7.07462C16.2618 7.80488 16.8462 8.90561 16.8462 10.1172C16.8462 11.3288 16.2618 12.4295 15.3117 13.1598L16.1306 14.3298C17.3964 13.3569 18.2308 11.8405 18.2308 10.1172C18.2308 8.39389 17.3964 6.87748 16.1306 5.90456L15.3117 7.07462Z"
          fill="#434343"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12 7.21512C10.4706 7.21512 9.23077 8.51442 9.23077 10.1172C9.23077 11.4694 10.1133 12.6057 11.3077 12.9278L11.3077 21H12.6923L12.6923 12.9278C13.8867 12.6057 14.7692 11.4694 14.7692 10.1172C14.7692 8.51442 13.5294 7.21512 12 7.21512ZM10.6154 10.1172C10.6154 9.31581 11.2353 8.66616 12 8.66616C12.7647 8.66616 13.3846 9.31581 13.3846 10.1172C13.3846 10.9186 12.7647 11.5682 12 11.5682C11.2353 11.5682 10.6154 10.9186 10.6154 10.1172Z"
          fill="#434343"
        />
      </g>
      <defs>
        <clipPath id="clip0_93_16620">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function PlusIcon() {
  return (
    <svg
      aria-hidden="true"
      width="14"
      height="3"
      viewBox="0 0 11 2"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect y="0.5" width="11" height="1" fill="white" />
    </svg>
  );
}

export function MinusIcon() {
  return (
    <svg
      aria-hidden="true"
      width="16"
      height="16"
      viewBox="0 0 11 12"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M6 5.5H11V6.5H6V11.5H5V6.5H0V5.5H5V0.5H6V5.5Z" fill="white" />
    </svg>
  );
}

export function CloseIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M16.205 18.0249L25.59 27.4099L27.4099 25.59L18.0249 16.205L27.41 6.81992L25.5901 5L16.205 14.3851L6.81992 5L5 6.81992L14.3851 16.205L5.00006 25.59L6.81998 27.4099L16.205 18.0249Z"
        fill="#141414"
      />
    </svg>
  );
}

export function TickIcon() {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.18468 14.0036L7.18342 14.0048L8.12643 14.9478L17.1313 5.94301L16.1882 5L8.12769 13.0606L4.06801 9.00088L3.125 9.94389L7.18468 14.0036Z"
        fill="#141414"
      />
    </svg>
  );
}

export function ChevronDown({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 16.8105L3.96966 8.78022L5.03032 7.71956L12 14.6892L18.9697 7.71956L20.0303 8.78022L12 16.8105Z"
        fill="#141414"
      />
    </svg>
  );
}

export function ChevronUp() {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 7.18945L20.0303 15.2198L18.9697 16.2804L12 9.31077L5.03033 16.2804L3.96967 15.2198L12 7.18945Z"
        fill="#141414"
      />
    </svg>
  );
}

export function PlayButtonIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M14.375 10.8333L7.5 14.375V6.25L14.375 10.8333ZM8.75 12.3249V8.58565L11.914 10.695L8.75 12.3249Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10 1.25C5.16751 1.25 1.25 5.16751 1.25 10C1.25 14.8325 5.16751 18.75 10 18.75C14.8325 18.75 18.75 14.8325 18.75 10C18.75 5.16751 14.8325 1.25 10 1.25ZM2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10C17.5 14.1421 14.1421 17.5 10 17.5C5.85786 17.5 2.5 14.1421 2.5 10Z"
        fill="#141414"
      />
    </svg>
  );
}

export function PACIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="40" height="40" rx="2" fill="#E5F3FF" />
      <path d="M26 17L14 17V15L26 15V17Z" fill="#141414" />
      <path d="M14 21L26 21V19L14 19V21Z" fill="#141414" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7 8H33V28H25.4142L20 33.4142L14.5858 28H7V8ZM9 10V26H15.4142L20 30.5858L24.5858 26H31V10H9Z"
        fill="#141414"
      />
    </svg>
  );
}
export function TextMessageIcon() {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="40" height="40" rx="2" fill="#E5F3FF" />
      <path d="M24 7H7V33H24V26H22V31H9V9H24V7Z" fill="#141414" />
      <path d="M17 29H14V27H17V29Z" fill="#141414" />
      <path d="M22 17L29 17V15L22 15V17Z" fill="#141414" />
      <path d="M22 20V18H26V20H22Z" fill="#141414" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18 11V27L21.6 24H33V11H18ZM31 22H20.8759L20 22.7299V13H31V22Z"
        fill="#141414"
      />
    </svg>
  );
}

export function ErrorIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      aria-hidden="true"
    >
      <circle cx="12" cy="12" r="10"></circle>
      <line x1="12" x2="12" y1="8" y2="12"></line>
      <line x1="12" x2="12.01" y1="16" y2="16"></line>
    </svg>
  );
}

export function LoadingSpinner({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width="25px"
      height="25px"
      viewBox="0 0 100 100"
    >
      <circle
        cx="50"
        cy="50"
        fill="none"
        opacity={0.7}
        stroke="currentColor"
        strokeWidth="10"
        r="35"
        strokeDasharray="164.93361431346415 56.97787143782138"
      >
        <animateTransform
          attributeName="transform"
          type="rotate"
          repeatCount="indefinite"
          dur="1s"
          values="0 50 50;360 50 50"
          keyTimes="0;1"
        ></animateTransform>
      </circle>
    </svg>
  );
}

export function InfoIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="25"
      height="24"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.5 21.9618C18.299 21.9618 23 17.4032 23 11.78C23 6.1567 18.299 1.59814 12.5 1.59814C6.70101 1.59814 2 6.1567 2 11.78C2 17.4032 6.70101 21.9618 12.5 21.9618ZM11.75 15.7176L17.5303 10.1124L16.4697 9.0839L11.75 13.6605L9.28034 11.2657L8.21968 12.2942L11.75 15.7176Z"
        fill="#141414"
      />
    </svg>
  );
}

export function WifiIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="57"
      height="57"
      viewBox="0 0 57 57"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.5" y="0.5" width="56" height="56" rx="2" fill="#E4FBED" />
      <path
        d="M16.2064 24.5796C22.9958 17.8068 34.0046 17.8068 40.7939 24.5796L42.2064 23.1636C34.6364 15.6121 22.364 15.6121 14.7939 23.1636L16.2064 24.5796Z"
        fill="#141414"
      />
      <path
        d="M20.0299 28.3938C24.7076 23.7275 32.2927 23.7275 36.9704 28.3938L38.3829 26.9779C32.9245 21.5328 24.0758 21.5328 18.6175 26.9779L20.0299 28.3938Z"
        fill="#141414"
      />
      <path
        d="M23.8535 32.208C26.4195 29.6482 30.5808 29.6482 33.1469 32.208L34.5593 30.792C31.2127 27.4535 25.7877 27.4535 22.441 30.792L23.8535 32.208Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.5002 33.5C26.8433 33.5 25.5002 34.8431 25.5002 36.5C25.5002 38.1569 26.8433 39.5 28.5002 39.5C30.157 39.5 31.5002 38.1569 31.5002 36.5C31.5002 34.8431 30.157 33.5 28.5002 33.5ZM27.5002 36.5C27.5002 35.9477 27.9479 35.5 28.5002 35.5C29.0525 35.5 29.5002 35.9477 29.5002 36.5C29.5002 37.0523 29.0525 37.5 28.5002 37.5C27.9479 37.5 27.5002 37.0523 27.5002 36.5Z"
        fill="#141414"
      />
    </svg>
  );
}

export function CameraIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="57"
      height="57"
      viewBox="0 0 57 57"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.5" y="0.5" width="56" height="56" rx="2" fill="#E4FBED" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.5 24.5C25.7386 24.5 23.5 26.7386 23.5 29.5C23.5 32.2614 25.7386 34.5 28.5 34.5C31.2614 34.5 33.5 32.2614 33.5 29.5C33.5 26.7386 31.2614 24.5 28.5 24.5ZM25.5 29.5C25.5 27.8431 26.8431 26.5 28.5 26.5C30.1569 26.5 31.5 27.8431 31.5 29.5C31.5 31.1569 30.1569 32.5 28.5 32.5C26.8431 32.5 25.5 31.1569 25.5 29.5Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M33.118 16.5H23.882L21.882 20.5H15.5V40.5H41.5V20.5H35.118L33.118 16.5ZM17.5 22.5H23.118L25.118 18.5H31.882L33.882 22.5H39.5V38.5H17.5V22.5Z"
        fill="#141414"
      />
    </svg>
  );
}

export function SimIcon({ className }: { className?: string }) {
  return (
    <svg
      className={className}
      width="57"
      height="57"
      viewBox="0 0 57 57"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="0.5" y="0.5" width="56" height="56" rx="2" fill="#E4FBED" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M30.6716 17.5H21.5V39.5H35.5V22.3284L30.6716 17.5ZM31.5 15.5H19.5V41.5H37.5V21.5L31.5 15.5Z"
        fill="#141414"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.5 27.5V37.5H33.5V27.5H23.5ZM31.5 29.5H29.5V31.5H31.5V29.5ZM31.5 33.5H29.5V35.5H31.5V33.5ZM25.5 29.5H27.5V35.5H25.5V29.5Z"
        fill="#141414"
      />
    </svg>
  );
}
