# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/
/dist/
/src/**/dist/
/src/**/.next/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
/src/uswitch/.env.local
/src/subscriber-app/.env.local
.env.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
!/smtp.py
/client-config/subscriber.env
/client-config/uswitch.env
