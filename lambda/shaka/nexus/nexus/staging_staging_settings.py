from .settings import *  # pylint: disable=wildcard-import, unused-wildcard-import

DEBUG = False

ALLOWED_HOSTS = ['*']  # WAF does host matching, this is mostly because the EB health check hits only the IP
SITE_URL = 'https://staging.shaka.tel'
CSRF_TRUSTED_ORIGINS = [SITE_URL]

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': '/var/log/django',
            'formatter': 'simple',
        },
    },
    'formatters': {
        'simple': {
            'format': '%(levelname)s %(asctime)s %(name)s.%(funcName)s:%(lineno)s- %(message)s'
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        '': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': False,
        }
    },
}


DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": "staging",
        "HOST": os.environ.get('DB_HOST', 'localhost'),
        "USER": os.environ.get('DB_USER', 'cdr_db'),
        "PASSWORD": os.environ.get('DB_PASSWORD'),
    }
}

SLACK_CLIENT_PK_CUTOFF = 4
