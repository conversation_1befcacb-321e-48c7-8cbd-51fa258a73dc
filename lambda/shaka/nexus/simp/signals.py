import logging
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.conf import settings
from django.db.utils import DatabaseError
from core.models import (
    Subscription,
    SimSubscriptionAssignment,
    SubscriptionPayment,
    PerkRedemption,
    Referral
)
from .models import Onboarding, Notification
logger = logging.getLogger(__name__)
@receiver(post_save, sender=Subscription)
def create_onboarding_details(instance, created, **kwargs):
    if not created:
        return
    if not getattr(settings, 'AUTO_CREATE_ONBOARDING_DETAILS', False):
        return
    try:
        subscriber = instance.subscriber
        with subscriber.lock():
            _, created = Onboarding.objects.get_or_create(
                subscriber=subscriber,
                defaults={'onboarding_details': {}}
            )
            subscriber.generate_referral_code_if_necessary()
            if created:
                logger.info(
                    "Created onboarding details for subscription %s (subscriber: %s)",
                    instance.id,
                    subscriber.id
                )
    except DatabaseError as err:
        logger.error(
            "Failed to create onboarding details for subscription %s: %s",
            instance.id,
            str(err)
        )

@receiver(post_save, sender=SimSubscriptionAssignment)
def update_onboarding_activation_link(instance, created, **kwargs):
    if not created or not instance.sim.is_esim:
        return
    try:
        with instance.subscription.subscriber.lock():
            onboarding = Onboarding.objects.filter(
                subscriber=instance.subscription.subscriber
            ).first()
            if onboarding:
                details = onboarding.onboarding_details or {}
                details['activationLink'] = instance.sim.esim_data
                onboarding.onboarding_details = details
                onboarding.save()
                logger.info(
                    "Updated onboarding activation link for subscription %s (subscriber: %s)",
                    instance.subscription.id,
                    instance.subscription.subscriber.id
                )
    except DatabaseError as err:
        logger.error(
            "Failed to update onboarding activation link for subscription %s: %s",
            instance.subscription.id,
            str(err)
        )

@receiver(post_save, sender=SubscriptionPayment)
def create_payment_notification(instance, created, **kwargs):
    if not created or not getattr(settings, 'SEND_SIMP_NOTIFICATIONS', False):
        return
    try:
        Notification.objects.create(
            subscriber=instance.subscription.subscriber,
            text="Billing successful!"
        )
        logger.info(
            "Created billing notification for subscription %s (subscriber: %s)",
            instance.subscription.id,
            instance.subscription.subscriber.id
        )
    except DatabaseError as err:
        logger.error(
            "Failed to create billing notification for subscription %s: %s",
            instance.subscription.id,
            str(err)
        )

@receiver(post_save, sender=PerkRedemption)
def create_perk_notification(instance, created, **kwargs):
    if not created or not getattr(settings, 'SEND_SIMP_NOTIFICATIONS', False):
        return
    try:
        Notification.objects.create(
            subscriber=instance.subscriber,
            text="You unlocked a perk!"
        )
        logger.info(
            "Created perk notification for subscriber %s",
            instance.subscriber.id
        )
    except DatabaseError as err:
        logger.error(
            "Failed to create perk notification for subscriber %s: %s",
            instance.subscriber.id,
            str(err)
        )

@receiver(post_save, sender=Referral)
def create_referral_notification(instance, created, **kwargs):
    if not created or not getattr(settings, 'SEND_SIMP_NOTIFICATIONS', False):
        return
    try:
        Notification.objects.create(
            subscriber=instance.referrer,
            text="You referred a friend!"
        )
        logger.info(
            "Created referral notification for referrer %s",
            instance.referrer.id
        )
    except DatabaseError as err:
        logger.error(
            "Failed to create referral notification for referrer %s: %s",
            instance.referrer.id,
            str(err)
        )
