from django.contrib.auth.mixins import UserPassesTestMixin
from django.shortcuts import get_object_or_404, redirect
from django.views import View
from django.http import HttpResponseBadRequest, HttpResponse
from django.shortcuts import render
from rest_framework import views, status, viewsets
from rest_framework.response import Response
from core.models import Subscriber
from subscriber_app.views import BearerTokenAuthentication
from .models import Draw, Prize, StaticFile, Alert, Onboarding, Notification
from .serializers import (
    DrawSerializer, PrizeSerializer, OnboardingDetailsSerializer,
    AlertSerializer, NotificationSerializer
)

class SuperuserRequiredMixin(UserPassesTestMixin):
    def test_func(self):
        return self.request.user.is_superuser

class ManualDrawDetailView(SuperuserRequiredMixin, View):
    template_name = 'draw_detail.html'

    def get(self, request, draw_id):
        draw = get_object_or_404(Draw, id=draw_id)
        prizes = Prize.objects.filter(draw=draw)
        context = {
            'draw': draw,
            'prizes': prizes,
        }
        return render(request, self.template_name, context)

    def post(self, request, draw_id):
        draw = get_object_or_404(Draw, id=draw_id)
        entries = int(request.POST.get('entries'))
        if draw.status == Draw.DrawStatus.RUNNING:
            draw.reset()
            draw.refresh_from_db()
        if draw.status == Draw.DrawStatus.DRAFT:
            draw.start_draw(entries)
        return redirect('draw_detail', draw_id=draw.id)


class SetPrizesView(SuperuserRequiredMixin, View):
    template_name = 'set_prizes.html'

    def get(self, request, draw_id):
        draw = get_object_or_404(Draw, id=draw_id)
        if draw.status != Draw.DrawStatus.DRAFT:
            return HttpResponseBadRequest("Draw must be in draft status to set prizes.")
        return render(request, self.template_name, {'draw': draw})

    def post(self, request, draw_id):
        draw = get_object_or_404(Draw, id=draw_id)
        if draw.status != Draw.DrawStatus.DRAFT:
            return HttpResponseBadRequest("Draw must be in draft status to set prizes.")

        prize_list = request.POST.get('prizes')
        if not prize_list:
            return HttpResponseBadRequest("Prizes list cannot be empty.")

        Prize.objects.filter(draw=draw).delete()

        lines = prize_list.split('\n')
        total_prizes = 0
        for line in lines:
            line = line.strip()
            if 'x' in line:
                count, name = line.split('x', 1)
                count = int(count.strip())
                name = name.strip()
                for _ in range(count):
                    Prize.objects.create(draw=draw, name=name)
                    total_prizes += 1

        while total_prizes < draw.total_pool_size:
            Prize.objects.create(draw=draw, name='Empty Prize', is_empty=True)
            total_prizes += 1

        return redirect('draw_detail', draw_id=draw.id)


class DrawListView(views.APIView):
    permission_classes = [BearerTokenAuthentication]
    def get(self, request, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        draws = Draw.objects.filter(client_id=client_id).prefetch_related('prizes__prize_template').all()
        serializer = DrawSerializer(draws, many=True, context={'subscriber': subscriber})
        return Response(serializer.data)

class DrawDetailView(views.APIView):
    permission_classes = [BearerTokenAuthentication]
    def get(self, request, client_id, pk):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        try:
            draw = Draw.objects.filter(client_id=client_id).prefetch_related('prizes__prize_template').get(pk=pk)
        except Draw.DoesNotExist:
            return Response({'detail': 'Draw not found.'}, status=status.HTTP_404_NOT_FOUND)

        serializer = DrawSerializer(draw, context={'subscriber': subscriber})
        return Response(serializer.data)

class SpinPrizeView(views.APIView):
    permission_classes = [BearerTokenAuthentication]
    def post(self, request, client_id, pk):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        try:
            draw = Draw.objects.filter(client_id=client_id).prefetch_related('prizes').get(pk=pk)
        except Draw.DoesNotExist:
            return Response({'detail': 'Draw not found.'}, status=status.HTTP_404_NOT_FOUND)

        prize = draw.draw_a_prize(subscriber)
        if not prize:
            return Response({'detail': 'No prizes available for this draw.'}, status=status.HTTP_400_BAD_REQUEST)

        return Response(PrizeSerializer(prize).data)


def serve_static_file(request, filename):
    static_file = get_object_or_404(StaticFile, name=filename)
    response = HttpResponse(static_file.data, content_type=static_file.content_type)
    response["Content-Disposition"] = f'inline; filename="{static_file.name}"'
    return response


def get_subscriber_by_cognito_username(cognito_username, client_id):
    return Subscriber.objects.filter(cognito_username=cognito_username, client_id=client_id).get()


class OnboardingDetailsView(views.APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        if not Onboarding.objects.filter(subscriber=subscriber).exists():
            print(f"Creating onboarding for {subscriber}")
            Onboarding.objects.create(subscriber=subscriber, onboarding_details={})

        onboarding_details = subscriber.onboarding_details

        serializer = OnboardingDetailsSerializer(onboarding_details.onboarding_details)
        print(serializer.data, subscriber, request.cognito_username)
        return Response(serializer.data)

    def put(self, request, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        onboarding = subscriber.onboarding_details

        onboarding.onboarding_details.update(request.data)
        onboarding.save()
        serializer = OnboardingDetailsSerializer(onboarding.onboarding_details)
        return Response(serializer.data)


class EsimQRView(views.APIView):
    permission_classes = [BearerTokenAuthentication]

    def get(self, request, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        sim = subscriber.subscriptions.first().latest_sim
        # serve qr as an image
        return HttpResponse(sim.esim_qr_as_png, content_type='image/png')

class AlertViewSet(viewsets.ModelViewSet):
    serializer_class = AlertSerializer
    permission_classes = [BearerTokenAuthentication]

    def get_queryset(self):
        subscriber = get_subscriber_by_cognito_username(self.request.cognito_username, self.kwargs.get('client_id'))
        return Alert.objects.for_subscriber(subscriber)

    def destroy(self, request, *args, **kwargs):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, self.kwargs.get('client_id'))
        alert = self.get_object()
        alert.mark_as_dismissed(subscriber)
        return Response(status=status.HTTP_204_NO_CONTENT)

    def create(self, request, *args, **kwargs):
        return Response({"detail": "Method Not Allowed"}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

    def update(self, request, *args, **kwargs):
        return Response({"detail": "Method Not Allowed"}, status=status.HTTP_405_METHOD_NOT_ALLOWED)


class SimpNotificationsView(views.APIView):
    permission_classes = [BearerTokenAuthentication]
    def get(self, request, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        notifications = Notification.objects.filter(subscriber=subscriber)
        serializer = NotificationSerializer(notifications, many=True)
        return Response(serializer.data)

    def delete(self, request, notification_id, *, client_id):
        subscriber = get_subscriber_by_cognito_username(request.cognito_username, client_id)
        try:
            notification = Notification.objects.get(id=notification_id, subscriber=subscriber)
            notification.mark_as_seen()
            return Response(status=status.HTTP_204_NO_CONTENT, headers={'Content-Length': '0'})
        except Notification.DoesNotExist:
            return Response(
                {"detail": "Notification not found"},
                status=status.HTTP_404_NOT_FOUND
            )
