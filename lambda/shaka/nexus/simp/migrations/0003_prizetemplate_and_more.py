# Generated by Django 4.2.7 on 2025-01-03 15:23

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('simp', '0002_alter_prize_options_alter_prize_draw'),
    ]

    operations = [
        migrations.CreateModel(
            name='PrizeTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('image_link', models.URLField(blank=True, null=True)),
                ('is_empty', models.BooleanField(db_index=True, default=False)),
            ],
        ),
        migrations.RemoveIndex(
            model_name='prize',
            name='simp_prize_draw_id_54df68_idx',
        ),
        migrations.RemoveField(
            model_name='prize',
            name='is_empty',
        ),
        migrations.RemoveField(
            model_name='prize',
            name='name',
        ),
        migrations.AddField(
            model_name='prize',
            name='prize_template',
            field=models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, to='simp.prizetemplate'),
            preserve_default=False,
        ),
    ]
