# Generated by Django 4.2.7 on 2025-02-18 23:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0152_alter_bolton_duration_type'),
        ('simp', '0003_prizetemplate_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Onboarding',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('onboarding_details', models.JSONField()),
                ('subscriber', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='core.subscriber')),
            ],
        ),
    ]
