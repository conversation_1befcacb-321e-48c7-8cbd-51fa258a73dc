<!-- draw_detail.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Draw Detail for {{ draw.name }}</title>
    <style>
        .prize-grid {
            display: flex;
            flex-wrap: wrap;
        }
        .prize {
            border: 1px solid #ccc;
            margin: 5px;
            padding: 10px;
            width: 100px;
            text-align: center;
        }
        .prize.claimed {
            background-color: green;
            color: white;
        }
        .prize.not-claimed {
            background-color: red;
            color: white;
        }
    </style>
</head>
<body>
    <h1>Draw Detail for {{ draw.name }}</h1>
    <p>Client: {{ draw.client.name }}</p>
    <p>Status: {{ draw.get_status_display }}</p>
    <p>Start: {{ draw.start_datetime }}</p>
    <p>End: {{ draw.end_datetime }}</p>
    <a href="{% url 'set_prizes' draw.id %}">Set prizes</a>

    {% if draw.status == 'draft' %}
        <form method="post">
          {% csrf_token %}
          <label>Entries:<label> <input type="number" name="entries">
            <button type="submit">Start Draw</button>
        </form>
    {% elif draw.status == 'running' %}
        <form method="post">
          {% csrf_token %}
          <label>Entries:<label> <input type="number" name="entries">
            <button type="submit">Redo Draw</button>
        </form>
    {% endif %}
        <p>Prizes Claimed: {{ draw.prizes_claimed }}</p>
        <p>Prizes Left: {{ draw.prizes_not_claimed }}</p>
        <div class="prize-grid">
            {% for prize in prizes %}
                <div class="prize {% if prize.claimed_by %}claimed{% else %}not-claimed{% endif %}">
                    <p>{{ prize.name }}{% if prize.claimed_by %} - {{ prize.claimed_by }}{% endif %}</p>
                </div>
            {% endfor %}
        </div>
</body>
</html>
