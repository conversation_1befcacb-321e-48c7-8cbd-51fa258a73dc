from django.core.management.base import BaseCommand, CommandError
from django.utils.dateparse import parse_date
from django.utils import timezone
import decimal
from core.models import Client, Subscription
from core.utils import get_stripe_client
from core.billing import prorate_remaining_charge
from datetime import datetime
import pytz

class Command(BaseCommand):
    help = 'Find common/legacy issues with subscriptions and report on them'

    def add_arguments(self, parser):
        parser.add_argument('--client-id', type=int, help='Client ID to check')
    
    def handle(self, *args, **options):
        client = Client.objects.get(pk=options['client_id'])
        subscriptions = Subscription.objects.filter(subscriber__client=client)
        stripe_client = get_stripe_client(client.payment_integration.secret_credentials)
        self.look_for_sims_without_activation_dates(subscriptions)
        self.look_for_paused_subscriptions_that_should_be_active(subscriptions, stripe_client)
        self.look_for_subscriptions_activated_after_a_billing_cycle_that_need_a_prorated_charge(subscriptions, stripe_client)
        self.look_for_subscriptions_with_a_next_bill_date_in_this_month_rather_than_next_month(subscriptions, stripe_client)

    def sim_and_subscription_is_active(self, subscription):
        return subscription.status == Subscription.Statuses.ACTIVE and subscription.latest_sim and subscription.latest_sim.status == Subscription.Statuses.ACTIVE

    def subscription_is_paused_in_stripe(self, subscription, stripe_client):
        stripe_subscription = stripe_client.subscriptions.retrieve(subscription.billing_subscription_id)
        return stripe_subscription['status'] == 'paused' or stripe_subscription.get('pause_collection')

    def subscription_has_recorded_payment_for_this_billing_cycle(self, subscription):
        start, end = subscription.latest_provider.current_billing_month_date_range
        return subscription.payments.filter(date__gte=start, date__lt=end).exists()

    def get_subscription_sim_activation_date(self, subscription):
        return subscription.latest_sim.activation_date

    def get_prorated_charge(self, subscription):
        total_amount = subscription.latest_plan.price
        start, end = subscription.latest_provider.current_billing_month_date_range
        proration_start = self.get_subscription_sim_activation_date(subscription)
        return prorate_remaining_charge(total_amount, start, end, proration_start)
    
    def look_for_sims_without_activation_dates(self, subscriptions):
        for subscription in subscriptions:
            if self.sim_and_subscription_is_active(subscription) and not subscription.latest_sim.activation_date:
                print(f"Subscription {subscription} has no activation date: {subscription.pk}")

    def look_for_paused_subscriptions_that_should_be_active(self, subscriptions, stripe_client):
        for subscription in subscriptions:
            if self.sim_and_subscription_is_active(subscription):
                if self.subscription_is_paused_in_stripe(subscription, stripe_client):
                    print(f"Subscription {subscription} is paused but should probably be active: {subscription.pk}")
    
    def look_for_subscriptions_activated_after_a_billing_cycle_that_need_a_prorated_charge(self, subscriptions, stripe_client):
        for subscription in subscriptions:
            if self.sim_and_subscription_is_active(subscription):
                if not self.subscription_is_paused_in_stripe(subscription, stripe_client):
                    if not self.subscription_has_recorded_payment_for_this_billing_cycle(subscription):
                        print(f"Subscription {subscription} was activated on {self.get_subscription_sim_activation_date(subscription)} but has probably not been charged for this billing cycle: {subscription.pk}. Charge should be {self.get_prorated_charge(subscription)}")

    def epoch_timestamp_to_europe_london(self, epoch_timestamp):
        return datetime.utcfromtimestamp(epoch_timestamp).replace(tzinfo=pytz.utc).astimezone(pytz.timezone('Europe/London'))

    def get_europe_london_current_month(self):
        return timezone.now().astimezone(pytz.timezone('Europe/London')).month

    def start_of_next_month_europe_london(self):
        return datetime(timezone.now().year, timezone.now().month + 1, 1, 0, 0, 0, tzinfo=pytz.utc).replace(tzinfo=pytz.timezone('Europe/London')).strftime('%Y-%m-%d')

    def look_for_subscriptions_with_a_next_bill_date_in_this_month_rather_than_next_month(self, subscriptions, stripe_client):
        for subscription in subscriptions:
            if self.sim_and_subscription_is_active(subscription):
                stripe_subscription = stripe_client.subscriptions.retrieve(subscription.billing_subscription_id)
                next_bill_date = self.epoch_timestamp_to_europe_london(stripe_subscription['current_period_end'])
                current_month = self.get_europe_london_current_month()
                if next_bill_date.month == current_month:
                    print(f"Subscription {subscription} has a next bill date in this month rather than next month: {subscription.pk}, timestamp: {next_bill_date}, epoch: {stripe_subscription['current_period_end']}. Recommend a free trial to the beginning of next month to reset the anchor.")
                    print(f'Recommend ./manage.py create_manual_free_trial --client-id {client.pk} --subscription-billing-id {subscription.billing_subscription_id} --date {self.start_of_next_month_europe_london()}')
