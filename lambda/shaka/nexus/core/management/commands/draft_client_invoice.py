import csv
from django.core.management.base import BaseCommand, CommandError
from django.utils.dateparse import parse_date
from django.utils import timezone
from decimal import Decimal
from core.models import Client, Subscription
from core.utils import get_stripe_client
from core.billing import prorate_remaining_charge
from core.time_control import STRIPE_BILLING_TIME_CONTROL
from datetime import datetime, timedelta
import pytz

def coerce_to_london_time(dt):
    el = pytz.timezone('Europe/London')
    if dt.tzinfo:
        return el.localize(datetime(dt.year, dt.month, dt.day, dt.hour, dt.minute, dt.second, 0))
    return el.localize(dt)

class Command(BaseCommand):
    help = 'Print prospective invoices for last month'

    def add_arguments(self, parser):
        parser.add_argument('--client-id', type=int, help='Client ID to check')
        parser.add_argument('--csv-file', type=str, help='CSV file to write to', required=True)
        parser.add_argument('--we-receive-from-subscriber', type=bool, help='Whether we receive payment from subscriber or client does', default=True)

    def handle(self, *args, **options):
        el = pytz.timezone('Europe/London')
        client = Client.objects.get(pk=options['client_id'])
        subscriptions = Subscription.objects.filter(subscriber__client=client)
        total = 0
        last_month = timezone.now().replace(day=1) - timezone.timedelta(days=15)  # skip DST week
        with open(options['csv_file'], 'w') as f:
            writer = csv.writer(f)
            writer.writerow(['email', 'sim', 'billing_start_date', 'prorated_charge', 'subscription_start_date', 'sim_activation_date'])
            for subscription in subscriptions:
                billing_month_start, billing_month_end = STRIPE_BILLING_TIME_CONTROL.get_month_start_and_end_containing(last_month)
                billing_month_start = coerce_to_london_time(billing_month_start)
                billing_month_end = coerce_to_london_time(billing_month_end)
                if subscription.latest_sim and subscription.latest_sim.activation_date:
                    billing_start_date = max(subscription.latest_sim.activation_date, billing_month_start, subscription.start_date)
                    if subscription.start_date > billing_month_start:
                        billing_start_date = min(subscription.start_date, billing_start_date)
                else:
                    if subscription.start_date > billing_month_end:
                        print(subscription.subscriber.email, 'no sim, started', subscription.start_date, 'after billing month', billing_month_end)
                        continue
                    elif subscription.start_date < billing_month_start:
                        print(subscription.subscriber.email, 'no sim activation, started', subscription.start_date, 'before billing month', billing_month_start)
                        continue
                    else:
                        if subscription.latest_sim:
                            billing_start_date = subscription.start_date
                        else:
                            print('No sim and no start date for ', subscription)
                            continue
                if billing_start_date > billing_month_end:
                    print(subscription.subscriber.email, 'activated', billing_start_date, 'after billing month', billing_month_end)
                    continue
#                billing_start_date = billing_start_date.astimezone(el).replace(hour=0, minute=0, second=0, microsecond=0)
                if subscription.latest_plan:
                    if options['we_receive_from_subscriber']:
                        relevant_payments = subscription.payments.filter(date__gte=billing_month_start, date__lt=billing_month_end)
                        if relevant_payments.count() != 1:
                            raise RuntimeError('Strange payments')
                        plan_charge = relevant_payments.first().amount
                        plan_charge_ex_vat = plan_charge / Decimal('1.2')
                        our_expected_profit = round(prorate_remaining_charge(subscription.latest_plan.cost_to_client, billing_month_start, billing_month_end, billing_start_date), 2)
                        prorated_charge = round(plan_charge_ex_vat - our_expected_profit, 2)
#                        print('zzz', plan_charge, plan_charge_ex_vat, our_expected_profit, prorated_charge, subscription.latest_plan.cost_to_client, billing_month_start, billing_month_end, billing_start_date)
                    else:
                        plan_charge = subscription.latest_plan.price - subscription.latest_plan.cost_to_client
                        prorated_charge = round(prorate_remaining_charge(plan_charge, billing_month_start, billing_month_end, billing_start_date), 2)
                    total += prorated_charge
                    print(subscription.subscriber.email, subscription.latest_sim, billing_start_date, prorated_charge, subscription.start_date, subscription.latest_sim.activation_date)
                    writer.writerow([subscription.subscriber.email, subscription.latest_sim, billing_start_date, prorated_charge, subscription.start_date, subscription.latest_sim.activation_date])
                else:
                    print(subscription.subscriber.email, 'no plan', subscription.pk)
        # total to 2 dp
        print('Total:', round(total, 2))
