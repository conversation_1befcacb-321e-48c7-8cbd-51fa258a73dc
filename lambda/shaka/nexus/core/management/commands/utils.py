from core.models import Subscription, Sim, NumberAssignment

def is_msisdn(identifier):
    return identifier.startswith('44') and len(identifier) == 12 and identifier.isdigit()

def is_subscription_id(identifier):
    return identifier.isdigit() and not is_msisdn(identifier) and not is_iccid(identifier) and len(identifier) < 10 and len(identifier) > 0 and Subscription.objects.filter(pk=identifier).exists()

def is_iccid(identifier):
    return len(identifier) in [19, 20] and identifier.isdigit()

def lookup_iccid(msisdn=None, subscription_id=None):
    if msisdn:
        assignment = NumberAssignment.objects.filter(msisdn=msisdn)
        if assignment.count() != 1:
            raise RuntimeError(f'No unique assignment found for {msisdn}')
        return assignment.first().sim.serial_number
    if subscription_id:
        return Subscription.objects.get(pk=subscription_id).latest_sim.serial_number
    raise RuntimeError('No identifier provided')

def lookup_client_by_iccid(iccid):
    return Sim.objects.get(serial_number=iccid).client

def lookup_subscription_by_iccid(iccid):
    return Sim.objects.get(serial_number=iccid).latest_subscription
