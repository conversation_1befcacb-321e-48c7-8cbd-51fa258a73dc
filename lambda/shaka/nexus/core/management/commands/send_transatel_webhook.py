import json
import hashlib
import hmac
import requests
import sys
from django.conf import settings
from django.core.management.base import BaseCommand



class Command(BaseCommand):
    help = 'Send webhook data with HMAC signature'

    def add_arguments(self, parser):
        parser.add_argument('url', type=str, help='URL to send the webhook data')

    def handle(self, *args, **options):
        if not settings.DEBUG:
            self.stdout.write(self.style.ERROR('This command can only be run in DEBUG mode'))
            return
        url = options['url']
        data = json.loads(sys.stdin.read())
        payload = json.dumps(data).encode('utf-8')
        hmac_sha256 = hmac.new(settings.TRANSATEL_WEBHOOK_SECRET_KEY.encode('utf-8'), payload, hashlib.sha256)
        sha256_hex = hmac_sha256.hexdigest()
        headers = {'X-TSL-Signature-256': sha256_hex}
        response = requests.post(url, data=payload, headers=headers)

        # Handle response
        if response.ok:
            self.stdout.write(self.style.SUCCESS('Webhook sent successfully!'))
        else:
            self.stdout.write(self.style.ERROR(f'Failed to send webhook: {response.status_code} {response.reason}'))
