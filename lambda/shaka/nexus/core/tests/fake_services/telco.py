from core.models import Sim, Plan


class FakeTelco:
    def __init__(self):
        self.plans = {}

    def mock_requests(self, requests_mocker):
        pass

    def bar_data(self, sim_serial):
        sim = Sim.objects.get(serial_number=sim_serial)
        sim.is_data_barred = True
        sim.save()

    def set_sim_plan(self, sim_serial, plan_code):
        sim = Sim.objects.get(serial_number=sim_serial)
        target_plan = None
        for plan in Plan.objects.filter(client=sim.latest_subscription.client):
            if plan.provider_plan_code == plan_code:
                if target_plan is not None:
                    raise ValueError('Multiple plans with the same provider code')
                target_plan = plan
        if target_plan != sim.latest_plan:
            sim.immediately_move_to_new_plan(target_plan)
        self.plans[sim_serial] = plan_code

    def sim_is_barred(self, sim_serial):
        sim = Sim.objects.get(serial_number=sim_serial)
        return sim.is_data_barred

    def sim_is_on_plan(self, sim_serial, plan_code):
        return self.plans.get(sim_serial) == plan_code
