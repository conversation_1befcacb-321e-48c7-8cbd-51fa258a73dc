from decimal import Decimal
from datetime import datetime
from pytz import timezone
from dateutil.relativedelta import relativedelta
from django.test import TestCase
from core.tests.test_clock import setup_test_clock, tear_down_test_clock
from core.time_control import STRIPE_BILLING_TIME_CONTROL, GAMMA_TIME_CONTROL, TRANSATEL_TIME_CONTROL

class TimeControlTestCase(TestCase):
    time_control = None
    timezone = None

    def setUp(self):
        super().setUp()
        self.test_clock = setup_test_clock()

    def tearDown(self):
        super().tearDown()
        tear_down_test_clock()

    @property
    def current_time(self):
        return self.test_clock.current_time

    def localize(self, naive_datetime):
        assert naive_datetime.tzinfo is None
        return self.timezone.localize(naive_datetime)

class TimeControlTestsMixin:  # pylint: disable=too-many-public-methods
    def test_start_of_next_month_is_correct_no_dst(self):
        self.test_clock.set_date('2024-01-15')
        self.assertEqual(self.time_control.start_of_next_cycle, self.localize(datetime(2024, 2, 1, 0, 0, 0)))

    def test_start_of_next_month_is_correct_in_dst(self):
        self.test_clock.set_date('2024-06-15')
        self.assertEqual(self.time_control.start_of_next_cycle, self.localize(datetime(2024, 7, 1, 0, 0, 0)))

    def test_start_of_next_month_is_correct_into_dst(self):
        self.test_clock.set_date('2024-03-15')
        self.assertEqual(self.time_control.start_of_next_cycle, self.localize(datetime(2024, 4, 1, 0, 0, 0)))

    def test_start_of_next_month_is_correct_out_of_dst(self):
        self.test_clock.set_date('2024-10-15')
        self.assertEqual(self.time_control.start_of_next_cycle, self.localize(datetime(2024, 11, 1, 0, 0, 0)))

    def test_start_of_next_month_is_correct_with_february_leap_year(self):
        self.test_clock.set_date('2024-02-15')
        self.assertEqual(self.time_control.start_of_next_cycle, self.localize(datetime(2024, 3, 1, 0, 0, 0)))

    def test_start_of_next_cycle_timestamp_is_correct(self):
        self.test_clock.set_date('2024-01-15')
        self.assertEqual(self.time_control.start_of_next_cycle_epoch_timestamp, int(self.localize(datetime(2024, 2, 1, 0, 0, 0)).timestamp()))

    def test_current_month_datetimes_are_correct_no_dst(self):
        self.test_clock.set_date('2024-01-15')
        self.assertEqual(self.time_control.current_month_datetimes, (self.localize(datetime(2024, 1, 1, 0, 0, 0)), self.localize(datetime(2024, 2, 1, 0, 0, 0))))

    def test_current_month_datetimes_are_correct_in_dst(self):
        self.test_clock.set_date('2024-06-15')
        self.assertEqual(self.time_control.current_month_datetimes, (self.localize(datetime(2024, 6, 1, 0, 0, 0)), self.localize(datetime(2024, 7, 1, 0, 0, 0))))

    def test_current_month_datetimes_are_correct_into_dst(self):
        self.test_clock.set_date('2024-03-15')
        self.assertEqual(self.time_control.current_month_datetimes, (self.localize(datetime(2024, 3, 1, 0, 0, 0)), self.localize(datetime(2024, 4, 1, 0, 0, 0))))

    def test_current_month_datetimes_are_correct_out_of_dst(self):
        self.test_clock.set_date('2024-03-15')
        self.assertEqual(self.time_control.current_month_datetimes, (self.localize(datetime(2024, 3, 1, 0, 0, 0)), self.localize(datetime(2024, 4, 1, 0, 0, 0))))

    def test_current_month_datetimes_are_correct_with_february_leap_year(self):
        self.test_clock.set_date('2024-02-15')
        self.assertEqual(self.time_control.current_month_datetimes, (self.localize(datetime(2024, 2, 1, 0, 0, 0)), self.localize(datetime(2024, 3, 1, 0, 0, 0))))

    def test_last_month_datetimes_are_correct_no_dst(self):
        self.test_clock.set_date('2024-01-15')
        self.assertEqual(self.time_control.last_month_datetimes, (self.localize(datetime(2023, 12, 1, 0, 0, 0)), self.localize(datetime(2024, 1, 1, 0, 0, 0))))

    def test_last_month_datetimes_are_correct_in_dst(self):
        self.test_clock.set_date('2024-06-15')
        self.assertEqual(self.time_control.last_month_datetimes, (self.localize(datetime(2024, 5, 1, 0, 0, 0)), self.localize(datetime(2024, 6, 1, 0, 0, 0))))

    def test_last_month_datetimes_are_correct_into_dst(self):
        self.test_clock.set_date('2024-03-15')
        self.assertEqual(self.time_control.last_month_datetimes, (self.localize(datetime(2024, 2, 1, 0, 0, 0)), self.localize(datetime(2024, 3, 1, 0, 0, 0))))

    def test_last_month_datetimes_are_correct_out_of_dst(self):
        self.test_clock.set_date('2024-10-15')
        self.assertEqual(self.time_control.last_month_datetimes, (self.localize(datetime(2024, 9, 1, 0, 0, 0)), self.localize(datetime(2024, 10, 1, 0, 0, 0))))

    def test_get_complete_months(self):
        self.test_clock.set_date('2024-01-15')
        self.assertEqual(self.time_control.get_complete_months(self.localize(datetime(2024, 1, 15, 0, 0, 0)), self.localize(datetime(2024, 3, 16, 0, 0, 0))), 2)
        self.assertEqual(self.time_control.get_complete_months(self.localize(datetime(2024, 1, 15, 0, 0, 0)), self.localize(datetime(2024, 3, 14, 0, 0, 0))), 1)

    def test_period_multiple_in_out_of_dst_month(self):
        start_dt = self.localize(datetime(2024, 1, 1, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 2, 1, 0, 0, 0))
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), 1)

    def test_period_multiple_in_multiple_out_of_dst_month(self):
        start_dt = self.localize(datetime(2024, 1, 1, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 2, 2, 0, 0, 0))
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round(1 + Decimal('1')/Decimal('29'), 5))

    def test_period_multiple_in_multiple_out_of_dst_month_off_either_end(self):
        start_dt = self.localize(datetime(2024, 1, 29, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 2, 2, 0, 0, 0))
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round((Decimal('3')/Decimal('31')) + Decimal('1')/Decimal('29'), 5))

    def test_period_multiple_entirely_within_out_of_dst_month(self):
        start_dt = self.localize(datetime(2024, 1, 2, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 1, 3, 12, 0, 0))
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round((1/Decimal(31.0))*Decimal('1.5'), 5))

    def test_period_multiple_in_dst_month(self):
        start_dt = self.localize(datetime(2024, 6, 2, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 6, 3, 12, 0, 0))
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round((1/Decimal(30.0))*Decimal('1.5'), 5))

    def test_period_multiple_into_dst_month(self):
        start_dt = self.localize(datetime(2024, 3, 22, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 4, 2, 12, 0, 0))
        hours_in_march = Decimal((31 * 24) - 1)
        march_hour = 1/hours_in_march
        march_days = (10 * 24 * march_hour) - march_hour
        april_days = ((1/Decimal(30.0))*Decimal('1.5'))
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round(march_days + april_days, 5))

    def test_period_multiple_out_of_dst_month(self):
        start_dt = self.localize(datetime(2024, 10, 22, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 11, 2, 12, 0, 0))
        hours_in_october = Decimal((31 * 24) + 1)
        october_hour = 1/hours_in_october
        october_days = (10 * 24 * october_hour) + october_hour
        november_days = ((1/Decimal(30.0))*Decimal('1.5'))
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round(october_days + november_days, 5))

    def test_period_multiple_with_february_leap_year(self):
        start_dt = self.localize(datetime(2024, 2, 28, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 3, 2, 12, 0, 0))
        february_days = ((1/Decimal('29.0'))*2)
        hours_in_march = Decimal((31 * 24) - 1)
        march_hour = 1/hours_in_march
        march_days = Decimal('1.5') * 24 * march_hour
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round(february_days + march_days, 5))

    def test_period_multiple_with_february_non_leap_year(self):
        start_dt = self.localize(datetime(2023, 2, 27, 0, 0, 0))
        end_dt = self.localize(datetime(2023, 3, 2, 12, 0, 0))
        february_days = ((1/Decimal(28.0))*2)
        hours_in_march = Decimal((31 * 24) - 1)
        march_hour = 1/hours_in_march
        march_days = Decimal('1.5') * 24 * march_hour
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round(february_days + march_days, 5))

    def test_period_multiple_across_two_months(self):
        start_dt = self.localize(datetime(2024, 1, 28, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 2, 2, 12, 0, 0))
        january_days = ((1/Decimal('31.0'))*4)
        february_days = ((1/Decimal('29.0'))*Decimal('1.5'))
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round(january_days + february_days, 5))

    def test_period_multiple_across_a_few_months(self):
        start_dt = self.localize(datetime(2024, 1, 28, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 3, 2, 12, 0, 0))
        january_days = ((1/Decimal('31.0'))*4)
        february_days = ((1/Decimal('29.0'))*29)
        hours_in_march = Decimal((31 * 24) - 1)
        march_hour = 1/hours_in_march
        march_days = Decimal('1.5') * 24 * march_hour
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round(january_days + february_days + march_days, 5))

    def test_period_multiple_across_a_few_months_with_dst(self):
        start_dt = self.localize(datetime(2024, 3, 28, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 5, 2, 12, 0, 0))
        hours_in_march = Decimal((31 * 24) - 1)
        march_hour = 1/hours_in_march
        march_days = (4 * 24 * march_hour) - march_hour
        april_days = 1
        may_days = ((1/Decimal(31.0))*Decimal('1.5'))
        self.assertEqual(round(self.time_control.period_multiple(start_dt, end_dt), 5), round(march_days + april_days + may_days, 5))

    def test_period_multiple_with_end_before_start(self):
        start_dt = self.localize(datetime(2024, 1, 28, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 1, 2, 12, 0, 0))
        with self.assertRaises(ValueError):
            self.time_control.period_multiple(start_dt, end_dt)

    def test_period_multiple_with_end_equals_start(self):
        start_dt = self.localize(datetime(2024, 1, 28, 0, 0, 0))
        end_dt = self.localize(datetime(2024, 1, 28, 0, 0, 0))
        self.assertEqual(self.time_control.period_multiple(start_dt, end_dt), 0)

    def test_previous_period_starts(self):
        over_year = self.localize(datetime(2024, 1, 1, 0, 0, 0))
        self.assertEqual(self.time_control.get_previous_period_start_and_end(over_year), (self.localize(datetime(2023, 12, 1, 0, 0, 0)), self.localize(datetime(2024, 1, 1, 0, 0, 0))))
        from_mid_month = self.localize(datetime(2024, 1, 15, 0, 0, 0))
        self.assertEqual(self.time_control.get_previous_period_start_and_end(from_mid_month), (self.localize(datetime(2023, 12, 1, 0, 0, 0)), self.localize(datetime(2024, 1, 1, 0, 0, 0))))
        from_after_dst_switch = self.localize(datetime(2024, 3, 31, 12, 0, 0))
        self.assertEqual(self.time_control.get_previous_period_start_and_end(from_after_dst_switch), (self.localize(datetime(2024, 2, 1, 0, 0, 0)), self.localize(datetime(2024, 3, 1, 0, 0, 0))))
        from_dst_month = self.localize(datetime(2024, 6, 15, 0, 0, 0))
        self.assertEqual(self.time_control.get_previous_period_start_and_end(from_dst_month), (self.localize(datetime(2024, 5, 1, 0, 0, 0)), self.localize(datetime(2024, 6, 1, 0, 0, 0))))
        from_before_dst_switch_back = self.localize(datetime(2024, 10, 15, 12, 0, 0))
        self.assertEqual(self.time_control.get_previous_period_start_and_end(from_before_dst_switch_back), (self.localize(datetime(2024, 9, 1, 0, 0, 0)), self.localize(datetime(2024, 10, 1, 0, 0, 0))))
        from_after_dst_switch_back = self.localize(datetime(2024, 10, 31, 12, 0, 0))
        self.assertEqual(self.time_control.get_previous_period_start_and_end(from_after_dst_switch_back), (self.localize(datetime(2024, 9, 1, 0, 0, 0)), self.localize(datetime(2024, 10, 1, 0, 0, 0))))
        back_into_dst = self.localize(datetime(2024, 11, 2, 0, 0, 0))
        self.assertEqual(self.time_control.get_previous_period_start_and_end(back_into_dst), (self.localize(datetime(2024, 10, 1, 0, 0, 0)), self.localize(datetime(2024, 11, 1, 0, 0, 0))))

    def test_apply_relative_delta(self):
        fortnight = relativedelta(days=14)
        fortnight_before_dst = self.localize(datetime(2024, 3, 22, 0, 0, 0))
        self.assertEqual(self.time_control.apply_relative_delta(fortnight_before_dst, fortnight), self.localize(datetime(2024, 4, 5, 0, 0, 0)))
        inside_dst = self.localize(datetime(2024, 6, 15, 0, 0, 0))
        self.assertEqual(self.time_control.apply_relative_delta(inside_dst, fortnight), self.localize(datetime(2024, 6, 29, 0, 0, 0)))
        outside_dst = self.localize(datetime(2024, 1, 15, 0, 0, 0))
        self.assertEqual(self.time_control.apply_relative_delta(outside_dst, fortnight), self.localize(datetime(2024, 1, 29, 0, 0, 0)))
        going_out_of_dst = self.localize(datetime(2024, 10, 25, 0, 0, 0))
        self.assertEqual(self.time_control.apply_relative_delta(going_out_of_dst, fortnight), self.localize(datetime(2024, 11, 8, 0, 0, 0)))

    def test_start_and_end_of_month_containing(self):
        start, end = self.time_control.get_month_start_and_end_containing(self.localize(datetime(2024, 1, 15, 0, 0, 0)))
        self.assertEqual(start, self.localize(datetime(2024, 1, 1, 0, 0, 0)))
        self.assertEqual(end, self.localize(datetime(2024, 2, 1, 0, 0, 0)))
        dst_start, dst_end = self.time_control.get_month_start_and_end_containing(self.localize(datetime(2024, 6, 15, 0, 0, 0)))
        self.assertEqual(dst_start, self.localize(datetime(2024, 6, 1, 0, 0, 0)))
        self.assertEqual(dst_end, self.localize(datetime(2024, 7, 1, 0, 0, 0)))
        into_dst_start, into_dst_end = self.time_control.get_month_start_and_end_containing(self.localize(datetime(2024, 3, 15, 0, 0, 0)))
        self.assertEqual(into_dst_start, self.localize(datetime(2024, 3, 1, 0, 0, 0)))
        self.assertEqual(into_dst_end, self.localize(datetime(2024, 4, 1, 0, 0, 0)))
        out_of_dst_start, out_of_dst_end = self.time_control.get_month_start_and_end_containing(self.localize(datetime(2024, 10, 15, 0, 0, 0)))
        self.assertEqual(out_of_dst_start, self.localize(datetime(2024, 10, 1, 0, 0, 0)))
        self.assertEqual(out_of_dst_end, self.localize(datetime(2024, 11, 1, 0, 0, 0)))

    def test_end_of_today(self):
        self.test_clock.set_datetime('2024-01-15 10:20:30')
        self.assertEqual(self.time_control.end_of_today, self.localize(datetime(2024, 1, 16, 0, 0, 0)))
        self.test_clock.set_datetime('2024-01-15 00:00:00')
        self.assertEqual(self.time_control.end_of_today, self.localize(datetime(2024, 1, 16, 0, 0, 0)))
        self.test_clock.set_datetime('2024-01-15 20:10:00')
        self.assertEqual(self.time_control.end_of_today, self.localize(datetime(2024, 1, 16, 0, 0, 0)))
        self.test_clock.set_datetime('2024-03-30 00:00:00')
        self.assertEqual(self.time_control.end_of_today, self.localize(datetime(2024, 3, 31, 0, 0, 0)))
        self.test_clock.set_datetime('2024-03-31 00:00:00')
        self.assertEqual(self.time_control.end_of_today, self.localize(datetime(2024, 4, 1, 0, 0, 0)))
        self.test_clock.set_datetime('2024-10-26 00:00:00')
        self.assertEqual(self.time_control.end_of_today, self.localize(datetime(2024, 10, 27, 0, 0, 0)))
        self.test_clock.set_datetime('2024-10-27 00:00:00')
        self.assertEqual(self.time_control.end_of_today, self.localize(datetime(2024, 10, 28, 0, 0, 0)))
        self.test_clock.set_datetime('2024-10-27 01:30:00')
        self.assertEqual(self.time_control.end_of_today, self.localize(datetime(2024, 10, 28, 0, 0, 0)))
        self.test_clock.set_datetime('2024-10-27 12:00:00')
        self.assertEqual(self.time_control.end_of_today, self.localize(datetime(2024, 10, 28, 0, 0, 0)))

class TransatelTimeControlTestCase(TimeControlTestCase, TimeControlTestsMixin):
    time_control = TRANSATEL_TIME_CONTROL
    timezone = timezone('Europe/Paris')

class GammaTimeControlTestCase(TimeControlTestCase, TimeControlTestsMixin):
    time_control = GAMMA_TIME_CONTROL
    timezone = timezone('Europe/London')

class StripeBillingTimeControlTestCase(TimeControlTestCase, TimeControlTestsMixin):
    time_control = STRIPE_BILLING_TIME_CONTROL
    timezone = timezone('Europe/London')

class CrossTimezoneTimeControlTests(TimeControlTestCase):
    def uk_dt(self, naive_dt):
        return timezone('Europe/London').localize(naive_dt)

    def fr_dt(self, naive_dt):
        return timezone('Europe/Paris').localize(naive_dt)

    def test_period_multiple_across_two_timezones(self):
        french_control = TRANSATEL_TIME_CONTROL
        british_control = GAMMA_TIME_CONTROL
        start_uk_dt = self.uk_dt(datetime(2024, 1, 1, 0, 0, 0))
        end_uk_dt = self.uk_dt(datetime(2024, 2, 1, 0, 0, 0))
        self.assertEqual(round(french_control.period_multiple(start_uk_dt, end_uk_dt), 3), round(1, 3))
        self.assertEqual(round(british_control.period_multiple(start_uk_dt, end_uk_dt), 3), round(1, 3))
        start_fr_dt = self.fr_dt(datetime(2024, 1, 1, 0, 0, 0))
        end_fr_dt = self.fr_dt(datetime(2024, 2, 1, 0, 0, 0))
        self.assertEqual(round(french_control.period_multiple(start_fr_dt, end_fr_dt), 3), round(1, 3))
        self.assertEqual(round(british_control.period_multiple(start_fr_dt, end_fr_dt), 3), round(1, 3))

    def test_ensure_timezone(self):
        french_control = TRANSATEL_TIME_CONTROL
        british_control = GAMMA_TIME_CONTROL
        with self.assertRaises(ValueError):
            french_control.ensure_timezone(datetime(2024, 1, 1, 0, 0, 0))
            british_control.ensure_timezone(datetime(2024, 1, 1, 0, 0, 0))
        other_timezone = timezone('Turkey')
        dt = other_timezone.localize(datetime(2024, 1, 1, 12, 0, 0))
        self.assertEqual(french_control.ensure_timezone(dt), self.fr_dt(datetime(2024, 1, 1, 10, 0, 0)))
        self.assertEqual(british_control.ensure_timezone(dt), self.uk_dt(datetime(2024, 1, 1, 9, 0, 0)))

    def test_start_and_end_containing_accounts_for_tz_differences(self):
        british_control = GAMMA_TIME_CONTROL
        french_control = TRANSATEL_TIME_CONTROL
        end_of_month_uk = self.uk_dt(datetime(2024, 1, 31, 23, 59, 59))
        start_uk, end_uk = british_control.get_month_start_and_end_containing(end_of_month_uk)
        self.assertEqual(start_uk, self.uk_dt(datetime(2024, 1, 1, 0, 0, 0)))
        self.assertEqual(end_uk, self.uk_dt(datetime(2024, 2, 1, 0, 0, 0)))
        start_fr, end_fr = french_control.get_month_start_and_end_containing(end_of_month_uk)
        self.assertEqual(start_fr, self.fr_dt(datetime(2024, 2, 1, 0, 0, 0)))
        self.assertEqual(end_fr, self.fr_dt(datetime(2024, 3, 1, 0, 0, 0)))
