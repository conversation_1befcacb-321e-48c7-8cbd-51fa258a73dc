# from decimal import Decimal
# from datetime import timedelta
# from django.test import LiveServerTestCase, override_settings
from django.test.testcases import LiveServerThread
from django.core.servers.basehttp import ThreadedWSGIServer
# from django.conf import settings
# from django.utils import timezone
# from core.tests.factories import PlanChangeFactory, SubscriptionPaymentFactory, SubscriptionFactory, DiscountPerkFactory
# from core.models import PlanDiscount, SubscriptionPayment
# from core.plan_change import PlanChangeValidationError, PlanChangeError
# from core.tests.fake_services import FakeServices
# from core.tests.scenario import Scenario
# from core.billing import prorate_remaining_charge
# from core.utils import format_datetime_for_step_functions


def make_wsgi_server_with_reuse(_, *args, **kwargs):
    kwargs['allow_reuse_address'] = True
    return ThreadedWSGIServer(*args, **kwargs)


class LiveServerThreadWithReuse(LiveServerThread):
    server_class = make_wsgi_server_with_reuse


# @override_settings(DEBUG=True)
# class DiscountTestCase(LiveServerTestCase):
#     server_thread_class = LiveServerThreadWithReuse
#     stripe_backend = 'fake'
#     port = 8050
#     host = "0.0.0.0"

#     def setUp(self):
#         super().setUp()
#         self.fake_services = FakeServices(stripe_backend=self.stripe_backend)
#         self.fake_services.set_up()
#         self.scenario = Scenario(self.fake_services)
#         self.subscription = self.scenario.setup_subscription_with_billing('2024-01-15')

#     def tearDown(self):
#         self.fake_services.tear_down()
#         super().tearDown()

#     def wait_for(self, fn, timeout=5):
#         start = time.time()
#         while time.time() - start < timeout:
#             if fn():
#                 return
#             time.sleep(0.1)
#         raise TimeoutError()

# class FlatDiscountPerkBillingTestCase(DiscountTestCase):
#     def setUp(self):
#         super().setUp()
#         subscriber = self.subscription.subscriber
#         subscriber.perk_points = 10000
#         subscriber.save()
#         self.discount_perk = DiscountPerkFactory(client=self.subscription.client, points=10, plan_discount__discount_type=PlanDiscount.DiscountType.FLAT, plan_discount__discount_amount=5.00)
#         self.discount = self.discount_perk.plan_discount
#         self.discount.sync_to_gateway_if_necessary()

#     def test_coupon_gets_created(self):
#         assert self.discount.billing_discount_id

#     def test_flat_coupon_gets_applied(self):
#         stripe = self.fake_services.stripe
#         base_price = self.subscription.latest_plan.price
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price,2)).exists(), 30)
#         self.subscription.subscriber.claim_perk_via_points(self.discount_perk)
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price - 5,2)).exists(), 30)

#     def test_multiple_flat_coupons_get_applied(self):
#         stripe = self.fake_services.stripe
#         base_price = self.subscription.latest_plan.price
#         self.discount.discount_duration_months = 3
#         self.discount.save()
#         self.discount.sync_to_gateway_if_necessary()
#         second_discount = DiscountPerkFactory(client=self.subscription.client, points=10, plan_discount__discount_type=PlanDiscount.DiscountType.FLAT, plan_discount__discount_amount=5.00, plan_discount__discount_duration_months=3)
#         second_discount.plan_discount.sync_to_gateway_if_necessary()
#         self.subscription.subscriber.claim_perk_via_points(self.discount_perk)
#         self.subscription.subscriber.claim_perk_via_points(second_discount)
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price - 10,2)).exists(), 30)

#     def test_even_more_flat_coupons_get_applied(self):
#         stripe = self.fake_services.stripe
#         base_price = self.subscription.latest_plan.price
#         self.discount.discount_duration_months = 3
#         self.discount.save()
#         self.discount.sync_to_gateway_if_necessary()
#         second_discount = DiscountPerkFactory(client=self.subscription.client, points=10, plan_discount__discount_type=PlanDiscount.DiscountType.FLAT, plan_discount__discount_amount=5.00, plan_discount__discount_duration_months=3)
#         second_discount.plan_discount.sync_to_gateway_if_necessary()
#         third_discount = DiscountPerkFactory(client=self.subscription.client, points=10, plan_discount__discount_type=PlanDiscount.DiscountType.FLAT, plan_discount__discount_amount=5.00, plan_discount__discount_duration_months=3)
#         third_discount.plan_discount.sync_to_gateway_if_necessary()
#         self.subscription.subscriber.claim_perk_via_points(self.discount_perk)
#         self.subscription.subscriber.claim_perk_via_points(second_discount)
#         self.subscription.subscriber.claim_perk_via_points(third_discount)
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price - 15,2)).exists(), 30)

#     def test_staggered_flat_coupons_fall_off_correctly(self):
#         stripe = self.fake_services.stripe
#         base_price = self.subscription.latest_plan.price
#         self.discount.discount_duration_months = 2
#         self.discount.save()
#         self.discount.sync_to_gateway_if_necessary()
#         self.subscription.subscriber.claim_perk_via_points(self.discount_perk)
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price - 5,2)).exists(), 30)
#         second_discount = DiscountPerkFactory(client=self.subscription.client, points=10, plan_discount__discount_type=PlanDiscount.DiscountType.FLAT, plan_discount__discount_amount=5.00, plan_discount__discount_duration_months=3)
#         second_discount.plan_discount.sync_to_gateway_if_necessary()
#         self.subscription.subscriber.claim_perk_via_points(second_discount)
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price - 10,2)).exists(), 30)
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price - 5,2)).exists(), 30)


# class PercentDiscountPerkBillingTestCase(DiscountTestCase):
#     def setUp(self):
#         super().setUp()
#         subscriber = self.subscription.subscriber
#         subscriber.perk_points = 10000
#         subscriber.save()
#         self.discount_perk = DiscountPerkFactory(client=self.subscription.client, points=10, plan_discount__discount_type=PlanDiscount.DiscountType.PERCENTAGE, plan_discount__discount_percentage=10.0)
#         self.discount = self.discount_perk.plan_discount
#         self.discount.sync_to_gateway_if_necessary()

#     def test_coupon_gets_created(self):
#         assert self.discount.billing_discount_id

#     def test_percentage_coupon_gets_applied(self):
#         stripe = self.fake_services.stripe
#         base_price = self.subscription.latest_plan.price
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price,2)).exists(), 30)
#         self.subscription.subscriber.claim_perk_via_points(self.discount_perk)
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price * Decimal('0.9'),2)).exists(), 30)

#     def test_multiple_percent_coupons_get_applied(self):
#         print(self.subscription.billing_subscription_id)
#         stripe = self.fake_services.stripe
#         base_price = self.subscription.latest_plan.price
#         self.discount.discount_duration_months = 3
#         self.discount.save()
#         self.discount.sync_to_gateway_if_necessary()
#         second_discount = DiscountPerkFactory(client=self.subscription.client, points=10, plan_discount__discount_type=PlanDiscount.DiscountType.PERCENTAGE, plan_discount__discount_percentage=20.00, plan_discount__discount_duration_months=3)
#         second_discount.plan_discount.sync_to_gateway_if_necessary()
#         self.subscription.subscriber.claim_perk_via_points(self.discount_perk)
#         self.subscription.subscriber.claim_perk_via_points(second_discount)
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round(base_price * Decimal('0.9') * Decimal('0.8'),2)).exists(), 30)


# class CombinedDiscountPerkBillingTestCase(DiscountTestCase):
#     def setUp(self):
#         super().setUp()
#         subscriber = self.subscription.subscriber
#         subscriber.perk_points = 10000
#         subscriber.save()
#         self.discount_perk = DiscountPerkFactory(client=self.subscription.client, points=10, plan_discount__discount_type=PlanDiscount.DiscountType.FLAT, plan_discount__discount_amount=5.0)
#         self.discount = self.discount_perk.plan_discount
#         self.discount.sync_to_gateway_if_necessary()

#     def test_percentage_and_flat_coupon_gets_applied(self):
#         print(self.subscription.billing_subscription_id)
#         stripe = self.fake_services.stripe
#         base_price = self.subscription.latest_plan.price
#         self.discount.discount_duration_months = 3
#         self.discount.save()
#         self.discount.sync_to_gateway_if_necessary()
#         second_discount = DiscountPerkFactory(client=self.subscription.client, points=10, plan_discount__discount_type=PlanDiscount.DiscountType.PERCENTAGE, plan_discount__discount_percentage=20.00, plan_discount__discount_duration_months=3)
#         second_discount.plan_discount.sync_to_gateway_if_necessary()
#         self.subscription.subscriber.claim_perk_via_points(self.discount_perk)
#         self.subscription.subscriber.claim_perk_via_points(second_discount)
#         stripe.advance_to_start_of_next_billing_cycle()
#         stripe.advance_time_past_collection()
#         self.wait_for(lambda: SubscriptionPayment.objects.filter(subscription=self.subscription, amount=round((base_price - 5) * Decimal('0.8'),2)).exists(), 30)
