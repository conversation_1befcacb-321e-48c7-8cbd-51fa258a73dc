from datetime import UTC
import factory
from core import models


DEFAULT_DATA_LIMIT_GB = 100

class ProviderFactory(factory.django.DjangoModelFactory):
    name = 'transatel'
    class Meta:
        model = models.Provider
        django_get_or_create = ('name',)


class SMSConfigurationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.SMSConfiguration

    provider = 'fake'


class ClientFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Client

    provider = factory.SubFactory(ProviderFactory)
    sms_config = factory.RelatedFactory(SMSConfigurationFactory, 'client')
    plans_assigned_externally = False
    obfuscated_id = factory.Faker('uuid4')


class DashboardUserFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.DashboardUser

    client = factory.SubFactory(ClientFactory)
    username = factory.Faker('user_name')


class PlanComponentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PlanComponent

    provider = factory.SubFactory(ProviderFactory)
    default_price = factory.Faker('random_int', min=10, max=20)
    default_cost = factory.Faker('random_int', min=5, max=10)

    class Params:
        sms = factory.Trait(
            dimension=models.PlanComponent.Dimension.SMS
        )
        voice = factory.Trait(
            dimension=models.PlanComponent.Dimension.VOICE
        )
        data = factory.Trait(
            dimension=models.PlanComponent.Dimension.DATA
        )

class PlanComponentOfferingFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PlanComponentOffering

    available_for_new_plans = True

class SMSComponentOfferingFactory(PlanComponentOfferingFactory):
    plan_component = factory.SubFactory(PlanComponentFactory, sms=True)

class VoiceComponentOfferingFactory(PlanComponentOfferingFactory):
    plan_component = factory.SubFactory(PlanComponentFactory, voice=True)

class DataComponentOfferingFactory(PlanComponentOfferingFactory):
    plan_component = factory.SubFactory(PlanComponentFactory, data=True)

class PlanFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Plan

    price = factory.Faker('random_int', min=10, max=20)
    provider = factory.SelfAttribute('client.provider')
    name = factory.Faker('word', part_of_speech='noun')
    custom_data_limit = DEFAULT_DATA_LIMIT_GB
    sms_component_offering = factory.SubFactory(SMSComponentOfferingFactory, client=factory.SelfAttribute('..client'))
    voice_component_offering = factory.SubFactory(VoiceComponentOfferingFactory, client=factory.SelfAttribute('..client'))
    data_component_offering = factory.SubFactory(DataComponentOfferingFactory, client=factory.SelfAttribute('..client'))


    class Params:
        top_tier = factory.Trait(
            price=100,
            custom_data_limit=100
        )
        mid_tier = factory.Trait(
            price=50,
            custom_data_limit=30
        )
        bottom_tier = factory.Trait(
            price=10,
            custom_data_limit=10
        )
        profit_12 = factory.Trait(
            price=20,
            custom_data_limit=100,
            sms_component_offering=factory.SubFactory(
                SMSComponentOfferingFactory,
                client=factory.SelfAttribute('..client'),
                plan_component__default_price=2
            ),
            voice_component_offering=factory.SubFactory(
                VoiceComponentOfferingFactory,
                client=factory.SelfAttribute('..client'),
                plan_component__default_price=2
            ),
            data_component_offering=factory.SubFactory(
                DataComponentOfferingFactory,
                client=factory.SelfAttribute('..client'),
                plan_component__default_price=4
            )
        )


class SimSubscriptionAssignmentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.SimSubscriptionAssignment


class SimPlanAssignmentFactory(factory.django.DjangoModelFactory):
    start_date = factory.Faker('date_time_between', start_date='-30d', end_date='-1d', tzinfo=UTC)

    class Meta:
        model = models.SimPlanAssignment


class NumberAssignmentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.NumberAssignment

    phone_number = factory.Faker('numerify', text='44########')
    start_date = factory.Faker('date_time_between', start_date='-30d', end_date='-1d', tzinfo=UTC)


class SimFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Sim

    serial_number = factory.Faker('numerify', text='####################')
    activation_date = factory.Faker('date_time_between', start_date='-30d', end_date='-1d', tzinfo=UTC)

    @factory.post_generation
    def plan(self, _, extracted, **kwargs):
        if extracted:
            SimPlanAssignmentFactory(sim=self, plan=extracted, start_date=self.activation_date)

    class Params:
        port_in_date = None
        with_top_tier_plan = factory.Trait(
            any_plan=factory.RelatedFactory(
                SimPlanAssignmentFactory,
                'sim',
                start_date=factory.SelfAttribute('...start_date'),
                # This wizardrdy is to get the client from the subscription factory that's probably creating this sim via an assignment
                plan=factory.SubFactory(PlanFactory, client=factory.SelfAttribute('....subscription.subscriber.client'), top_tier=True)
            )
        )
        with_mid_tier_plan = factory.Trait(
            any_plan=factory.RelatedFactory(
                SimPlanAssignmentFactory,
                'sim',
                start_date=factory.SelfAttribute('...start_date'),
                plan=factory.SubFactory(PlanFactory, client=factory.SelfAttribute('....subscription.subscriber.client'), mid_tier=True)
            )
        )
        with_number = factory.Trait(
            any_number=factory.RelatedFactory(
                NumberAssignmentFactory,
                'sim',
                start_date=factory.SelfAttribute('...start_date'),
            )
        )
        with_port_in = factory.Trait(
            first_number=factory.RelatedFactory(
                NumberAssignmentFactory,
                'sim',
                start_date=factory.SelfAttribute('...start_date'),
                end_date=factory.LazyAttribute(lambda o: o.factory_parent.port_in_date)
            ),
            second_number=factory.RelatedFactory(
                NumberAssignmentFactory,
                'sim',
                start_date=factory.LazyAttribute(lambda o: o.factory_parent.port_in_date)
            )
        )


class SubscriberFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Subscriber

    client = factory.SubFactory(ClientFactory)
    join_date = factory.Faker('date_time_between', start_date='-30d', end_date='-1d', tzinfo=UTC)
    email = factory.Faker('email')


class SubscriptionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.Subscription

    start_date = factory.Faker('date_time_between', start_date='-30d', end_date='-1d', tzinfo=UTC)
    subscriber = factory.SubFactory(SubscriberFactory, join_date=factory.SelfAttribute('..start_date'))
    status = models.Subscription.Statuses.ACTIVE

    @factory.post_generation
    def plan(self, _, extracted, **kwargs):
        if extracted:
            sim = SimFactory(plan=extracted)
            SimSubscriptionAssignmentFactory(sim=sim, subscription=self, start_date=self.start_date)
            self.save()

    class Params:
        old = factory.Trait(
            start_date=factory.Faker('date_time_between', start_date='-100d', end_date='-40d', tzinfo=UTC)
        )
        using = factory.Trait(
            any_sim=factory.RelatedFactory(
                SimSubscriptionAssignmentFactory,
                'subscription',
                sim=factory.SubFactory(SimFactory, with_top_tier_plan=True, with_number=True, activation_date=factory.SelfAttribute('...start_date')),
                start_date=factory.SelfAttribute('..start_date'))
        )
        using_mid_tier = factory.Trait(
            any_sim=factory.RelatedFactory(
                SimSubscriptionAssignmentFactory,
                'subscription',
                sim=factory.SubFactory(SimFactory, with_mid_tier_plan=True, with_number=True, activation_date=factory.SelfAttribute('...start_date')),
                start_date=factory.SelfAttribute('..start_date'))
        )

class SubscriptionWithPortInFactory(SubscriptionFactory):
    ported_sim = factory.RelatedFactory(
        SimSubscriptionAssignmentFactory,
        'subscription',
        sim=factory.SubFactory(
            SimFactory,
            with_top_tier_plan=True,
            with_port_in=True,
            port_in_date=factory.SelfAttribute('...port_in_date'),
        ),
        start_date=factory.SelfAttribute('..start_date'))


    class Params:
        port_in_date = None
        using = factory.Trait(
        )


class MarketingEligibilityFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.MarketingEligibility



class SMSMessageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.SMSMessage

    client = factory.SubFactory(ClientFactory)
    message = factory.Faker('sentence')
    initiator = factory.SubFactory(DashboardUserFactory)
    marketing_eligibility = factory.SubFactory(MarketingEligibilityFactory)

    class Params:
        to_send = factory.Trait(
            status=models.SMSMessage.Status.TO_SEND,
            send_on=factory.Faker('date_time_between', start_date='+30d', end_date='+40d', tzinfo=UTC)
        )
        sending = factory.Trait(
            status=models.SMSMessage.Status.SENDING
        )
        sent = factory.Trait(
            status=models.SMSMessage.Status.SENT
        )

class PlanChangeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PlanChange

    subscription = factory.SubFactory(
                SubscriptionFactory,
                plan=factory.SubFactory(PlanFactory, mid_tier=True, client=factory.LazyAttribute(lambda o: o.factory_parent.factory_parent.subscriber.client)))
    execution_start_time = factory.Faker('date_time_between', start_date='-30d', end_date='-1d', tzinfo=UTC)
    execution_id = factory.Faker('uuid4')
    change_type = models.PlanChange.ChangeType.UPGRADE
    target_plan = factory.SubFactory(PlanFactory, client=factory.SelfAttribute('..subscription.subscriber.client'), top_tier=True)
    target_plan_change = None
    status = models.PlanChange.Status.IN_PROGRESS

    class Params:
        upgrade = factory.Trait(
            change_type=models.PlanChange.ChangeType.UPGRADE,
            target_plan=factory.SubFactory(PlanFactory, client=factory.SelfAttribute('..subscription.subscriber.client'), top_tier=True)
        )
        downgrade = factory.Trait(
            change_type=models.PlanChange.ChangeType.DOWNGRADE,
            target_plan=factory.SubFactory(PlanFactory, client=factory.SelfAttribute('..subscription.subscriber.client'), bottom_tier=True)
        )
        cancel_change = factory.Trait(
            cancel_downgrade=True
        )
        cancel_downgrade = factory.Trait(
            change_type=models.PlanChange.ChangeType.CANCEL_CHANGE,
            target_plan=None,
            target_plan_change=factory.SubFactory('core.tests.factories.PlanChangeFactory', downgrade=True, subscription=factory.SelfAttribute('..subscription'))
        )
        cancelled = factory.Trait(
            status=models.PlanChange.Status.CANCELLED,
            sub_execution_id = 'sub'
        )
        complete = factory.Trait(
            status=models.PlanChange.Status.COMPLETE,
            sub_execution_id = 'sub'
        )
        locked = factory.Trait(
            status=models.PlanChange.Status.LOCKED,
            sub_execution_id = 'sub'
        )


class PaymentIntegrationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PaymentIntegration

    client = factory.SubFactory(ClientFactory)
    public_credentials = factory.Faker('uuid4')
    secret_credentials = factory.Faker('uuid4')
    webhook_secret = factory.Faker('uuid4')


class SubscriptionPaymentFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.SubscriptionPayment

    subscription = factory.SubFactory(SubscriptionFactory)
    amount = factory.Faker('random_int', min=10, max=20)
    billing_invoice_id = factory.Faker('uuid4')
    date = factory.Faker('date_time_between', start_date='-30d', end_date='-1d', tzinfo=UTC)


class BoltOnFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.BoltOn

    name = factory.Faker('word')
    provider_code = factory.Faker('word')
    provider = factory.SubFactory(ProviderFactory)
    description = factory.Faker('sentence')
    default_price = factory.Faker('random_int', min=10, max=20)
    default_cost = factory.Faker('random_int', min=5, max=10)
    bolt_on_type = 'binary'
    duration_type = 'day'
    billing_calendar_sync = True
    data_gb = factory.Faker('random_int', min=10, max=20)


class BoltOnOfferingFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.BoltOnOffering

    client = factory.SubFactory(ClientFactory)
    available_for_new_selection = True
    bolt_on = factory.SubFactory(BoltOnFactory, provider=factory.SelfAttribute('..client.provider'))


class ClientBoltOnFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.ClientBoltOn

    client = factory.SubFactory(ClientFactory)
    price = factory.Faker('random_int', min=10, max=20)
    status = 'enabled'
    offering = factory.SubFactory(BoltOnOfferingFactory, client=factory.SelfAttribute('..client'))


class PlanDiscountFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PlanDiscount

    discount_type = 'percentage'
    discount_percentage = factory.Faker('random_int', min=10, max=20)
    discount_duration_months = factory.Faker('random_int', min=1, max=12)


class DiscountPerkFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.DiscountPerk

    client = factory.SubFactory(ClientFactory)
    name = factory.Faker('word')
    plan_discount = factory.RelatedFactory(PlanDiscountFactory, 'discount_perk')
    enabled = True
    elective_redemption_cost = factory.Faker('random_int', min=10, max=20)
    electively_redeemable = True

    @factory.post_generation
    def points(self, _, extracted, **kwargs):
        if extracted is not None:
            self.electively_redeemable = True
            self.elective_redemption_cost = extracted
            self.save()

class VoucherPerkFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.VoucherPerk

    client = factory.SubFactory(ClientFactory)
    name = factory.Faker('word')
    merchant_name = factory.Faker('word')
    code = factory.Faker('uuid4')
    instructions = factory.Faker('sentence')

class BoltOnPerkFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.BoltOnPerk

    client = factory.SubFactory(ClientFactory)
    bolt_on = factory.SubFactory(ClientBoltOnFactory, client=factory.SelfAttribute('..client'))


class DemoUsageFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.DemoUsage

    client = factory.SubFactory(ClientFactory)
    value = factory.Faker('random_int', min=10, max=20)
    dimension = factory.Faker('random_element', elements=['data', 'sms', 'voice'])

class PerkRedemptionFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = models.PerkRedemption

    subscriber = factory.SubFactory(SubscriberFactory)
    perk = factory.SubFactory(VoucherPerkFactory)
    fulfilment_status = models.PerkRedemption.FulfilmentStatus.PENDING
