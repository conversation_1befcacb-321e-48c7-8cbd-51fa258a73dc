import time
from core.models import Subscription
from core.billing import get_eu_roaming_days_this_month
from core.slack import send_slack_message

def update_roaming_days():
    for subscription in Subscription.objects.all():
        if subscription.latest_sim and subscription.uk_prefixed_phone_number and not subscription.subscriber.client.provider.is_demo:
            roaming_days = get_eu_roaming_days_this_month(subscription)
            subscription.update_eu_roaming_days(roaming_days)
            if subscription.eu_roaming_days_this_month >= subscription.total_available_eu_roaming_days:
                send_slack_message(f'Roaming days exceeded, will disable roaming for {subscription} on {subscription.client}')
            time.sleep(2)
