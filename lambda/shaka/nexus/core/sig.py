import base64
import json
import hmac
import hashlib
import secrets
import os
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
from django.conf import settings

def generate_hmac(data_dict):
    json_data = json.dumps(data_dict, sort_keys=True)
    hmac_digest = hmac.new(settings.HMAC_KEY.encode('utf-8'), json_data.encode('utf-8'), hashlib.sha256).hexdigest()
    return hmac_digest

def verify_hmac(data_dict, received_hmac):
    json_data = json.dumps(data_dict, sort_keys=True)
    expected_hmac = hmac.new(settings.HMAC_KEY.encode('utf-8'), json_data.encode('utf-8'), hashlib.sha256).hexdigest()
    return hmac.compare_digest(expected_hmac, received_hmac)


def aes_encrypt(plaintext, key=settings.ENCRYPTION_KEY):
    iv = os.urandom(16)
    cipher = Cipher(algorithms.AES(key.encode('utf-8')), modes.CFB(iv), backend=default_backend())
    encryptor = cipher.encryptor()
    ciphertext = encryptor.update(plaintext.encode('utf-8')) + encryptor.finalize()
    iv_and_ciphertext = iv + ciphertext
    b64_encoded = base64.urlsafe_b64encode(iv_and_ciphertext).decode('utf-8')
    return b64_encoded

def aes_decrypt(encoded_ciphertext, key=settings.ENCRYPTION_KEY):
    iv_and_ciphertext = base64.urlsafe_b64decode(encoded_ciphertext)
    iv = iv_and_ciphertext[:16]
    ciphertext = iv_and_ciphertext[16:]
    cipher = Cipher(algorithms.AES(key.encode('utf-8')), modes.CFB(iv), backend=default_backend())
    decryptor = cipher.decryptor()
    plaintext = decryptor.update(ciphertext) + decryptor.finalize()
    return plaintext.decode('utf-8')

def generate_code():
    return f"{secrets.randbelow(10000):04d}"
