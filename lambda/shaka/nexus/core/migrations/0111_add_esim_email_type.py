# Generated by Django 4.2.7 on 2024-09-16 10:55

from django.db import migrations

def add_email_type(apps, schema_editor):
    EmailType = apps.get_model('core', 'EmailType')
    EmailType.objects.create(name='esim-instructions')


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0110_sim_esim_available_to'),
    ]

    operations = [
        migrations.RunPython(add_email_type)
    ]
