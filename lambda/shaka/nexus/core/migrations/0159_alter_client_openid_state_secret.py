# Generated by Django 4.2.7 on 2025-02-05 19:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0158_client_openid_client_id_client_openid_client_secret_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='client',
            name='openid_state_secret',
            field=models.CharField(blank=True, default='', help_text='A secret used to generate the state parameter for OpenID Connect and auth the user afterwards', max_length=255),
        ),
    ]
