# Generated by Django 4.2.7 on 2024-09-27 14:42

from django.db import migrations

def add_email_type(apps, schema_editor):
    EmailType = apps.get_model('core', 'EmailType')
    EmailType.objects.create(name='voucher-details')

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0119_client_obfuscated_id'),
    ]

    operations = [
        migrations.RunPython(add_email_type)
    ]
