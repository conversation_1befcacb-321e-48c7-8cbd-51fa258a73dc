# Generated by Django 4.2.7 on 2024-12-17 15:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0146_client_enable_wallet_checkout'),
    ]

    operations = [
        migrations.AddField(
            model_name='boltonpurchase',
            name='quantity',
            field=models.IntegerField(default=1),
        ),
        migrations.AddField(
            model_name='plan',
            name='base_eu_roaming_days',
            field=models.IntegerField(default=7),
        ),
        migrations.AlterField(
            model_name='bolton',
            name='roaming',
            field=models.BooleanField(default=False, help_text='Is this a roaming bolt-on?'),
        ),
        migrations.AlterField(
            model_name='bolton',
            name='roaming_zone',
            field=models.CharField(blank=True, help_text='Lower case, e=EU', max_length=50, null=True),
        ),
        migrations.CreateModel(
            name='EuRoamingTracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField()),
                ('month', models.IntegerField()),
                ('roaming_days', models.IntegerField(default=0)),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='eu_roaming_tracking', to='core.subscription')),
            ],
        ),
    ]
