# Generated by Django 4.2.7 on 2024-04-04 15:20

from django.db import migrations


def convert_gbp_to_gbx(apps, schema):
    SubscriptionPayment = apps.get_model('core', 'SubscriptionPayment')
    for sp in SubscriptionPayment.objects.all():
        if sp.amount < 100:
            sp.currency = 'GBP'
            sp.save()
    


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0053_alter_subscriptionpayment_currency'),
    ]

    operations = [
    ]
