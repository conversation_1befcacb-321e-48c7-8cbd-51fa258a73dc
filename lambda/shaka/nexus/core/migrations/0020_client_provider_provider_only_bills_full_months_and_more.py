# Generated by Django 4.2.7 on 2024-02-21 18:47

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0019_rename_data_limit_plan_custom_data_limit_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='client',
            name='provider',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.provider'),
        ),
        migrations.AddField(
            model_name='provider',
            name='only_bills_full_months',
            field=models.BooleanField(default=True, help_text='Whether the provider only bills full months'),
        ),
        migrations.AddField(
            model_name='provider',
            name='prorates_on_activation',
            field=models.BooleanField(default=True, help_text='Whether the provider prorates on activation'),
        ),
    ]
