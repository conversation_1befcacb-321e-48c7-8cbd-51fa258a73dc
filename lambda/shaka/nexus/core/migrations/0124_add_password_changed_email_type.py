# Generated by Django 4.2.7 on 2024-10-03 15:57

from django.db import migrations

def add_email_type(apps, schema_editor):
    EmailType = apps.get_model('core', 'EmailType')
    EmailType.objects.create(name='password-changed')
    EmailType.objects.create(name='payment-failed')


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0123_merge_20241001_1137'),
    ]

    operations = [
        migrations.RunPython(add_email_type)
    ]
