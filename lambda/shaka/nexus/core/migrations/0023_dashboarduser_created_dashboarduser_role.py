# Generated by Django 4.2.7 on 2024-02-23 03:36

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0022_alter_client_provider'),
    ]

    operations = [
        migrations.AddField(
            model_name='dashboarduser',
            name='created',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='dashboarduser',
            name='role',
            field=models.CharField(choices=[('admin', 'Admin'), ('member', 'Member')], default='member', max_length=20),
        ),
    ]
