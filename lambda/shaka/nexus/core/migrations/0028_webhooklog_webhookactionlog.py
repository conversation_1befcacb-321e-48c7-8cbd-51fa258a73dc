# Generated by Django 4.2.7 on 2024-02-29 03:03

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0027_alter_client_spn'),
    ]

    operations = [
        migrations.CreateModel(
            name='WebhookLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('webhook_source', models.CharField(choices=[('transatel', 'Transatel'), ('stripe', 'Stripe'), ('fake_transatel', 'Fake Transatel'), ('fake_stripe', 'Fake Stripe')], max_length=20)),
                ('payload', models.JSONField()),
                ('supplemental_data', models.TextField(blank=True)),
            ],
            options={
                'verbose_name': 'Webhook Log',
                'verbose_name_plural': 'Webhook Logs',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='WebhookActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('log', models.TextField(default='')),
                ('status', models.CharField(choices=[('in_progress', 'In Progress'), ('errored_unknown', 'Errored - Unknown'), ('errored_known', 'Errored - Known'), ('completed', 'Completed')], max_length=20)),
                ('webhook_log', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.webhooklog')),
            ],
            options={
                'verbose_name': 'Webhook Action Log',
                'verbose_name_plural': 'Webhook Action Logs',
            },
        ),
    ]
