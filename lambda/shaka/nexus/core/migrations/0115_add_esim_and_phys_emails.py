# Generated by Django 4.2.7 on 2024-09-24 14:30

from django.db import migrations

def set_email_types(apps, schema_editor):
    EmailType = apps.get_model('core', 'EmailType')
    EmailType.objects.get(name='esim-instructions').delete()
    EmailType.objects.create(name='port-number')
    EmailType.objects.create(name='welcome-esim')
    EmailType.objects.create(name='welcome-physical')

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0114_add_gamma'),
    ]

    operations = [
        migrations.RunPython(set_email_types),
    ]
