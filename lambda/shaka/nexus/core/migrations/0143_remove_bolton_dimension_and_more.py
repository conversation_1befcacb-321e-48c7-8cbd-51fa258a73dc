# Generated by Django 4.2.7 on 2024-12-02 00:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0142_bolton_roaming_bolton_roaming_zone_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='bolton',
            name='dimension',
        ),
        migrations.RemoveField(
            model_name='bolton',
            name='dimension_value',
        ),
        migrations.AddField(
            model_name='bolton',
            name='data_gb',
            field=models.FloatField(blank=True, default=0),
        ),
        migrations.AddField(
            model_name='bolton',
            name='sms',
            field=models.IntegerField(blank=True, default=0),
        ),
        migrations.AddField(
            model_name='bolton',
            name='voice_minutes',
            field=models.FloatField(blank=True, default=0),
        ),
    ]
