# Generated by Django 4.2.7 on 2024-07-23 13:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0087_remove_discountperk_plan_discount_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='discountperk',
            name='redemptions_amount',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='discountperk',
            name='remaining_quantity',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='discountperk',
            name='total_cost',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='discountperk',
            name='client',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='perks', to='core.client'),
        ),
    ]
