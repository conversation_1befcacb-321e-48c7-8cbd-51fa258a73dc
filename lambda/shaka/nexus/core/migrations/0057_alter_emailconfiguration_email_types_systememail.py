# Generated by Django 4.2.7 on 2024-04-28 16:19

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0056_portability_done_email_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='emailconfiguration',
            name='email_types',
            field=models.ManyToManyField(help_text='Types of emails that can be sent using this configuration', to='core.emailtype'),
        ),
        migrations.CreateModel(
            name='SystemEmail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', models.DateTimeField(default=django.utils.timezone.now)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('recipient', models.EmailField(max_length=254)),
                ('recipient_key', models.Char<PERSON><PERSON>(max_length=255)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sending', 'Sending'), ('sent', 'Sent'), ('errored', 'Errored')], default='pending', max_length=7)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.client')),
                ('email_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.emailtype')),
            ],
            options={
                'unique_together': {('recipient_key', 'email_type')},
            },
        ),
    ]
