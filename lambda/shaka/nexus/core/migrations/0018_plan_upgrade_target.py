# Generated by Django 4.2.7 on 2024-02-11 20:11

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0017_remove_provider_price_per_data_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='plan',
            name='upgrade_target',
            field=models.ForeignKey(blank=True, help_text='Plan to upgrade to when a threshold is reached', null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.plan'),
        ),
    ]
