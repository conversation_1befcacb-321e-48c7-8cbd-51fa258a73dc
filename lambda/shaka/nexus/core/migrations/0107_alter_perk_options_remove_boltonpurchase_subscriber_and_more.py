# Generated by Django 4.2.7 on 2024-09-10 00:40

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0106_plan_perk_points_per_period'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='perk',
            options={'ordering': ['-id']},
        ),
        migrations.RemoveField(
            model_name='boltonpurchase',
            name='subscriber',
        ),
        migrations.AddField(
            model_name='bolton',
            name='dimension',
            field=models.CharField(blank=True, choices=[('voice', 'Voice'), ('data', 'Data')], max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='bolton',
            name='dimension_value',
            field=models.FloatField(blank=True, help_text='Value of the dimension (e.g. 1GB, 100 minutes)', null=True),
        ),
        migrations.AddField(
            model_name='boltonpurchase',
            name='subscription',
            field=models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, related_name='bolt_on_purchases', to='core.subscription'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='clientbolton',
            name='client',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bolt_ons', to='core.client'),
        ),
    ]
