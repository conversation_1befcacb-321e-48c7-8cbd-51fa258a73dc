# Generated by Django 4.2.7 on 2024-04-28 15:26

from django.db import migrations

def add_email_type(apps, schema_editor):
    EmailType = apps.get_model('core', 'EmailType')
    EmailType.objects.create(name='porting-complete')

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0055_emailtype_alter_subscription_status_and_more'),
    ]

    operations = [
        migrations.RunPython(add_email_type)
    ]
