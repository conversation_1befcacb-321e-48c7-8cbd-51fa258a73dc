# Generated by Django 4.2.7 on 2024-01-04 14:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0012_fonts'),
    ]

    operations = [
        migrations.CreateModel(
            name='DashboardUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(db_index=True, help_text='Cognito user pool username, cached', max_length=50, unique=True)),
                ('notes', models.TextField(blank=True, default='')),
                ('client', models.ForeignKey(help_text='Cached, so changes will require a restart to be visible', on_delete=django.db.models.deletion.CASCADE, to='core.client')),
            ],
        ),
    ]
