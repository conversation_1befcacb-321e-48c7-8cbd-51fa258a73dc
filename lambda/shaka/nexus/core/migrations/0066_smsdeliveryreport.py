# Generated by Django 4.2.7 on 2024-06-04 23:32

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0065_alter_smsconfiguration_client'),
    ]

    operations = [
        migrations.CreateModel(
            name='SMSDeliveryReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sender', models.CharField(max_length=255)),
                ('send_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('units', models.PositiveIntegerField()),
                ('total_sent', models.PositiveIntegerField()),
                ('total_delivered', models.PositiveIntegerField()),
                ('pending', models.PositiveIntegerField()),
                ('failed', models.PositiveIntegerField()),
                ('sms_message', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_report', to='core.smsmessage')),
            ],
            options={
                'ordering': ['-send_date'],
                'indexes': [models.Index(fields=['send_date'], name='core_smsdel_send_da_8a3b9c_idx')],
            },
        ),
    ]
