# Generated by Django 4.2.7 on 2024-01-30 19:37

import core.models
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0013_dashboarduser'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlanComponent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(blank=True, default='', max_length=255)),
                ('default_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('default_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('dimension', models.CharField(choices=[('sms', 'SMS'), ('voice', 'Voice'), ('data', 'Data')], db_index=True, max_length=5)),
                ('bundle_only', models.<PERSON>oleanField(default=False, help_text='Tick if this component is only available in bundles. Untick if you want it to be allowed separately')),
                ('allow_custom_limit', models.BooleanField(default=False, help_text='Allow a limit lower than max limit')),
                ('max_limit', models.IntegerField(default=0, help_text='0 for unlimited')),
                ('provider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.provider')),
            ],
            options={
                'ordering': ['-provider'],
            },
        ),
        migrations.AddField(
            model_name='plan',
            name='cost_override',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='plan',
            name='implementation_datetime',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='plan',
            name='is_bundled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='subscription',
            name='end_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='plan',
            name='data_limit',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='plan',
            name='sms_limit',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='plan',
            name='voice_limit',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='SubscriptionPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.BigIntegerField()),
                ('currency', models.CharField(choices=[('GBX', 'GBX')], default='GBX', max_length=3)),
                ('date', models.DateField()),
                ('subscription', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.subscription')),
            ],
        ),
        migrations.CreateModel(
            name='PlanComponentOffering',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cost_override', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('price_override', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('available_for_new_plans', models.BooleanField(default=True, help_text='Whether the client can use this component in new plans')),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.client')),
                ('plan_component', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.plancomponent')),
            ],
            options={
                'ordering': ['-client', '-plan_component'],
                'unique_together': {('client', 'plan_component')},
            },
        ),
        migrations.CreateModel(
            name='BundledPlanOffering',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cost_override', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('price_override', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('is_available', models.BooleanField(default=True, help_text='Whether this bundle is available to be selected')),
                ('description', models.CharField(blank=True, default='', max_length=255)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.client')),
                ('data', models.ForeignKey(limit_choices_to={'plan_component__dimension': 'data'}, on_delete=django.db.models.deletion.CASCADE, related_name='in_bundles_as_data', to='core.plancomponentoffering')),
                ('sms', models.ForeignKey(limit_choices_to={'plan_component__dimension': 'sms'}, on_delete=django.db.models.deletion.CASCADE, related_name='in_bundles_as_sms', to='core.plancomponentoffering')),
                ('voice', models.ForeignKey(limit_choices_to={'plan_component__dimension': 'voice'}, on_delete=django.db.models.deletion.CASCADE, related_name='in_bundles_as_voice', to='core.plancomponentoffering')),
            ],
            options={
                'ordering': ['-client'],
            },
        ),
        migrations.AddField(
            model_name='plan',
            name='data_component_offering',
            field=models.ForeignKey(blank=True, limit_choices_to={'plan_component__dimension': 'data'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='in_plan_as_data', to='core.plancomponentoffering'),
        ),
        migrations.AddField(
            model_name='plan',
            name='sms_component_offering',
            field=models.ForeignKey(blank=True, limit_choices_to={'plan_component__dimension': 'sms'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='in_plan_as_sms', to='core.plancomponentoffering'),
        ),
        migrations.AddField(
            model_name='plan',
            name='voice_component_offering',
            field=models.ForeignKey(blank=True, limit_choices_to={'plan_component__dimension': 'voice'}, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='in_plan_as_voice', to='core.plancomponentoffering'),
        ),
    ]
