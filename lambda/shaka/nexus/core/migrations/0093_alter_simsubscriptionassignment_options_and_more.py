# Generated by Django 4.2.7 on 2024-08-08 00:47

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0092_provider_is_demo_demousage'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='simsubscriptionassignment',
            options={},
        ),
        migrations.AddField(
            model_name='emailconfiguration',
            name='debug_bcc',
            field=models.EmailField(blank=True, help_text='Email to BCC on all emails for debugging', max_length=254, null=True),
        ),
        migrations.RemoveField(
            model_name='discountperk',
            name='redemptions_amount',
        ),
        migrations.RemoveField(
            model_name='discountperk',
            name='total_cost',
        ),
        migrations.AlterField(
            model_name='discountperk',
            name='remaining_quantity',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
    ]
