# Generated by Django 4.2.7 on 2023-12-14 16:53

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0003_plan_sim_simsubscriptionassignment_subscriber_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='client',
            options={'ordering': ['name']},
        ),
        migrations.AlterModelOptions(
            name='numberassignment',
            options={'ordering': ['-start_date']},
        ),
        migrations.AlterModelOptions(
            name='plan',
            options={'ordering': ['name']},
        ),
        migrations.AlterModelOptions(
            name='sim',
            options={'ordering': ['serial_number']},
        ),
        migrations.AlterModelOptions(
            name='simplanassignment',
            options={'ordering': ['-start_date']},
        ),
        migrations.AlterModelOptions(
            name='simsubscriptionassignment',
            options={'ordering': ['-start_date']},
        ),
        migrations.AlterModelOptions(
            name='subscriber',
            options={'ordering': ['-join_date']},
        ),
        migrations.AlterModelOptions(
            name='subscription',
            options={'ordering': ['-start_date']},
        ),
        migrations.AlterField(
            model_name='numberassignment',
            name='sim',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='number_assignments', to='core.sim'),
        ),
        migrations.AlterField(
            model_name='sim',
            name='plans',
            field=models.ManyToManyField(related_name='sims', through='core.SimPlanAssignment', to='core.plan'),
        ),
        migrations.AlterField(
            model_name='simplanassignment',
            name='plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sim_assignments', to='core.plan'),
        ),
        migrations.AlterField(
            model_name='simplanassignment',
            name='sim',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='plan_assignments', to='core.sim'),
        ),
        migrations.AlterField(
            model_name='simsubscriptionassignment',
            name='sim',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscription_assignments', to='core.sim'),
        ),
        migrations.AlterField(
            model_name='simsubscriptionassignment',
            name='subscription',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sim_assignments', to='core.subscription'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='sims',
            field=models.ManyToManyField(related_name='subscriptions', through='core.SimSubscriptionAssignment', to='core.sim'),
        ),
        migrations.AlterField(
            model_name='subscription',
            name='subscriber',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='core.subscriber'),
        ),
    ]
