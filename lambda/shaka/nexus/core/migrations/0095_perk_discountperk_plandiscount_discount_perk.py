# Generated by Django 4.2.7 on 2024-08-08 00:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0094_remove_plandiscount_discount_perk_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Perk',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True, null=True)),
                ('cost_base', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('perk_image', models.TextField(blank=True, null=True)),
                ('eligibility_type', models.CharField(choices=[('tenure', 'Tenure'), ('total_spend', 'Total Spend'), ('total_points_earned', 'Total Points Earned'), ('airdrop', 'Airdrop')], max_length=40)),
                ('eligibility_threshold', models.DecimalField(decimal_places=2, default=0, help_text='Points, months, spend in pounds, etc', max_digits=10)),
                ('allow_multiple_redemptions', models.BooleanField(default=False)),
                ('electively_redeemable', models.BooleanField(default=False)),
                ('elective_redemption_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('availability_date', models.DateTimeField(blank=True, help_text='Blank means immediately', null=True)),
                ('redemption_limit', models.IntegerField(default=0, help_text='0 means unlimited')),
                ('enabled', models.BooleanField(default=False)),
                ('client', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='perks', to='core.client')),
                ('target_group', models.ForeignKey(blank=True, help_text='Unset means target everyone', null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.subscribergroup')),
            ],
        ),
        migrations.CreateModel(
            name='DiscountPerk',
            fields=[
                ('perk_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.perk')),
            ],
            bases=('core.perk',),
        ),
        migrations.AddField(
            model_name='plandiscount',
            name='discount_perk',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='plan_discount', to='core.discountperk'),
        ),
    ]
