# clientapp/serializers.py
from rest_framework import serializers
from .utils import format_datetime_for_step_functions
from .models import Client, Subscriber, Subscription, Plan, Sim, SimPlanAssignment, SimSubscriptionAssignment, NumberAssignment, Provider, Font, ClientBranding, PlanComponentOffering, BundledPlanOffering, PlanComponent, DashboardUser, SMSMessage, MarketingEligibility, Campaign, PlanDiscount, PlanChange, DiscountPerk, VoucherPerk, BoltOnPerk, BoltOnOffering, ClientBoltOn


def validate_color_hex(value):
    if not value.startswith('#') or not all(c in '0123456789ABCDEFabcdef' for c in value[1:]):
        raise serializers.ValidationError("Invalid color hex format")


class ClientSerializer(serializers.HyperlinkedModelSerializer):
    subscriber_count = serializers.SerializerMethodField()
    active_subscriber_count = serializers.SerializerMethodField()

    def get_subscriber_count(self, obj):
        return obj.subscribers.count()

    def get_active_subscriber_count(self, obj):
        return obj.subscribers.filter(subscriptions__status=Subscription.Statuses.ACTIVE).count()

    class Meta:
        model = Client
        fields = ['id', 'name', 'user_pool_id', 'auth_app_client_id', 'auth_app_domain_prefix', 'auth_app_redirect_uri', 'authorisation_url', 'branding', 'subscriber_count', 'active_subscriber_count', 'perk_point_name_singular', 'perk_point_name_plural']


class FontSerializer(serializers.ModelSerializer):
    class Meta:
        model = Font
        fields = '__all__'

class ClientBrandingSerializer(serializers.ModelSerializer):
    headings_font = serializers.CharField(max_length=50)
    paragraph_font = serializers.CharField(max_length=50)
    primary_color = serializers.CharField(validators=[validate_color_hex])
    secondary_color = serializers.CharField(validators=[validate_color_hex])
    accent_color = serializers.CharField(validators=[validate_color_hex])
    text_color = serializers.CharField(validators=[validate_color_hex])
    link_color = serializers.CharField(validators=[validate_color_hex])

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['headings_font'] = instance.headings_font.name
        representation['paragraph_font'] = instance.paragraph_font.name
        return representation

    def validate(self, attrs):
        # Validate and link fonts manually
        headings_font_name = attrs.get('headings_font')
        paragraph_font_name = attrs.get('paragraph_font')

        # Retrieve Font instances based on font names
        try:
            attrs['headings_font'] = Font.objects.get(name=headings_font_name)
        except Font.DoesNotExist:
            raise serializers.ValidationError(f"Font '{headings_font_name}' does not exist.")  # pylint: disable=raise-missing-from

        try:
            attrs['paragraph_font'] = Font.objects.get(name=paragraph_font_name)
        except Font.DoesNotExist:
            raise serializers.ValidationError(f"Font '{paragraph_font_name}' does not exist.")  # pylint: disable=raise-missing-from

        return attrs

    class Meta:
        model = ClientBranding
        fields = '__all__'

class ProviderSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Provider
        fields = '__all__'

class PlanComponentSerializer(serializers.ModelSerializer):
    class Meta:
        model = PlanComponent
        fields = ['id', 'description', 'dimension', 'bundle_only', 'allow_custom_limit', 'max_limit', 'provider']

class PlanComponentOfferingSerializer(serializers.ModelSerializer):
    plan_component = PlanComponentSerializer()

    class Meta:
        model = PlanComponentOffering
        fields = ['id', 'plan_component', 'available_for_new_plans', 'price']

class BundledPlanOfferingSerializer(serializers.ModelSerializer):
    voice = PlanComponentOfferingSerializer()
    data = PlanComponentOfferingSerializer()
    sms = PlanComponentOfferingSerializer()

    class Meta:
        model = BundledPlanOffering
        fields = ['price', 'is_available', 'description', 'voice', 'sms', 'data', 'id']

# pylint: disable=abstract-method
class PlanOfferingSetSerializer(serializers.Serializer):
    bundled_plan_offerings = BundledPlanOfferingSerializer(many=True)
    plan_component_offerings = PlanComponentOfferingSerializer(many=True)

class PlanSerializer(serializers.ModelSerializer):
    bundle_id = serializers.IntegerField()

    def validate_sms_component_offering(self, value):
        request = self.context.get('request')
        if request.client_id != value.client_id:
            raise serializers.ValidationError("Invalid client for offering.")
        return value

    def validate_data_component_offering(self, value):
        request = self.context.get('request')
        if request.client_id != value.client_id:
            raise serializers.ValidationError("Invalid client for offering.")
        return value

    def validate_voice_component_offering(self, value):
        request = self.context.get('request')
        if request.client_id != value.client_id:
            raise serializers.ValidationError("Invalid client for offering.")
        return value

    class Meta:
        model = Plan
        fields = ['voice_component_offering', 'sms_component_offering', 'data_component_offering', 'id', 'name', 'price', 'status', 'created', 'implementation_datetime', 'plan_key', 'version_number', 'custom_voice_limit', 'custom_sms_limit', 'custom_data_limit', 'is_bundled', 'provider', 'bundle_id', 'points_per_month']
        read_only_fields = ['bundle_id']


class CustomPlanSerializer(PlanSerializer):
    def validate_sms_component_offering(self, value):
        request = self.context.get('request')
        if request.client_id != value.client_id:
            raise serializers.ValidationError("Invalid client for offering.")
        return value

    def validate_data_component_offering(self, value):
        request = self.context.get('request')
        if request.client_id != value.client_id:
            raise serializers.ValidationError("Invalid client for offering.")
        return value

    def validate_voice_component_offering(self, value):
        request = self.context.get('request')
        if request.client_id != value.client_id:
            raise serializers.ValidationError("Invalid client for offering.")
        return value

    class Meta:
        model = Plan
        fields = ['voice_component_offering', 'sms_component_offering', 'data_component_offering', 'id', 'name', 'price', 'status', 'created', 'implementation_datetime', 'plan_key', 'version_number', 'custom_voice_limit', 'custom_sms_limit', 'custom_data_limit', 'provider', 'points_per_month']


class BundledPlanSerializer(PlanSerializer):
    bundle_id = serializers.IntegerField()

    def validate_bundle_id(self, value):
        bundle = BundledPlanOffering.objects.get(pk=value)
        request = self.context.get('request')
        if request.client_id != bundle.client_id:
            raise serializers.ValidationError("Invalid client for offering.")
        return value

    class Meta:
        model = Plan
        fields = ['id', 'name', 'price', 'status', 'created', 'implementation_datetime', 'plan_key', 'version_number', 'custom_voice_limit', 'custom_sms_limit', 'custom_data_limit', 'provider', 'bundle_id', 'points_per_month']


class SimSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Sim
        fields = ['serial_number', 'status', 'plans', 'actvation_data', 'is_data_barred', 'type']

class SubscriberSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Subscriber
        fields = '__all__'

class SubscriptionSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = Subscription
        fields = '__all__'

class SimPlanAssignmentSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = SimPlanAssignment
        fields = '__all__'

class SimSubscriptionAssignmentSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = SimSubscriptionAssignment
        fields = '__all__'

class NumberAssignmentSerializer(serializers.HyperlinkedModelSerializer):
    class Meta:
        model = NumberAssignment
        fields = '__all__'


# pylint: disable=abstract-method
class FrontendSubscriberSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(source='subscriber.name')
    email = serializers.CharField(source='subscriber.email')
    date_of_birth = serializers.DateField(source='subscriber.date_of_birth')
    start_date = serializers.DateTimeField()
    latest_number = serializers.SerializerMethodField()
    latest_number_display = serializers.SerializerMethodField()
    latest_plan_name = serializers.SerializerMethodField()
    status = serializers.CharField(source='status_display_for_frontend')
    revenue_generated = serializers.DecimalField(max_digits=10, decimal_places=2)  # todo: apply to subscriber  # pylint: disable=fixme
    group = serializers.CharField(source='subscriber.group')

    def get_latest_number(self, obj):
        latest_number = obj.latest_msisdn
        if latest_number:
            return latest_number
        else:
            return ''

    def get_latest_number_display(self, obj):
        latest_number = obj.latest_msisdn
        if latest_number:
            return latest_number
        else:
            # If sim is 'being assigned' then we should indicate that
            sim = obj.latest_sim
            if sim:
                return 'Assign in progress'
            else:
                return ''

    def get_latest_plan_name(self, obj):
        if getattr(obj, '_latest_plan_name', None):
            return obj._latest_plan_name  # pylint: disable=protected-access
        latest_plan = obj.latest_plan
        return str(latest_plan)


class DashboardUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = DashboardUser
        fields = ['email', 'role', 'id']


class ClientSPNSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = ['id', 'spn']
        read_only_fields = ['id']


class FrontendSubscriberDetailSerializer(serializers.Serializer):
    id = serializers.IntegerField()
    name = serializers.CharField(source='subscriber.name')
    email = serializers.CharField(source='subscriber.email')
    address = serializers.CharField(source='subscriber.address')
    date_of_birth = serializers.DateField(source='subscriber.date_of_birth')
    sim_status = serializers.SerializerMethodField()
    num_subscriptions = serializers.IntegerField(default=1)
    total_profit = serializers.SerializerMethodField()
    data_usage = serializers.SerializerMethodField()
    voice_usage = serializers.SerializerMethodField()
    sms_usage = serializers.SerializerMethodField()

    start_date = serializers.DateTimeField()
    latest_number = serializers.SerializerMethodField()
    latest_number_display = serializers.SerializerMethodField()
    latest_plan = serializers.SerializerMethodField()
    latest_sim_type = serializers.SerializerMethodField()

    status = serializers.CharField(source='status_display_for_frontend')
    revenue_generated = serializers.DecimalField(source='subscriber.revenue_generated', max_digits=10, decimal_places=2)
    group = serializers.CharField(source='subscriber.group')
    perk_points = serializers.IntegerField(source='subscriber.perk_points')
    total_points_earned = serializers.IntegerField(source='subscriber.total_points_earned')
    perk_redemption_amount = serializers.IntegerField(source='subscriber.num_perk_redemptions')
    send_marketing = serializers.BooleanField(source='subscriber.send_marketing')

    def get_data_usage(self, obj):
        sim = obj.latest_sim
        msisdn = obj.latest_msisdn
        if sim and msisdn:
            return sim.get_data_usage_this_billing_month(msisdn) or 0
        return 0

    def get_voice_usage(self, obj):
        sim = obj.latest_sim
        msisdn = obj.latest_msisdn
        if sim and msisdn:
            return sim.get_voice_usage_this_billing_month(msisdn) or 0
        return 0

    def get_sms_usage(self, obj):
        sim = obj.latest_sim
        msisdn = obj.latest_msisdn
        if sim and msisdn:
            return sim.get_sms_usage_this_billing_month(msisdn) or 0
        return 0

    def get_total_profit(self, obj):
        sim = obj.latest_sim
        if sim:
            return sim.get_lifetime_profit()
        return 0

    def get_sim_status(self, obj):
        sim = obj.latest_sim
        if sim:
            return sim.status
        return ''

    def get_latest_number(self, obj):
        latest_number = obj.latest_number
        if latest_number:
            return latest_number.phone_number
        else:
            return ''

    def get_latest_number_display(self, obj):
        latest_number = obj.latest_msisdn
        if latest_number:
            return latest_number
        else:
            # If sim is 'being assigned' then we should indicate that
            sim = obj.latest_sim
            if sim:
                return 'Assign in progress'
            else:
                return ''

    def get_latest_sim_type(self, obj):
        sim = obj.latest_sim
        if sim:
            if sim.esim_data:
                return 'esim'
            else:
                return 'physical'
        else:
            return obj.subscriber.intended_sim_type

    def get_latest_plan(self, obj):
        latest_plan = obj.latest_plan
        return PlanSerializer(latest_plan or obj.intended_plan).data


class MarketingEligibilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = MarketingEligibility
        fields = ['eligibility_type', 'active_since', 'active_duration_months']


class SMSDeliveryReportSerializer(serializers.Serializer):
    delivery_rate = serializers.ReadOnlyField()
    sender = serializers.ReadOnlyField()
    sms_message_id = serializers.ReadOnlyField()
    send_date = serializers.ReadOnlyField()
    total_units = serializers.ReadOnlyField()
    total_sent = serializers.ReadOnlyField()
    total_delivered = serializers.ReadOnlyField()
    total_pending = serializers.ReadOnlyField()
    total_failed = serializers.ReadOnlyField()


class SMSMessageListSerializer(serializers.ModelSerializer):
    delivery_report = SMSDeliveryReportSerializer(read_only=True)
    marketing_eligibility = MarketingEligibilitySerializer(required=False)
    campaign = serializers.PrimaryKeyRelatedField(queryset=Campaign.objects.all(), required=False)

    def validate_campaign(self, value):
        campaign_client = value.client
        expected_client = self.instance.client if self.instance else self.context['request'].client
        if campaign_client != expected_client:
            raise serializers.ValidationError("Selected campaign does not belong to the same client as the existing campaign.")
        return value

    class Meta:
        model = SMSMessage
        read_only_fields = ['id', 'created', 'status', 'client', 'initiator', 'marketing_eligibility']
        fields = ['id', 'title', 'message', 'status', 'client', 'initiator', 'created', 'send_on', 'delivery_report', 'marketing_eligibility', 'campaign']


class PlanDiscountSerializer(serializers.ModelSerializer):
    class Meta:
        model = PlanDiscount
        read_only_fields = ['id', 'campaign']
        fields = ['id', 'campaign', 'plan', 'discount_type', 'discount_percentage', 'discount_amount', 'discount_duration_months']


class CampaignSerializer(serializers.ModelSerializer):
    marketing_eligibility = MarketingEligibilitySerializer()
    plan_discounts = PlanDiscountSerializer(many=True, required=False)

    def create(self, validated_data):
        plan_discounts_data = validated_data.pop('plan_discounts', [])
        marketing_eligibility_data = validated_data.pop('marketing_eligibility')
        marketing_eligibility = MarketingEligibility.objects.create(**marketing_eligibility_data)
        campaign = Campaign.objects.create(marketing_eligibility=marketing_eligibility, **validated_data)
        for plan_discount_data in plan_discounts_data:
            obj = PlanDiscount.objects.create(campaign=campaign, **plan_discount_data)
            obj.sync_to_gateway_if_necessary()
        return campaign

    def update(self, instance, validated_data):
        plan_discounts_data = validated_data.pop('plan_discounts', [])
        marketing_eligibility_data = validated_data.pop('marketing_eligibility')
        marketing_eligibility = instance.marketing_eligibility
        for key, value in marketing_eligibility_data.items():
            setattr(marketing_eligibility, key, value)
        marketing_eligibility.save()

        for key, value in validated_data.items():
            setattr(instance, key, value)
        instance.save()

        for pd in instance.plan_discounts.all():
            pd.mark_as_deleted()
        for plan_discount_data in plan_discounts_data:
            obj = PlanDiscount.objects.create(campaign=instance, **plan_discount_data)
            obj.sync_to_gateway_if_necessary()
        return instance


    def validate(self, data):  # pylint: disable=arguments-renamed
        allowed_transitions = {
            Campaign.Status.DRAFT: [Campaign.Status.READY, Campaign.Status.CANCELLED, Campaign.Status.DRAFT],
            Campaign.Status.READY: [Campaign.Status.DRAFT, Campaign.Status.CANCELLED, Campaign.Status.READY],
            Campaign.Status.ACTIVE: [Campaign.Status.CANCELLED, Campaign.Status.ACTIVE],
            Campaign.Status.FINISHED: [Campaign.Status.FINISHED],
            Campaign.Status.CANCELLED: [Campaign.Status.DRAFT, Campaign.Status.READY, Campaign.Status.CANCELLED]
        }
        if self.instance is None:
            if data['status'] not in [Campaign.Status.DRAFT, Campaign.Status.READY]:
                raise serializers.ValidationError(f"New campaigns can only be created with status '{Campaign.Status.DRAFT}' or '{Campaign.Status.READY}'.")
        else:
            current_status = self.instance.status
            new_status = data.get('status', current_status)
            if new_status not in allowed_transitions[current_status]:
                raise serializers.ValidationError(f"Cannot transition from {current_status} to {new_status}.")

        # validate plan discount plans are for this client
        if self.instance:
            expected_client = self.instance.client
        else:
            expected_client = self.context['request'].client
        if 'plan_discounts' in data:
            for plan_discount_data in data['plan_discounts']:
                plan = plan_discount_data['plan']
                if plan.client != expected_client:
                    raise serializers.ValidationError(f"Plan '{plan}' does not belong to client '{expected_client}'.")
        return data

    class Meta:
        model = Campaign
        read_only_fields = ['id', 'client', 'discount_type_summary', 'discount_value_summary']
        fields = ['id', 'title', 'client', 'marketing_eligibility', 'campaign_type', 'start_date', 'end_date', 'status', 'plan_discounts', 'discount_type_summary', 'discount_value_summary']


class PlanChangeSerializer(serializers.ModelSerializer):
    future_execution_timestamp = serializers.SerializerMethodField()
    completion_expected_timestamp = serializers.SerializerMethodField()

    def get_future_execution_timestamp(self, obj):
        if obj.future_execution_timestamp:
            return format_datetime_for_step_functions(obj.future_execution_timestamp)
        return None

    def get_completion_expected_timestamp(self, obj):
        if obj.completion_expected_timestamp:
            return format_datetime_for_step_functions(obj.completion_expected_timestamp)
        return None

    def get_fields(self):
        fields = super().get_fields()
        fields['target_plan_change'] = PlanChangeSerializer()
        return fields

    class Meta:
        model = PlanChange
        fields = ['id', 'subscription', 'change_type', 'target_plan', 'execution_start_time', 'status', 'execution_id', 'sub_execution_id', 'task_token', 'target_plan_change', 'future_execution_timestamp', 'completion_expected_timestamp']
        read_only_fields = ['id', 'execution_id', 'sub_execution_id', 'task_token']


class DiscountPerkDetailsSerializer(serializers.ModelSerializer):
    plan_discount = PlanDiscountSerializer()

    class Meta:
        model = DiscountPerk
        fields = ['plan_discount']


class DiscountPerkSerializer(serializers.ModelSerializer):
    discount_details = DiscountPerkDetailsSerializer()

    class Meta:
        model = DiscountPerk
        fields = [
            'id', 'name', 'description', 'cost_base', 'perk_image', 'eligibility_type', 'eligibility_threshold',
            'target_group', 'allow_multiple_redemptions', 'electively_redeemable', 'elective_redemption_cost',
            'availability_date', 'redemption_limit', 'enabled', 'discount_details',
            'redemptions_amount', 'remaining_quantity', 'total_cost', 'featured'
        ]
        read_only_fields = ['client_id', 'redemptions_amount', 'remaining_quantity', 'total_cost']

    def create(self, validated_data):
        discount_details_data = validated_data.pop('discount_details')
        plan_discount_data = discount_details_data.pop('plan_discount')
        instance = super().create(validated_data)
        plan_discount_serializer = PlanDiscountSerializer(data=plan_discount_data)
        plan_discount_serializer.is_valid(raise_exception=True)
        obj = plan_discount_serializer.save(discount_perk=instance)
        obj.sync_to_gateway_if_necessary()
        return instance

    def update(self, instance, validated_data):
        discount_details_data = validated_data.pop('discount_details')
        plan_discount_data = discount_details_data.pop('plan_discount')
        instance = super().update(instance, {**validated_data})
        instance.plan_discount.mark_as_deleted()
        plan_discount_serializer = PlanDiscountSerializer(data=plan_discount_data)
        plan_discount_serializer.is_valid(raise_exception=True)
        obj = plan_discount_serializer.save(discount_perk=instance)
        obj.sync_to_gateway_if_necessary()
        return instance


class VoucherPerkDetailsSerializer(serializers.ModelSerializer):
    def to_internal_value(self, data):
        if data.get("expiry_date") == "":
            data["expiry_date"] = None
        return super().to_internal_value(data)

    class Meta:
        model = VoucherPerk
        fields = ['code', 'url', 'instructions', 'expiry_date', 'merchant_name']


class VoucherPerkSerializer(serializers.ModelSerializer):
    voucher_details = VoucherPerkDetailsSerializer()

    class Meta:
        model = VoucherPerk
        fields = [
            'id', 'name', 'description', 'cost_base', 'perk_image', 'eligibility_type', 'eligibility_threshold',
            'target_group', 'allow_multiple_redemptions', 'electively_redeemable', 'elective_redemption_cost',
            'availability_date', 'redemption_limit', 'enabled', 'voucher_details',
            'redemptions_amount', 'remaining_quantity', 'total_cost', 'featured'
        ]
        read_only_fields = ['client_id', 'redemptions_amount', 'remaining_quantity', 'total_cost']

    def create(self, validated_data):
        voucher_details_data = validated_data.pop('voucher_details')
        instance = super().create({**validated_data, **voucher_details_data})
        return instance

    def update(self, instance, validated_data):
        voucher_details_data = validated_data.pop('voucher_details')
        instance = super().update(instance, {**validated_data, **voucher_details_data})
        return instance

class BoltOnPerkDetailsSerializer(serializers.ModelSerializer):
    class Meta:
        model = BoltOnPerk
        fields = ['bolt_on']

class BoltOnPerkSerializer(serializers.ModelSerializer):
    bolt_on_details = BoltOnPerkDetailsSerializer()

    class Meta:
        model = BoltOnPerk
        fields = [
            'id', 'name', 'description', 'cost_base', 'perk_image', 'eligibility_type', 'eligibility_threshold',
            'target_group', 'allow_multiple_redemptions', 'electively_redeemable', 'elective_redemption_cost',
            'availability_date', 'redemption_limit', 'enabled', 'bolt_on_details',
            'redemptions_amount', 'remaining_quantity', 'total_cost', 'featured'
        ]
        read_only_fields = ['client_id', 'redemptions_amount', 'remaining_quantity', 'total_cost']

    def create(self, validated_data):
        bolt_on_details_data = validated_data.pop('bolt_on_details')
        instance = super().create({**validated_data, **bolt_on_details_data})
        return instance

    def update(self, instance, validated_data):
        bolt_on_details_data = validated_data.pop('bolt_on_details')
        instance = super().update(instance, {**validated_data, **bolt_on_details_data})
        return instance

class PerkRedemptionAggregateSerializer(serializers.Serializer):
    period = serializers.CharField()
    perk_id = serializers.IntegerField()
    date = serializers.DateTimeField()
    redemptions = serializers.IntegerField()
    cost = serializers.DecimalField(max_digits=10, decimal_places=2)
    quantity = serializers.IntegerField()
    is_unlimited = serializers.BooleanField()


class PerkRedeemerSerializer(serializers.Serializer):
    full_name = serializers.CharField()
    image = serializers.URLField()
    id = serializers.IntegerField()

class SubscriberRedemptionSerializer(serializers.Serializer):
    user = PerkRedeemerSerializer()
    redemption_date = serializers.DateTimeField()
    quantity = serializers.IntegerField()
    is_unlimited = serializers.BooleanField()


class BoltOnOfferingSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='bolt_on.name', read_only=True)
    description = serializers.CharField(source='bolt_on.description', read_only=True)
    bolt_on_type = serializers.CharField(source='bolt_on.bolt_on_type', read_only=True)
    duration_type = serializers.CharField(source='bolt_on.duration_type', read_only=True)
    billing_calendar_sync = serializers.BooleanField(source='bolt_on.billing_calendar_sync', read_only=True)
    price = serializers.DecimalField(source='price_override', max_digits=10, decimal_places=2, read_only=True)

    class Meta:
        model = BoltOnOffering
        fields = ['name', 'description', 'bolt_on_type', 'duration_type', 'billing_calendar_sync', 'price', 'available_for_new_selection', 'id']


class ClientBoltOnSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='name_override')
    cost = serializers.DecimalField(max_digits=10, decimal_places=2, source='price')
    availability_date = serializers.DateTimeField(source='implementation_datetime')
    min_cost = serializers.DecimalField(max_digits=10, decimal_places=2, source='min_price', read_only = True)
    initial_name = serializers.CharField(source='offering.name', read_only=True)

    def validate_offering(self, value):
        request = self.context.get('request')
        if request.client_id != value.client_id:
            raise serializers.ValidationError("Invalid client for offering.")
        return value

    class Meta:
        model = ClientBoltOn
        fields = [
            'id', 'name', 'cost', 'status', 'availability_date', 'min_cost', 'initial_name', 'offering'
        ]
        read_only_fields = ['id', 'initial_name', 'min_price']

class ClientBoltOnOverviewSerializer(serializers.Serializer):
    purchases = serializers.IntegerField()
    revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
    perk_redemptions = serializers.IntegerField()
    uptake = serializers.DecimalField(max_digits=10, decimal_places=2)
    bolt_ons = ClientBoltOnSerializer(many=True)
