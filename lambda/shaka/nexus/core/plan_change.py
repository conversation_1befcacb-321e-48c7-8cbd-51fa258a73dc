import json
from contextlib import contextmanager
import boto3
from django.db import transaction
from django.conf import settings
from django.utils import timezone
from core.models import PlanChange, Subscription, PaymentIntegration, SubscriptionPayment
from core.serializers import PlanChangeSerializer
from core.utils import get_stripe_client
from core.time_control import STRIPE_BILLING_TIME_CONTROL
from core.slack import send_debug_slack_message, send_slack_message

class PlanChangeError(Exception):
    pass

class PlanChangeValidationError(PlanChangeError):
    pass


state_machine_arns = {
    'UPGRADE_PLAN': settings.PLAN_UPGRADE_STATE_MACHINE_ARN,
    'DOWNGRADE_PLAN': settings.PLAN_DOWNGRADE_STATE_MACHINE_ARN,
    'CANCEL_PLAN_CHANGE': settings.CANCEL_PLAN_CHANGE_STATE_MACHINE_ARN,
    'CANCEL_SUBSCRIPTION': settings.CANCEL_SUBSCRIPTION_STATE_MACHINE_ARN,
}

def upgrade_to_plan(subscription_id, plan_id):
    send_slack_message(f'Upgrade to plan {plan_id} for subscription {subscription_id}')
    plan_change = PlanChange.objects.create(subscription_id=subscription_id, target_plan_id=plan_id, change_type=PlanChange.ChangeType.UPGRADE)
    client = boto3.client('stepfunctions', region_name='eu-west-2')
    arn = state_machine_arns['UPGRADE_PLAN']
    if arn:
        response = client.start_execution(
            stateMachineArn=arn,
            input=json.dumps({
                'id': plan_change.id
            })
        )
        plan_change.execution_id = response['executionArn']
    else:
        plan_change.execution_id = 'not-set'
    plan_change.execution_start_time = timezone.now()
    plan_change.save()


def downgrade_to_plan(subscription_id, plan_id):
    send_debug_slack_message(f'Downgrade to plan {plan_id} for subscription {subscription_id}')
    plan_change = PlanChange.objects.create(subscription_id=subscription_id, target_plan_id=plan_id, change_type=PlanChange.ChangeType.DOWNGRADE)
    client = boto3.client('stepfunctions', region_name='eu-west-2')
    arn = state_machine_arns['DOWNGRADE_PLAN']
    if arn:
        response = client.start_execution(
            stateMachineArn=arn,
            input=json.dumps({
                'id': plan_change.id
            })
        )
        plan_change.execution_id = response['executionArn']
    else:
        plan_change.execution_id = 'not-set'
    plan_change.execution_start_time = timezone.now()
    plan_change.save()


def cancel_plan_change(plan_change_id):
    send_debug_slack_message(f'Cancel plan change {plan_change_id}')
    existing_plan_change = PlanChange.objects.get(id=plan_change_id)
    if existing_plan_change.status != PlanChange.Status.IN_PROGRESS:
        raise PlanChangeValidationError('Plan change is not in progress')
    plan_change = PlanChange.objects.create(subscription=existing_plan_change.subscription, target_plan_change=existing_plan_change, change_type=PlanChange.ChangeType.CANCEL_CHANGE)
    client = boto3.client('stepfunctions', region_name='eu-west-2')
    arn = state_machine_arns['CANCEL_PLAN_CHANGE']
    if arn:
        response = client.start_execution(
            stateMachineArn=arn,
            input=json.dumps({
                'id': plan_change.id
            })
        )
        plan_change.execution_id = response['executionArn']
    else:
        plan_change.execution_id = 'not-set'
    plan_change.execution_start_time = timezone.now()
    plan_change.save()


def cancel_subscription(subscription_id):
    plan_change = PlanChange.objects.create(subscription_id=subscription_id, change_type=PlanChange.ChangeType.CANCELLATION)
    client = boto3.client('stepfunctions', region_name='eu-west-2')
    arn = state_machine_arns['CANCEL_SUBSCRIPTION']
    if arn:
        response = client.start_execution(
            stateMachineArn=state_machine_arns['CANCEL_SUBSCRIPTION'],
            input=json.dumps({
                'id': plan_change.id
            })
        )
        plan_change.execution_id = response['executionArn']
    else:
        plan_change.execution_id = 'not-set'
    plan_change.execution_start_time = timezone.now()
    plan_change.save()


class PlanChangeManager:
    def __init__(self, plan_change, sub_execution_id=None):
        self.plan_change = plan_change
        self.sub_execution_id = sub_execution_id

    @staticmethod
    def for_sub_execution(plan_change_id, sub_execution_id):
        plan_change = PlanChange.objects.get(id=plan_change_id)
        return {
            PlanChange.ChangeType.UPGRADE: UpgradePlanChangeManager,
            PlanChange.ChangeType.DOWNGRADE: DowngradePlanChangeManager,
            PlanChange.ChangeType.CANCEL_CHANGE: CancelChangePlanChangeManager,
            PlanChange.ChangeType.CANCELLATION: CancelSubscriptionChangeManager,
        }[plan_change.change_type](plan_change, sub_execution_id=sub_execution_id)

    def validate_plan_change(self):
        if not self.plan_change:
            raise PlanChangeValidationError('Plan change not found')

    def lock_plan_change_for_actuation(self):
        with self.subscription_lock():
            self.plan_change.status = PlanChange.Status.LOCKED
            self.plan_change.sub_execution_id = self.sub_execution_id
            self.plan_change.save()

    @contextmanager
    def subscription_lock(self):
        with transaction.atomic():
            subs_obj = Subscription.objects.select_for_update().get(id=self.subscription.id)
            self.plan_change.refresh_from_db()
            if self.subscription.id != subs_obj.id:
                raise PlanChangeValidationError('Subscription has changed?')
            self.validate_plan_change()
            yield

    def serialise_plan_change(self):
        return PlanChangeSerializer(self.plan_change).data

    @property
    def subscription(self):
        return self.plan_change.subscription

    @property
    def target_plan(self):
        return self.plan_change.target_plan

    @property
    def target_plan_change(self):
        return self.plan_change.target_plan_change

    @property
    def _billing_secret_key(self):
        integration = self.subscription.subscriber.client.payment_integration
        if not integration:
            raise PlanChangeValidationError('No payment integration found')
        if integration.gateway != PaymentIntegration.Gateway.STRIPE:
            raise PlanChangeValidationError('Unsupported payment gateway')
        return integration.secret_credentials

    def adjust_billing_for_plan_change(self):
        raise NotImplementedError

    def is_billing_change_complete(self):
        raise NotImplementedError

    def make_appropriate_changes_in_provider(self):
        raise NotImplementedError

    def is_provider_change_complete(self):
        raise NotImplementedError

    def complete_plan_change(self):
        self.plan_change.status = PlanChange.Status.COMPLETE
        self.plan_change.save()


class UpgradePlanChangeManager(PlanChangeManager):
    def validate_plan_change(self):
        super().validate_plan_change()
        if not self.target_plan:
            raise PlanChangeValidationError('Target plan is not set')
        if not self.subscription.latest_plan.is_upgrade_if_going_to(self.target_plan):
            raise PlanChangeValidationError('Target plan is not an upgrade')

    def adjust_billing_for_plan_change(self):
        if not self.subscription.client.plans_assigned_externally:
            stripe_client = get_stripe_client(self._billing_secret_key)
            stripe_subscription = stripe_client.subscriptions.retrieve(self.subscription.billing_subscription_id)
            item_id = stripe_subscription['items']['data'][0]['id']
            resp = stripe_client.subscriptions.update(self.subscription.billing_subscription_id, {
                'proration_behavior': 'always_invoice',
                'items': [
                    {
                        'id': item_id,
                        'price': self.target_plan.billing_plan_id
                    }
                ]
            })
            self.plan_change.billing_reference = resp['latest_invoice']
            self.plan_change.save()

    def is_billing_change_complete(self):
        if self.subscription.client.plans_assigned_externally:
            return True
        else:
            return SubscriptionPayment.objects.filter(subscription=self.subscription, billing_invoice_id=self.plan_change.billing_reference).exists()

    def make_appropriate_changes_in_provider(self):
        self.subscription.latest_sim.trigger_change_plan(self.target_plan.provider_plan_code, fake=False)

    def is_provider_change_complete(self):
        return self.subscription.latest_plan == self.target_plan

    def lock_plan_change_for_actuation(self):
        if self.plan_change.status != PlanChange.Status.IN_PROGRESS:
            raise PlanChangeValidationError(f'Plan change is not in progress: {self.target_plan.status}')
        if self.plan_change.sub_execution_id:
            raise PlanChangeValidationError('Plan change already has a sub execution id')
        super().lock_plan_change_for_actuation()


class DowngradePlanChangeManager(PlanChangeManager):
    def validate_plan_change(self):
        super().validate_plan_change()
        if not self.target_plan:
            raise PlanChangeValidationError('Target plan is not set')
        if self.subscription.latest_plan.is_upgrade_if_going_to(self.target_plan):
            raise PlanChangeValidationError('Target plan is not a downgrade')
        if self.plan_change.sub_execution_id and self.sub_execution_id != self.plan_change.sub_execution_id:
            raise PlanChangeValidationError('Sub execution id does not match')

    def adjust_billing_for_plan_change(self):
        if self.plan_change.status == PlanChange.Status.CANCELLED:
            raise PlanChangeValidationError('Plan change is cancelled')
        if not self.subscription.client.plans_assigned_externally:
            stripe_client = get_stripe_client(self._billing_secret_key)
            stripe_subscription = stripe_client.subscriptions.retrieve(self.subscription.billing_subscription_id)
            # Create a schedule if necessary
            if stripe_subscription['schedule']:
                schedule = stripe_client.subscription_schedules.retrieve(stripe_subscription['schedule'])
            else:
                schedule = stripe_client.subscription_schedules.create(
                    params={
                        'from_subscription': stripe_subscription['id']
                    }
                )
            self.plan_change.billing_reference = schedule['id']
            self.plan_change.save()
            # Modify schedule to downgrade
            now = int(timezone.now().timestamp())
            for phase in schedule['phases']:
                if phase['start_date'] < now and (phase['end_date'] is None or phase['end_date'] > now):
                    current_phase = phase
            time_control = STRIPE_BILLING_TIME_CONTROL
            current_phase['end_date'] = time_control.start_of_next_cycle_epoch_timestamp
            next_phase = {
                'start_date': time_control.start_of_next_cycle_epoch_timestamp,
                'iterations': 1,
                'items': [
                    {
                        'price': self.target_plan.billing_plan_id,
                        'quantity': 1,
                    }
                ]
            }
            stripe_client.subscription_schedules.update(
                schedule['id'],
                params={
                    'proration_behavior': 'none',
                    'phases': [
                        current_phase,
                        next_phase
                    ]
                }
            )

    def lock_plan_change_for_actuation(self):
        if self.plan_change.status != PlanChange.Status.CANCELLED:
            if self.plan_change.status != PlanChange.Status.IN_PROGRESS:
                raise PlanChangeValidationError(f'Plan change is not in progress: {self.target_plan.status}')
            if self.plan_change.sub_execution_id and self.sub_execution_id != self.plan_change.sub_execution_id:
                raise PlanChangeValidationError('Sub execution id does not match')
            super().lock_plan_change_for_actuation()

    def is_billing_change_complete(self):
        if self.plan_change.status == PlanChange.Status.CANCELLED:
            raise PlanChangeValidationError('Plan change is cancelled')
        if self.subscription.client.plans_assigned_externally:
            return True
        else:
            if timezone.now() >= self.plan_change.completion_expected_timestamp:
                return SubscriptionPayment.objects.filter(subscription=self.subscription, date__gt=self.plan_change.execution_start_time, amount=self.target_plan.price).exists()
            stripe_client = get_stripe_client(self._billing_secret_key)
            if self.plan_change.billing_reference is None:
                return False
            schedule = stripe_client.subscription_schedules.retrieve(self.plan_change.billing_reference)
            time_control = STRIPE_BILLING_TIME_CONTROL
            for phase in schedule['phases']:
                if phase['start_date'] == time_control.start_of_next_cycle_epoch_timestamp:
                    return phase['items'][0]['price'] == self.target_plan.billing_plan_id
            return False

    def make_appropriate_changes_in_provider(self):
        self.subscription.latest_sim.trigger_change_plan(self.target_plan.provider_plan_code, fake=False)

    def is_provider_change_complete(self):
        return self.subscription.latest_plan == self.target_plan


class CancelChangePlanChangeManager(PlanChangeManager):
    def validate_plan_change(self):
        super().validate_plan_change()
        if not self.target_plan_change:
            raise PlanChangeValidationError('Target plan change is not set')
        if not self.target_plan_change.can_be_cancelled:
            raise PlanChangeValidationError('Target plan change is not cancellable')
        if self.target_plan_change.subscription != self.subscription:
            raise PlanChangeValidationError('Target plan change has a different subscription')

    def lock_plan_change_for_actuation(self):
        super().lock_plan_change_for_actuation()
        with self.subscription_lock():
            self.target_plan_change.status = PlanChange.Status.LOCKED
            self.target_plan_change.save()

    def adjust_billing_for_plan_change(self):
        pass

    def complete_plan_change(self):
        self.target_plan_change.status = PlanChange.Status.CANCELLED
        self.target_plan_change.save()
        super().complete_plan_change()

    def is_billing_change_complete(self):
        raise PlanChangeValidationError('This should not be called')

    def make_appropriate_changes_in_provider(self):
        raise PlanChangeValidationError('This should not be called')

    def is_provider_change_complete(self):
        raise PlanChangeValidationError('This should not be called')


class CancelSubscriptionChangeManager(PlanChangeManager):
    def adjust_billing_for_plan_change(self):
        pass

    def is_billing_change_complete(self):
        return False

    def make_appropriate_changes_in_provider(self):
        pass

    def is_provider_change_complete(self):
        return False
