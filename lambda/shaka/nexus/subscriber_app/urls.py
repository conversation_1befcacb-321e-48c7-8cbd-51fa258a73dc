from django.urls import path
from subscriber_app import billing_views
from .views import LoginView, ChangePasswordView, RefreshTokenView, ForgotPasswordView, ConfirmForgotPasswordView, SignUpView, ClientDetailView, ClientPlansView, SubscriberDetailView, CancelSubscriptionView, PACRequestAPIView, ActivateSimView, VerifyView, PlanChangeView, CancelPlanChangeView, PlanChangeDetailView, ResendVerificationCodeView, ClientPerksView, ClaimPerkView, SendESimInstructionsView, BoltOnsView, HideNotificationDialogView, ESimSettingsView, RoamingBoltOnsView, AuthSsoView, ApplyReferralView, OpenIDLoginView, LoginViaOpenIDView, TestAuthView, ReferralAPIView, ShareEsimView

urlpatterns = [
    path('api/v1/<str:client_id>/auth/login/', LoginView.as_view(), name='login'),
    path('api/v1/<str:client_id>/auth/change-password/', ChangePasswordView.as_view(), name='change-password'),
    path('api/v1/<str:client_id>/auth/refresh-token/', RefreshTokenView.as_view(), name='refresh-token'),
    path('api/v1/<str:client_id>/auth/forgot-password/', ForgotPasswordView.as_view(), name='forgot-password'),
    path('api/v1/<str:client_id>/auth/confirm-forgot-password/', ConfirmForgotPasswordView.as_view(), name='confirm-forgot-password'),
    path('api/v1/<str:client_id>/auth/sign-up/', SignUpView.as_view(), name='sign-up'),
    path('api/v1/<str:client_id>/auth/verify/', VerifyView.as_view(), name='verify'),
    path('api/v1/<str:client_id>/auth/verify/resend/', ResendVerificationCodeView.as_view(), name='verify-resend'),
    path('api/v1/<str:client_id>/auth/instructions/', SendESimInstructionsView.as_view(), name='e-sim-instructions'),
    path('api/v1/<str:client_id>/auth/openid-login-details/', OpenIDLoginView.as_view(), name='openid-login-details'),
    path('api/v1/<str:client_id>/auth/openid-login/', LoginViaOpenIDView.as_view(), name='openid-login'),
    path('api/v1/<str:client_id>/data/client/', ClientDetailView.as_view(), name='client-detail'),
    path('api/v1/<str:client_id>/data/plans/', ClientPlansView.as_view(), name='plans'),
    path('api/v1/<str:client_id>/plans/sign-up/<str:plan_id>/', billing_views.PlanSignUpView.as_view(), name='plan-signup'),
    path('api/v1/<str:client_id>/checkout/session/status/', billing_views.SessionStatusView.as_view(), name='session-status'),
    path('api/v1/<str:client_id>/data/subscriber/', SubscriberDetailView.as_view(), name='subscriber-detail'),
    path('api/v1/<str:client_id>/subscription/cancel/', CancelSubscriptionView.as_view(), name='cancel-subscription'),
    path('api/v1/<str:client_id>/subscription/pac-code/', PACRequestAPIView.as_view(), name='pac-code'),
    path('api/v1/<str:client_id>/subscription/change-card/', billing_views.ChangeCardDetailsView.as_view(), name='change-card'),
    path('api/v1/<str:client_id>/subscription/activate-sim/', ActivateSimView.as_view(), name='activate-sim'),
    path('api/v1/<str:client_id>/subscription/plan-change/', PlanChangeView.as_view(), name='plan-change'),
    path('api/v1/<str:client_id>/subscription/plan-change/<str:plan_change_id>/', PlanChangeDetailView.as_view(), name='plan-change-detail'),
    path('api/v1/<str:client_id>/subscription/plan-change/<str:plan_change_id>/cancel/', CancelPlanChangeView.as_view(), name='cancel-plan-change'),
    path('api/v1/<str:client_id>/subscription/hide-notification-dialog/', HideNotificationDialogView.as_view(), name='hide-notification-dialog'),
    path('api/v1/<str:client_id>/data/perks/', ClientPerksView.as_view(), name='perks'),
    path('api/v1/<str:client_id>/data/claim-perk/', ClaimPerkView.as_view(), name='claim-perk'),
    path('api/v1/<str:client_id>/data/boltons/', BoltOnsView.as_view(), name='boltons'),
    path('api/v1/<str:client_id>/data/roaming-bolt-ons/', RoamingBoltOnsView.as_view(), name='roaming-bolt-ons'),
    path('api/v1/<str:client_id>/boltons/buy/', billing_views.BuyBoltOnView.as_view(), name='purchase-bolt-on'),
    path('api/v1/<str:client_id>/boltons/buy-eu/', billing_views.BuyBoltOnEUView.as_view(), name='purchase-eu-bolt-on'),
    path('api/v1/<str:client_id>/data/esim-settings/', ESimSettingsView.as_view(), name='esim-settings'),
    path('api/v1/<str:client_id>/data/test-auth/', TestAuthView.as_view(), name='test-auth'),
    path('api/v1/<str:client_id>/auth/login/sso/', AuthSsoView.as_view(), name='sso'),
    path('api/v1/<str:client_id>/subscription/referral/', ApplyReferralView.as_view(), name='referral'),
    path('api/v1/<str:client_id>/data/referral/', ReferralAPIView.as_view(), name='referral-data'),
    path('api/v1/<str:client_id>/share-esim/', ShareEsimView.as_view(), name='share-esim'),
]
