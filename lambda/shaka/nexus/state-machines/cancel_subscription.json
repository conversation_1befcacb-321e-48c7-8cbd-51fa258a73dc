{"Comment": "Cancel subscription - expects a plan change id", "StartAt": "Validate Config", "States": {"Validate Config": {"Type": "Task", "Next": "Slack Debug", "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "validate"}, "Resource": "${plan_change_fn_arn}", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Slack Debug": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"Payload": {"text.$": "States.JsonToString($)"}, "FunctionName": "${slack_debug_fn_arn}"}, "End": true}}}