{"Comment": "Plan change cancel - expects a plan change id", "StartAt": "Validate Config", "States": {"Validate Config": {"Type": "Task", "Next": "Cancel Downgrade Or Cancellation", "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "validate_and_lock"}, "Resource": "${plan_change_fn_arn}", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Cancel Downgrade Or Cancellation": {"Type": "Choice", "Choices": [{"StringEquals": "downgrade", "Variable": "$.target_plan_change.change_type", "Next": "Cancel Downgrade"}, {"StringEquals": "cancellation", "Variable": "$.target_plan_change.change_type", "Next": "Cancel Cancellation"}], "Default": "Slack Debug"}, "Cancel Downgrade": {"Type": "Task", "Next": "Complete", "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "bill"}, "Resource": "${plan_change_fn_arn}", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Cancel Cancellation": {"Type": "Task", "Next": "Complete", "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "bill"}, "Resource": "${plan_change_fn_arn}", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Complete": {"Type": "Task", "End": true, "Parameters": {"plan_change_id.$": "$.id", "sub_execution_id.$": "$$.Execution.Id", "action": "complete"}, "Resource": "${plan_change_fn_arn}", "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "Slack Debug"}]}, "Slack Debug": {"Type": "Task", "Resource": "arn:aws:states:::lambda:invoke", "Parameters": {"Payload": {"text.$": "States.JsonToString($)"}, "FunctionName": "${slack_debug_fn_arn}"}, "End": true}}}