{"name": "simp-webapp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3002", "build": "next build", "start": "next start -p $PORT", "lint": "next lint", "preview": "npm run build && npm run start -p 3002"}, "dependencies": {"@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@intercom/messenger-js-sdk": "^0.0.14", "@stripe/react-stripe-js": "^3.1.1", "axios": "^1.7.9", "jwt-decode": "^4.0.0", "next": "^15.1.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "reactjs-otp-input": "^2.0.10", "swr": "^2.3.0", "tailwind-merge": "^3.0.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}