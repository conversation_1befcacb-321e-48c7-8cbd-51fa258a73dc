import { getExpiresAt } from "@/helpers/auth";
import { LocalKey } from "@/hooks/useLocalStorage";
import axios from "axios";
import { jwtDecode, JwtPayload } from "jwt-decode";

interface CustomJwtPayload extends JwtPayload {
  "cognito:username": string;
}

let isTokenValid = true;
let isRefreshing = false;

const clearStorage = () => {
  localStorage.clear();
};

const setSubscriberSession = (
  idToken: string,
  accessToken: string,
  expiresAt: string
) => {
  localStorage.setItem(LocalKey.ACCESS_TOKEN, accessToken);
  localStorage.setItem(LocalKey.EXPIRES_AT, expiresAt);
  localStorage.setItem(LocalKey.ID_TOKEN, idToken);
};

const client_id = process.env.NEXT_PUBLIC_CLIENT_ID;

const api = axios.create({
  baseURL: `${
    process.env.NEXT_PUBLIC_BASE_URL ?? "http://localhost:8000"
  }/s/api/v1/${client_id}`,
});

const requestRefreshToken = (
  refreshToken: string | null,
  cognito_username: string | null
) =>
  api
    .post("/auth/refresh-token/", {
      refresh_token: refreshToken,
      cognito_username: cognito_username,
    })
    .then((res) => {
      isTokenValid = true;
      return res;
    })
    .catch(() => {
      clearStorage();
      console.error("Error refreshing token");
      window.location.replace(window.location.origin + "/login");
    });

const updateSubscriberSession = (response?: {
  data: {
    IdToken: string;
    AccessToken: string;
  };
}) => {
  const { IdToken = "", AccessToken = "" } = response?.data || {};
  const expiresAt = getExpiresAt(AccessToken);
  setSubscriberSession(IdToken, AccessToken, expiresAt);
};

api.interceptors.request.use(
  async function (config) {
    let accessToken = localStorage.getItem(LocalKey.ACCESS_TOKEN);
    const expiresAt = localStorage.getItem(LocalKey.EXPIRES_AT);
    const refreshToken = localStorage.getItem(LocalKey.REFRESH_TOKEN);
    const id_token = localStorage.getItem(LocalKey.ID_TOKEN);

    if (
      isTokenValid &&
      (!expiresAt || new Date().getTime() >= Number(expiresAt)) &&
      refreshToken
    ) {
      isTokenValid = false;

      const response = await requestRefreshToken(refreshToken, id_token);
      const { AccessToken } = response?.data || {};

      if (response) {
        updateSubscriberSession(response);
      }

      accessToken = AccessToken;
    }

    if (accessToken) config.headers.Authorization = `Bearer ${accessToken}`;

    return config;
  },
  function (error) {
    // Do something with request error
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    try {
      const originalRequest = error.config;

      if (
        error.response &&
        (error.response.status === 401 || error.response.status === 403) &&
        !originalRequest._retry
      ) {
        const refreshToken = localStorage.getItem(LocalKey.REFRESH_TOKEN);
        const id_token = localStorage.getItem(LocalKey.ID_TOKEN);
        const userData = jwtDecode<CustomJwtPayload>(id_token || "");
        const cognito_username = userData?.["cognito:username"];

        if (refreshToken) {
          if (!isRefreshing) {
            isRefreshing = true;

            try {
              const response = await requestRefreshToken(
                refreshToken,
                cognito_username
              );
              isRefreshing = false;
              if (response) {
                updateSubscriberSession(response);
              }
            } catch (refreshError) {
              isRefreshing = false;
              clearStorage();
              return Promise.reject(refreshError);
            }
          }

          return new Promise((resolve) => {
            const newToken = localStorage.getItem(LocalKey.ACCESS_TOKEN);
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            resolve(api(originalRequest));
          });
        } else {
          clearStorage();
          console.error("No refresh token found");
          window.location.replace(window.location.origin + "/login");
        }
      }
    } catch (e) {
      console.error(e);
    }

    return Promise.reject(error);
  }
);

export default api;
