import { Navigation } from "@/app/navigation";
import { Button } from "@/components/Button";
import Image from "next/image";
import Link from "next/link";
import { ReactNode } from "react";

export default function ResultBlock({
  imageSrc,
  alt,
  description,
  title,
  onRetry,
}: {
  title: string;
  imageSrc: string;
  alt: string;
  description: string | ReactNode;
  onRetry?: () => void;
}) {
  return (
    <>
      <div className="flex-grow relative max-md:text-center max-md:px-10 max-md:mb-16">
        <div className="absolute top-16 right-0 w-full h-[90%] bg-white/10 rounded-xl md:hidden" />
        <div className="flex-col-reverse md:flex-row flex justify-between items-center mb-8">
          <h3 className="text-3xl font-semibold">{title}</h3>
          <Image
            src={imageSrc}
            width={100}
            height={100}
            alt={alt}
            className="max-md:w-1/2 max-md:-mt-4"
          />
        </div>
        {description}
      </div>
      <div>
        <div className="flex flex-col gap-4 md:flex-row justify-center md:justify-end mt-6 md:gap-10">
          <div className="w-full md:w-1/2">
            {onRetry && (
              <Button type="button" variant="filled-gray" onClick={onRetry}>
                Try another code
              </Button>
            )}
          </div>

          <div className="w-full md:w-1/2">
            <Link href={Navigation.PAYMENT}>
              <Button type="submit" variant="filled">
                Continue
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
