"use client";

import SignUpContent from "@/components/SignUpContent";
import VerificationForm from "./_componets/VerificationForm";
import withAuth from "@/hoc/withAuth";

function Verification() {
  return (
    <SignUpContent biggerRight stepIndex={3}>
      <SignUpContent.Left
        title="Email check"
        description="We just sent you an email. Check your spam folder too."
        hideGoBack
      />
      <SignUpContent.Right>
        <div className="md:mt-[120px]">
          <VerificationForm />
        </div>
      </SignUpContent.Right>
    </SignUpContent>
  );
}

export default withAuth(Verification);
