"use client";

import { verify } from "@/api/auth";
import { Navigation } from "@/app/navigation";
import { Button } from "@/components/Button";
import ErrorText from "@/components/ErrorText";
import { useSubscriber } from "@/hooks/useSubscriber";
import { schema, SignupCodeInputs } from "@/schemas/signup-code";
import { Subscriber } from "@/types/subscriber";
import { yupResolver } from "@hookform/resolvers/yup";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { OtpInput } from "reactjs-otp-input";
import { twMerge } from "tailwind-merge";
import StartAgainDialog from "./StartAgainDialog";

export default function VerificationForm() {
  const router = useRouter();
  const { subscriber, updateSubscriber } = useSubscriber();

  const [error, setError] = useState("");

  const codeSent = useRef(false);

  const {
    handleSubmit,
    control,
    watch,
    formState: { isValid },
  } = useForm<SignupCodeInputs>({
    resolver: yupResolver(schema),
  });

  const watchCode = watch("code");

  const onSubmit = useCallback(
    (data: SignupCodeInputs) => {
      verify({ code: data.code })
        .then(() => {
          updateSubscriber({ ...subscriber, is_verified: true } as Subscriber);
          router.push(Navigation.REFERRAL);
        })
        .catch((error) => {
          codeSent.current = false;
          setError(error.response.data.error || error.message);
        });
    },
    [router, subscriber, updateSubscriber]
  );

  useEffect(() => {
    if (subscriber?.is_verified) {
      router.push(Navigation.REFERRAL);
    }
  }, [router, subscriber]);

  useEffect(() => {
    if (watchCode?.length === 4 && !codeSent.current) {
      codeSent.current = true;
      onSubmit({ code: watchCode });
    }
  }, [onSubmit, watchCode]);

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="md:p-8 md:bg-[#d9d9d91a] rounded-2xl w-full h-[376px] flex flex-col md:justify-center relative"
    >
      <div className="h-full flex flex-col justify-between relative">
        <Controller
          control={control}
          name="code"
          render={({ field }) => (
            <div className="relative mx-auto md:m-auto">
              <OtpInput
                {...field}
                shouldAutoFocus
                placeholder="____"
                numInputs={4}
                className="otp"
                isInputNum
                containerStyle={{
                  display: "flex",
                  justifyContent: "center",
                  gap: 16,
                  margin: "0 auto",
                }}
              />
              <ErrorText absolute size="sm">
                {error}
              </ErrorText>
            </div>
          )}
        />
      </div>

      <div
        className={twMerge(
          "flex justify-center md:justify-between",
          "max-md:fixed bottom-4 max-md:w-[90%] gap-4 flex-col md:flex-row items-center"
        )}
      >
        <div className="w-full md:max-w-[300px] md:w-1/2">
          <StartAgainDialog />
        </div>
        <div className="w-full md:max-w-[300px] md:w-1/2">
          <Button type="submit" variant="filled" disabled={!isValid}>
            Continue
          </Button>
        </div>
      </div>
    </form>
  );
}
