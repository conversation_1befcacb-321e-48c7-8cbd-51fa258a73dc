"use client";

import { Navigation } from "@/app/navigation";
import AppStoreButtons from "@/components/AppStoreButtons";
import { Button } from "@/components/Button";
import SignUpContent from "@/components/SignUpContent";
import withAuth from "@/hoc/withAuth";
import Image from "next/image";
import Link from "next/link";

const messages = {
  mobile: {
    title: "That's it",
    description:
      "The magic happens in our app. \n" + "Download it now and start simping.",
  },
  desktop: {
    title: "Almost there",
    description: "The magic happens in the app.",
  },
};

function AppSettingsPage() {
  return (
    <SignUpContent stepIndex={6} biggerRight withBackButton={false}>
      <SignUpContent.Left
        title={
          <>
            <p className="md:hidden">{messages.mobile.title}</p>
            <p className="hidden md:block">{messages.desktop.title}</p>
          </>
        }
        description={
          // hack to have one layout for all steps
          <>
            <p className="md:hidden">
              {messages.mobile.description}
              <br /> &nbsp;
            </p>

            <p className="hidden md:block">
              {messages.desktop.description}
              <br /> &nbsp;
            </p>
          </>
        }
        hideGoBack
        actionButton={
          <Link href={Navigation.DASHBOARD} className="w-full md:w-auto">
            <Button variant="filled-gray">Back home</Button>
          </Link>
        }
        keepButtonOnMobile
      />
      <SignUpContent.Right>
        <div className="md:mt-[120px]">
          <div className="md:p-12 md:bg-[#d9d9d91a] rounded-2xl w-full flex flex-col md:justify-center">
            <div className="hidden md:block flex-grow relative max-md:text-center max-md:px-10 max-md:mb-16">
              <div className="flex-col-reverse md:flex-row md:gap-4 flex justify-between items-center mb-4">
                <h3 className="text-3xl font-semibold">
                  Download the Simp app
                </h3>
                <Image
                  src="/images/small-logo.png"
                  width={60}
                  height={60}
                  alt="small-logo"
                  className="max-md:w-1/2 max-md:-mt-4"
                />
              </div>
              Download the app now to set up your device and earn your first
              reward.
            </div>

            <div className="flex flex-col md:flex-row justify-center md:justify-start items-center gap-12 mt-8">
              <Image
                src="/images/qr.svg"
                width={200}
                height={200}
                alt="app-qr-code"
                quality={100}
                className="hidden md:block max-md:w-1/2 max-md:-mt-4 bg-[#868686] rounded-xl"
              />
              <Image
                src="/images/small-logo.png"
                width={60}
                height={60}
                alt="small-logo"
                className="w-1/4 md:hidden"
              />
              <AppStoreButtons />
            </div>
          </div>
        </div>
      </SignUpContent.Right>
    </SignUpContent>
  );
}

export default withAuth(AppSettingsPage);
