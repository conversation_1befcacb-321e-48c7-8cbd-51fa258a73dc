"use client";

import { checkSessionStatus } from "@/api/stripe";
import { Navigation } from "@/app/navigation";
import { Button } from "@/components/Button";
import Loading from "@/components/Loading";
import { LocalKey } from "@/hooks/useLocalStorage";
import { useSubscriber } from "@/hooks/useSubscriber";
import { PaymentStatus, SessionStatus } from "@/types/stripe";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect } from "react";
import useSWR from "swr";

const headerStyle = "text-xl text-center mb-4 font-semibold md:mt-20";

const removeStripeInfo = () => {
  localStorage.removeItem(LocalKey.PLAN_ID);
  localStorage.removeItem(LocalKey.CLIENT_SECRET);
  localStorage.removeItem(LocalKey.SESSION_EXPIRES_AT);
  localStorage.removeItem(LocalKey.SESSION_ID);
};

function CheckoutReturn() {
  const searchParams = useSearchParams();
  const session_id = searchParams.get("session_id") || "";
  const { data } = useSWR("checkout-status", () =>
    checkSessionStatus(session_id)
  );
  const router = useRouter();

  const { payment_status, status } = data || {};
  const { subscriber, updateSubscriber } = useSubscriber();
  const isSessionCompleted = status === SessionStatus.Complete;
  const isPaymentPaid = payment_status === PaymentStatus.Paid;

  const onBack = () => history.back();

  useEffect(() => {
    if (isSessionCompleted && isPaymentPaid && subscriber?.plans) {
      router.push(Navigation.APP_SETTINGS);
    }
  }, [isPaymentPaid, isSessionCompleted, router, subscriber]);

  useEffect(() => {
    if (isSessionCompleted && isPaymentPaid) {
      removeStripeInfo();
    }
  }, [isPaymentPaid, isSessionCompleted]);

  useEffect(() => {
    const interval = setInterval(() => {
      if (!subscriber?.plans) {
        updateSubscriber();
      }
    }, 2000);
    return () => clearInterval(interval);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subscriber]);

  if (!data) {
    return <Loading />;
  }

  if (!isSessionCompleted) {
    return (
      <div className="grow flex flex-col justify-center max-w-xl m-auto">
        <h2 className={headerStyle}>Payment failed</h2>
        <p className="text-center mb-20">
          We are unable to process your payment. Please try again.
        </p>
        <Button onClick={onBack} variant="filled-gray">
          Return to payment
        </Button>
      </div>
    );
  }

  if (!isPaymentPaid) {
    return (
      <div className="grow flex flex-col justify-center">
        <h3 className={headerStyle}>We are waiting for your payment</h3>
        <p className="mb-20 text-center">
          While we wait you can fill in the data to get your SIM.
        </p>
        <div className="space-y-2">
          to
          <Link href={Navigation.DASHBOARD}>
            <Button variant="filled">Continue</Button>
          </Link>
          <Button onClick={onBack} variant="filled-gray">
            Return to payment
          </Button>
        </div>
      </div>
    );
  }

  if (!subscriber?.plans) {
    return (
      <div className="grow flex flex-col justify-center items-center">
        <h3 className={headerStyle}>Transaction in progress</h3>
        <p className="text-center mb-20">
          Your payment is being processed.
          <br />
          Please wait a moment
        </p>
        <div>
          <Loading />
        </div>
      </div>
    );
  }

  return null;
}

export default function CheckoutReturnPageWrapped() {
  return (
    <Suspense fallback={<Loading />}>
      <CheckoutReturn />
    </Suspense>
  );
}