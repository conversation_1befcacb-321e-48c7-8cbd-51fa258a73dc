import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Script from "next/script";
import { headers } from "next/headers";
import ScrollRestoration from "./_components/ScrollRestoration";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export async function generateMetadata() {
  const headersList = headers();
  const list = await headersList;
  const host = list.get("host");
  const protocol = list.get("x-forwarded-proto") || "https";
  const siteUrl = `${protocol}://${host}`;

  const metadata: Metadata = {
    title: "Simp - the mobile network that loves you a little too much",
    description:
      "eSIM with unlimited UK data, free global roaming and daily rewards",
    robots: "noindex, nofollow",
    openGraph: {
      title: "Simp - the mobile network that loves you a little too much",
      description:
        "eSIM with unlimited UK data, free global roaming and daily rewards",
      url: siteUrl,
      siteName: "Simp Mobile",
      images: [
        {
          url: `${siteUrl}/images/og-image.jpg`,
          width: 800,
          height: 600,
        },
      ],
      type: "website",
    },
  };

  return metadata;
}

const GA_TRACKING_ID = process.env.NEXT_GOOGLE_TRACKING_ID || "";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <Script
          strategy="afterInteractive"
          src={`https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`}
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_TRACKING_ID}', {
              page_path: window.location.pathname,
            });
          `}
        </Script>
        <div className="w-[90%] md:w-4/5 max-w-screen-xl m-auto min-h-screen relative flex flex-col">
          <ScrollRestoration />
          <Header />
          <main className="grow flex flex-col">{children}</main>
        </div>
      </body>
    </html>
  );
}
