@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Oakes Grotesk';
  src: url('/fonts/Oakes Grotesk Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Oakes Grotesk';
  src: url('/fonts/Oakes Grotesk Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Oakes Grotesk';
  src: url('/fonts/Oakes Grotesk Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Oakes Grotesk';
  src: url('/fonts/Oakes Grotesk Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Oakes Grotesk';
  src: url('/fonts/Oakes Grotesk SemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
}

:root {
  color-scheme: dark;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* --background: #0a0a0a; */
    /* --foreground: #ededed; */
  }
}

body {
  color: #ededed;
  background: linear-gradient(247.58deg, #333333 0.42%, #000000 100.86%), #000000;
  font-family: 'Oakes Grotesk', Arial, Helvetica, sans-serif;
}

@media screen and (max-width: 768px) {
  body {
    background: #000000;
  }
}

.otp input {
  border-radius: 10px;
  width: 72px !important;
  height: 54px;
  background: #ffffff1a;
}

.otp input:focus {
  outline: solid 2px #FDFE00;
}

@media screen and (max-width: 1024px) {
  .otp input {
    width: 15vw !important;
    height: 15vw;
    max-width: 72px;
    max-height: 72px;
  }
}

.masked-logo {
  position: relative;
  width: 155px;
  height: 75px;
}

.logo-base {
  position: absolute;
  top: 0;
  left: 0;
}

.logo-progress {
  position: absolute;
  top: 0;
  left: 0;
  mask-image: linear-gradient(to right, black 0%, black var(--progress, 0%), transparent var(--progress, 0%)) ;

}

.input {
  -webkit-appearance: none;
  background: var(--input-color);
  border-radius: 10px;
  height: 48px;
  width: 100%;
  padding: 0 20px;
  font-weight: 600;
  font-size: 14px;
  border: 1px solid #00000000;
}

.input.select {
  padding-left: 0.875rem;
}


.input.icon-right {
  padding-right: 40px;
}

.input.icon-left {
  padding-left: 40px;
}

.input::placeholder {
  font-weight: normal;
}

.input:focus,
.input:focus {
  outline-color: var(--primary-color);
}

/* terms */
.plain-text .list-title {
  margin-bottom: 8px;
  margin-top: 16px;
  font-weight: 600;
}

.plain-text .list-decimal {
  list-style-type: decimal;
  margin-left: 16px;
}

.plain-text .big-list-margin li {
  margin-bottom: 16px;
}

.plain-text .list-alpha {
  list-style-type: lower-alpha;
  margin-left: 16px;
}

.plain-text .small-list-margin li {
  margin-bottom: 8px;
}

.plain-text .list-roman {
  list-style-type: lower-roman;
  margin-left: 16px;
}

.plain-text .list-disc {
  list-style-type: disc;
  margin-left: 16px;
}

.plain-text .list-circle {
  list-style-type: circle;
  margin-left: 16px;
}

.plain-text .paragraphs p {
  margin-bottom: 12px;
}

.plain-text .paragraphs .bold {
  font-weight: 600;
}