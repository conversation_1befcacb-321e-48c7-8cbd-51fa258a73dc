"use client";

import SignUpContent from "@/components/SignUpContent";
import SignupForm from "./_components/signup-form";
import AuthProvider, { useAuth } from "@/context/AuthContext";
import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSubscriber } from "@/hooks/useSubscriber";
import { Navigation } from "../navigation";

function SignUp() {
  const { loggedIn } = useAuth();
  const router = useRouter();
  const { subscriber } = useSubscriber();

  useEffect(() => {
    if (loggedIn) {
      if (subscriber?.is_verified) {
        router.push(Navigation.DASHBOARD);
      } else {
        router.push(Navigation.SIGN_UP_VERIFICATION);
      }
    }
  }, [loggedIn, router, subscriber?.is_verified]);

  return (
    <SignUpContent biggerRight stepIndex={2}>
      <SignUpContent.Left
        title="Your details"
        description={
          // hack to have one layout for all steps
          <p>
            Tell us about yourself.
            <br /> &nbsp;
          </p>
        }
      />
      <SignUpContent.Right>
        <SignupForm />
      </SignUpContent.Right>
    </SignUpContent>
  );
}

export default function SignUpWrapper() {
  return (
    <AuthProvider>
      <SignUp />
    </AuthProvider>
  );
}
