"use client";
import { startTransition, useActionState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Input from "@/components/Input";
import { login as loginAction } from "@/actions/login";
import AuthProvider, { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/Button";
import Link from "next/link";
import SocialButtons from "@/components/SocialButtons";
import { Controller, useForm } from "react-hook-form";
import { LoginFormInputs, LoginFormSchema } from "@/schemas/login";
import { yupResolver } from "@hookform/resolvers/yup";
import ErrorText from "@/components/ErrorText";
import { useSubscriber } from "@/hooks/useSubscriber";
import useFlowRedirect from "@/hooks/useFlowRedirect";
import { Navigation } from "@/app/navigation";

const LoginPage = () => {
  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<LoginFormInputs>({
    resolver: yupResolver(LoginFormSchema),
  });

  const [state, action, pending] = useActionState(loginAction, undefined);
  const router = useRouter();
  const { login, loggedIn } = useAuth();
  const { subscriber } = useSubscriber();
  useFlowRedirect({ subscriber, fetchSubscriberOnRender: false });

  const buttonDisabled = pending || (loggedIn && !subscriber);

  const onSubmit = (data: LoginFormInputs) => {
    startTransition(() => {
      action(data);
    });
  };

  useEffect(() => {
    if (state?.data) {
      login(state.data);
    }
  }, [login, router, state?.data]);

  return (
    <div>
      <div className="max-md:hidden absolute top-0 left-0 w-full flex justify-end pt-10 pb-4 items-center md:pt-20 md:pb-8 pointer-events-none">
        <Link href={Navigation.HOME} className="pointer-events-auto">
          <Button variant="outlined">Go home</Button>
        </Link>
      </div>
      <div className="mt-10 text-xs mb-4">
        <SocialButtons isLogin />
      </div>

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col gap-4 max-w-md m-auto"
      >
        <Controller
          control={control}
          name="email"
          render={({ field }) => (
            <Input
              {...field}
              type="email"
              onChange={field.onChange}
              placeholder="Email"
              error={errors.email?.message}
            />
          )}
        />

        <Controller
          control={control}
          name="password"
          render={({ field }) => (
            <Input
              {...field}
              type="password"
              placeholder="Password"
              onChange={field.onChange}
              error={errors.password?.message}
            />
          )}
        />
        <ErrorText>{state?.formError?.join(", ")}</ErrorText>
        <Button
          type="submit"
          loading={buttonDisabled}
          disabled={buttonDisabled}
          variant="filled"
        >
          Login
        </Button>
      </form>

      <div className="flex flex-col mt-10 gap-6 items-center">
        <Link
          href="/forgot-password"
          className="underline text-[#868686] hover:text-white transition-colors"
        >
          Forgot password?
        </Link>
        <Link href="/sign-up/plan" className="text-[#868686]">
          Don&apos;t have an account?{" "}
          <span className="underline hover:text-white transition-colors">
            Sign up
          </span>
        </Link>
      </div>
    </div>
  );
};

export default function LoginPageWrapper() {
  return (
    <AuthProvider>
      <LoginPage />
    </AuthProvider>
  );
}
