import * as yup from "yup";
import { emailScheme } from "./common";

export type LoginFormInputs = {
  email: string;
  password: string;
};

export const LoginFormSchema = yup.object({
  email: emailScheme,
  password: yup.string().required("No password provided."),
});

export type FormState<T> =
  | {
      errors?: {
        email?: string[];
        password?: string[];
      };
      formError?: string[];
      message?: string;
      data?: T;
    }
  | undefined;
