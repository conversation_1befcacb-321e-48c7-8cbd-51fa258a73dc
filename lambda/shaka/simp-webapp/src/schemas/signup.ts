import * as yup from "yup";
import {
  dobScheme,
  emailScheme,
  passwordScheme,
  subscriberName,
} from "./common";

export const SignupFormSchema = yup.object({
  name: subscriberName,
  email: emailScheme,
  password: passwordScheme,
  dob: dobScheme,
});

export type FormState<T> =
  | {
      errors?: {
        name?: string[];
        email?: string[];
        password?: string[];
        date?: string[];
      };
      formError?: string[];
      message?: string;
      data?: T;
    }
  | undefined;

export type SignupFormInputs = {
  name: string;
  email: string;
  password: string;
  dob: string;
};
