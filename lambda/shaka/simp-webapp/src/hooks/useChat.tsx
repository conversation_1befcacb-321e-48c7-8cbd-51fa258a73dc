import { useEffect } from "react";
import Intercom, { shutdown } from "@intercom/messenger-js-sdk";
import { useSubscriber } from "./useSubscriber";
import { useAuth } from "@/context/AuthContext";

const intercomKey = process.env.NEXT_PUBLIC_INTERCOM_KEY || "";

async function createHmacSHA256(secretKey: string, userIdentifier: string) {
  const enc = new TextEncoder();
  const keyData = enc.encode(secretKey);
  const data = enc.encode(userIdentifier);

  const key = await crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "HMAC", hash: { name: "SHA-256" } },
    false,
    ["sign"],
  );

  const signature = await crypto.subtle.sign("HMAC", key, data);

  return Array.from(new Uint8Array(signature))
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");
}

const useChat = () => {
  const { subscriber } = useSubscriber();
  const { loggedIn } = useAuth();

  console.log(loggedIn);

  useEffect(() => {
    if (!loggedIn) {
      shutdown();
      return;
    }

    if (!subscriber?.email || !intercomKey) {
      shutdown();
      return;
    }

    const userIdentifier = subscriber.email;

    createHmacSHA256(intercomKey, userIdentifier)
      .then((hash) => {
        const intercomConfig = {
          api_base: "https://api-iam.intercom.io",
          app_id: "na34h0vg",
          name: subscriber.name || "Unknown User",
          email: subscriber.email,
          user_id: subscriber.email,
          user_hash: hash,
          created_at: Math.floor(Date.now() / 1000),
        };

        console.debug("Initializing Intercom with config:", intercomConfig);
        Intercom(intercomConfig);
      })
      .catch((error) => {
        console.error("Failed to initialize Intercom:", error);
      });

    return () => shutdown();
  }, [subscriber, loggedIn]);
};

export default useChat;
