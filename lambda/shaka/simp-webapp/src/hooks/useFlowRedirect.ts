import { fetchSubscriber } from "@/api/auth";
import { Subscriber } from "@/types/subscriber";
import { useEffect } from "react";
import useSWR from "swr";
import { redirect, usePathname } from "next/navigation";
import { Navigation } from "@/app/navigation";

export default function useFlowRedirect({
  subscriber,
  fetchSubscriberOnRender,
}: {
  subscriber?: Subscriber;
  fetchSubscriberOnRender: boolean;
}) {
  const pathname = usePathname();
  const { data } = useSWR(
    fetchSubscriberOnRender ? "subscriber" : null,
    fetchSubscriber,
    {
      revalidateOnFocus: false,
      shouldRetryOnError: false,
    }
  );

  useEffect(() => {
    const currentSubscriber = subscriber || (data as Subscriber);
    console.log("currentSubscriber", currentSubscriber);

    if (currentSubscriber) {
      if (
        currentSubscriber?.is_verified === false &&
        pathname !== Navigation.SIGN_UP_VERIFICATION
      ) {
        redirect(Navigation.SIGN_UP_VERIFICATION);
        return;
      }

      if (
        !currentSubscriber?.plans?.length &&
          pathname !== Navigation.REFERRAL &&
          pathname !== Navigation.PAYMENT
        
      ) {
        redirect(Navigation.REFERRAL);
        return;
      }

      if (
        currentSubscriber?.plans?.length &&
        pathname !== Navigation.DASHBOARD
      ) {
        redirect(Navigation.DASHBOARD);
      }
    }
  }, [subscriber, data, pathname]);
}
