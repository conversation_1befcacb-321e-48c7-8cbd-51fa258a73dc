"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { Navigation } from "@/app/navigation";
import Image from "next/image";
import useLogoAnimation from "@/hooks/useLogoAnimation";
import React from "react";

const iconLoadingBytPage = {
  [Navigation.SIGN_UP]: 2,
  [Navigation.SIGN_UP_PLAN]: 1,
  [Navigation.SIGN_UP_VERIFICATION]: 3,
  [Navigation.PAYMENT]: 5,
  [Navigation.REFERRAL]: 4,
};

export default function Header() {
  const path = usePathname();
  const currentStep =
    iconLoadingBytPage[path as keyof typeof iconLoadingBytPage];

  const { currentProgress, progressRef } = useLogoAnimation(currentStep);

  return (
    <header className="flex justify-center pt-10 pb-4 md:justify-between items-center text-white md:pt-20 md:pb-8 relative">
      <Link href="/">
        <div className="masked-logo cursor-pointer">
          <div className="logo-base">
            <Image
              src="/logos/SIMP_Logo_3D_White.svg"
              alt="Simp 3d logo"
              width={155}
              height={75}
            />
          </div>
          <div
            ref={progressRef}
            className="logo-progress"
            style={
              {
                "--progress": `${currentProgress}%`,
              } as React.CSSProperties
            }
          >
            <Image
              src="/logos/SIMP_Logo_3D_Yellow.svg"
              alt="Simp 3d logo"
              width={155}
              height={75}
            />
          </div>
        </div>
      </Link>
    </header>
  );
}
