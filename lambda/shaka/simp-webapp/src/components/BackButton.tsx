"use client";

import { useRouter } from "next/navigation";
import { Button } from "./Button";
import Image from "next/image";

export default function BackButton({
  href,
  arrow,
}: {
  href?: string;
  arrow?: boolean;
}) {
  const router = useRouter();

  const goBack = () => {
    if (href) {
      router.push(href);
    }

    if (window.history.length > 1) return router.back();
  };

  if (arrow) {
    return (
      <button
        className="bg-[#D9D9D9]/20 rounded-full size-8 flex justify-center items-center"
        onClick={goBack}
      >
        <Image
          alt="back-arrow"
          src="/icons/back-arrow.svg"
          width={18}
          height={18}
        />
      </button>
    );
  }

  return (
    <Button variant="filled-gray" onClick={goBack}>
      Go back
    </Button>
  );
}
