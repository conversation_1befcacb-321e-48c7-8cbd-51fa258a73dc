import Image from "next/image";
import { useMemo } from "react";
import { twMerge } from "tailwind-merge";

const baseStyles =
  "px-6 py-3 pt-4 rounded-full font-medium text-[14px] md:text-base transition-all w-full active:bg-[#B5B700]";
const outlinedStyles = "border border-[#868686] text-white md:hover:bg-white/5";
const filledStyles = "bg-[#FDFE00] text-black md:hover:bg-[#FDFE00]/70";
const grayStyles = "bg-[#4A4A4A] text-white md:hover:bg-[#686767]";
const disabledStyles = "opacity-50 cursor-not-allowed pointer-events-none";

type ButtonVariant = "outlined" | "filled" | "filled-gray";

const getStyles = (variant: ButtonVariant) => {
  switch (variant) {
    case "outlined":
      return `${baseStyles} ${outlinedStyles}`;
    case "filled":
      return `${baseStyles} ${filledStyles}`;
    case "filled-gray":
      return `${baseStyles} ${grayStyles}`;
    default:
      return baseStyles;
  }
};

export function Button({
  children,
  onClick,
  disabled,
  type,
  loading,
  variant,
}: Readonly<{
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  disabled?: boolean;
  type?: "button" | "submit" | "reset";
  loading?: boolean;
  variant: ButtonVariant;
}>) {
  const styles = useMemo(
    () => twMerge(getStyles(variant), disabled ? disabledStyles : ""),
    [disabled, variant],
  );

  return (
    <button
      onClick={onClick}
      className={styles}
      disabled={disabled}
      type={type}
    >
      {loading ? (
        <span className="flex items-center justify-center -mt-1 h-7">
          <Image
            src={
              variant === "filled"
                ? "/images/spinner-black.svg"
                : "/images/spinner.svg"
            }
            width={20}
            height={20}
            alt="loading"
            priority
          />
        </span>
      ) : (
        children
      )}
    </button>
  );
}
