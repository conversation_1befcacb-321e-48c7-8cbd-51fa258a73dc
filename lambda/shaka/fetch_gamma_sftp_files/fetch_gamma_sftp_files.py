import io
import base64
import logging
import os
import pathlib
import tempfile
from datetime import datetime
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend

import boto3
from paramiko import SFTPClient, Transport, RSAKey

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def get_pkey_as_string():
    iv_and_ciphertext = base64.urlsafe_b64decode(PKEY_DATA)
    iv = iv_and_ciphertext[:16]
    ciphertext = iv_and_ciphertext[16:]
    cipher = Cipher(algorithms.AES(os.environ['SFTP_PKEY_KEY'].encode('utf-8')), modes.CFB(iv), backend=default_backend())
    decryptor = cipher.decryptor()
    plaintext = decryptor.update(ciphertext) + decryptor.finalize()
    return plaintext.decode('utf-8')

def get_pkey_as_filelike():
    return io.StringIO(get_pkey_as_string())

def is_cdr_file(potential_filename):
    # Cdrs/FZG_Daily_Calls_ShakaTelecommsLtdTEST_16092024_45294612_1_NRO_V3.txt
    return potential_filename.startswith('FZG') and potential_filename.endswith('V3.txt') and 'Daily_Calls' in potential_filename

def key_to_key_prefix(key):
    return '/'.join(key.split('/')[:-1])

def key_to_filename(key):
    return key.split('/')[-1]

def cdr_filename_to_key(cdr_filename):
    without_extension = cdr_filename.split('.')[0]
    _, freq, data_type, acc, date_portion, _, _, extra, _ = without_extension.split('_')
    date = datetime.strptime(date_portion, '%d%m%Y')
    return f'{data_type.lower()}/{freq}/{acc}/{extra}/{date.year}/{date.month:02}/{date.day:02}/{cdr_filename}'

def lambda_handler(_, __):
    sftp_dir = pathlib.Path(os.environ['SFTP_DIR'])
    s3_bucket_name = os.environ['S3_BUCKET_NAME']

    s3 = boto3.resource('s3')
    bucket = s3.Bucket(s3_bucket_name)

    already_copied_objects = {}

    def has_already_uploaded(potential_cdr_file):
        key_prefix = key_to_key_prefix(cdr_filename_to_key(potential_cdr_file))
        if key_prefix not in already_copied_objects:
            already_copied_objects[key_prefix] = {key_to_filename(obj.key) for obj in bucket.objects.filter(Prefix=key_prefix)}
            logger.info('Fetched existing %s', already_copied_objects[key_prefix])
        return potential_cdr_file in already_copied_objects[key_prefix]

    with tempfile.TemporaryDirectory() as tempdir:
        def copy_cdr_file_to_s3(cdr_file):
            local_filename = pathlib.Path(tempdir) / cdr_file
            sftp.get(str(sftp_dir / cdr_file), local_filename)
            with open(local_filename, 'rb') as f:
                bucket.put_object(Key=cdr_filename_to_key(cdr_file), Body=f)
            os.remove(local_filename)

        with Transport((os.environ['SFTP_HOST'], int(os.environ['SFTP_PORT'])), disabled_algorithms={'pubkeys': ['rsa-sha2-512', 'rsa-sha2-256']}) as transport:
            transport.connect(username=os.environ['SFTP_USERNAME'], pkey=RSAKey.from_private_key(get_pkey_as_filelike()))
            with SFTPClient.from_transport(transport) as sftp:
                remote_files = sftp.listdir(str(sftp_dir))
                for potential_cdr_file in remote_files:
                    if is_cdr_file(potential_cdr_file):
                        if not has_already_uploaded(potential_cdr_file):
                            logger.info('Copying %s', potential_cdr_file)
                            copy_cdr_file_to_s3(potential_cdr_file)
                        else:
                            logger.info('Not copying %s', potential_cdr_file)


PKEY_DATA = '1wyiye4aOIr-YCY73nwv-2VaZkkvEG0wMV6aeA8z46e3iYXx9hPMygQguuO7rXwSamCR5OMVaQkJAHWUjg4k7Fc6dyEeSxxNOK0LYvR9xrKcJQqvnnZkDZ7YIQ8iDM-HUmgvYY1IBjrqzJesGnRXepD0m77s_uLl5S9zqJ3sEFRHPdUQrtN_Pir_rQGZo2ThkYXz9wPLgavJ3VE4vvnsOeE4ljYm04GezwXD2S8dGwVc9ljQ2J-8VPilOszHr3rSoUlv6hKXD4TU-D1K_NxChRpBoWzhhEDFjRyzRpVRe41kOsEn5P9bar8o87KLJ793iGs4-WA3SqVhrG1zveX7Bv0sgNVD52IVTs8RcSHR2A8B4lodzP1Z_pl18CqYxPfkRR6iQi9csp2Yh0zPx_ncezX3aV9hkQ2EWrUjeRpMGowWbWr-3Srar_E2Ep-3hvuv4AwhF-hQUiKt_E8OA54pd5BD40V-wSb5rBQe7QsqIFK-H0HYYD97BXLCke_FAxzyHkoYBB3G56KagfD7kltv7sfg0FJG3OcN6aVzt-FZVKNIqGRijoA2ar9YOVZ_zon-Brv-48Mc4QnLReONzE2lFAfDjT2uEUpGvWh9e1h7mOie8q6Eyn_0sNvTf6pvxlmPNTKu5jvsOfe9pSpRjcgRvt3lEQjVapK64r3pMc_yoXYCW6e0NXzX1TiYB77zmmp6WWrwz1C_ZFppf8B9bFm46dLJ-n0gKjX1ZyEJ773j7h73SO-wPHMpOW9PMsGaLpWCfnxsGerDef32JRcyikfp9p3CxOS3vWLT__ol6d4_TUBFNPKdqACSqGYKIsnFY3fJeZlDAdKZ_02xPNDrIfedah07b0-LMJntS_DYo7-7EDwKmSTI2SXBYKfyuYC-CcZv5jjo89yq1FZioEeb4TYz2ffjXom_kyy-WgKqHDYxZcpAXiVO7g7GaPsjjb7kTy9a7qyMPwKOE3PUvJHjvlmnFKxGohIjHDIzYh6mPIK4-yQ_zIe8tw2QJuJutZ_7bLxZr_bqQnqO1nzqWWW7r6_c1WVif_XJ9HddWBJCtAiYo0Uuv7GosVzE3KFHpyTkqnDm4cc2GVDBQB2c2ViEcNHyIWkIr3Ug1SkJqG26O8At70phPogZ0bAQ6G52esTxxPerCqNNIx7F4iU4mHli01i7ImWulHTsurSyEkYt-6oQN_ovR_VjZmPNCa5Z4ZSIbXGuIIfaTF__aKFZ0AyTCWX7G2NZm0bo2xTy5dZ8XVXn4wEztgtyamqRrv9eqnwwDm0Jro6TRvEaxnRp0LppeUCCUXOwAFfP03GR6JNDoQOLTfh14UFMeEj2tzUbrUExtCKAX-f9M9BIdtSl5ohrmcne4aiEFV8hpyDGl3XVVueAn0nX0n4qGD0Dnfh9NjJzMY-f4oy8IACY0vYIuFttGB8lRw6Fzo46KjQ45lxVgyTCmmJE4XR1dSRcnNrpd8py_7Pu9QhWCu828M4BXtM1pTuciQff2TDkjttM8YDjjZy_2v1igfLdIISrgHm1zfVzfwk0G0xGyj_bVcoOgw009SI37DHDVlef5GarcsiyB0geGLiOt5bvtSSCGoQvXu3p9Ja5-EJohQjdtILu5taai4dJYMxAeOqcklasLZNOav3ynkEf4ncBWNi0e4_OkRGIA970JvrQHFBa7lqLUPaxYOmXgABFqIFepOPHiCLJAhdcLYqvaMdo8k9s12HhISIaP2niTKhDVcTnxl1uHYWoqUajQuSEdJn02dD7RxbH1fnBxbyoPxZ7qetpn8i6OmIWVfzZdDMe3YJVNkV4Tr-6nEsvLtg0EznKTMuU0PxiVOuehRrV-hD6HIJn0cCk4wpQWc9S7AYDxx4eTbrTRrbvBr0M1RWRuYW31B77nlF8bLeotgpm_BOHTWdfBvM996aJPGziqtDJSCMBsdAOvJlo3GiuTdNDyGZtgU4NL8zshuBrNBQL9VVUcXYTTyjCGIay9TrtNgQLA6gaMTj4Q7GFY1ovUGXH-0_1Pm9WyojqH3XBVIcClvB091BfZQW093Y7_dqt8dhaC1HFGxusqUknKROIkh5LCpaGi_AOAbbZN4ynTyjX4J_lM0962rymoiZ1pP5xoZq-Rw0LCd2HnfKgakhHkyTZew_ilcQkObp-gpJvzy02pXfeGKD1QDTNITmvH0b0FsqRlKBxrG4yvDf0qOylgDx9HFIZ2_tYOWjbfnj3V7kB5Sa-0eJbtIMKPtD8u3tWOiGOJ5ktHDMptMpK0d7MVTN4jO1XTqhHoyGmqVv84eCG35JVy39xd2YUt0wMDA2UWjEf302ToB24FfpSOtVTQPWuR7AeRL4CVBrJnq-3aadMXL5D-1gntsOEY6cnCa9uAUfeRGLzhtwCMJyqo1XB8TjfWbncXXS1AkCMuL-c-3FoudoEto6Cn1LdShzKwG9ZZhS9uLF3TfkjGDlFOno-FgMiaYmalmw-zu8RuorwVl-E4CG8_w1oJH3zLJ5Gzx6hRJRrWb0Sn7qoC8a5BtR_ns6WFuRUY-K-BDqmIdnWPvEUX6-a9mqyZ_AD4hJazjvK2HlMSvAHLqhbQe74FuAY1oKBtoNgtwTfiE7G8TI0-KlZ2ic1-_E_V2WgnZiQAd4vuqO8fNSwIAJSfq6uM7QQV07LNY44J31pGOPOJ7uaVUVokK-2kznkHVClIgDc9iYV2immgDxKwXg-baGMkHFTLQYm2Yd06_GmFc4gM7aOzSXRIJ-5ozvxRO3Bo703c2oHoa6IBYzNNVdETpOqQR9aIywWsEaCTpFCyDVlWYmp_yCoY0sBLMcgtFe_217v1-3a4CKflYJPRG_IFWIaH5_nlck9qfw0fys5ZefRPbzZI7M5pI5gymDjZqrI_5ub1Roh1Fkg_USl8aMfsiyj0I_BjDGnsMEFePfRUju6Mp5QivGa44_bx5l9SGfzTCO_AKGruB8T8Y3dxH7691HrBoDTMH2OZe0WtDvRb61AYArs0Ip8dTXoKGSLnv6Sgza5b9x4NctfGObGXDk7YQE4KAZPn8tNrKeF85IJuogJwpqFnlvXreNYG3zzTKcxDggfv1HGCwihedKpXtRa1sU8MaU15nfGzOuJkxN6uEaFc7FQcPkX0xFszsaVnWwAmET0o7y1XRTSXPP69ZyF6tl5akPbltXhl3maorW5XO62VM-hhcxKTtXxQDLA1X0BhOCGZbitWEhYS6nNvggr510BjLfjrPoVgWMe_VnxBVMR8pe8NEMqwUcOpSVZR7d_GaeljWyo10FrbSgwh8UWajC_j3chj3VOydW7HdHzugXPMTJA5-51v7jnwXxmiPHSphpH0d15wgjoyxLID410zwcczdPd3tPKdm2t9Dy0ngvtGzZsQ8aZNyjC5IEyh-InKsjfsp2l3yNaF6bZkuG0gJQ_LOuiEPvhpesMQomgkVPkinCrtYIZ0GUnSH4LgHwVw3sGiV3ByR2GgLGyj_WAwsPaLl030Y2w76xd9MKnpUnlRf45iwe85pV3I0iudNp3tOsnVD6RckBI5YcYQx__EQm7bDkTYhjHwcG68HNVkWLnVYY_sKcxzqvuLmDVFSZjzu7dJz3eVQaxQhG3bdZf3423Q9W0wTL9mPHbN-wogjIxwAh_Le2c2JzC4lYf7ClQ6VwyF-UrA9f2coZzZxj9hHvkEd6Dr1Lsqu0WHD1zy1CEiosSsS52z3fDy5UcnFWWQsqIUCXMM44L5xMjGKaxaWfqkkAPfk2iZK5wgDdfFBiYPX9E7ZhSqj7Nlehgamwxi_TgpnXvzTAIpQ54bgwNuK28vIl5zOz5B5YYxEjmxVZD0CLJXpFvxbX34EGTotCN0zHn01mdHkKVV-F-tc77mUndpPh7C0nWFvWJKNcY1QbK-8oY_McQfeSQTyGJMxwqkWltOib3_CDikyPrYYObod_6Fq8icdBjByLdZtjYqKtzDvqTduZT2Pwx-bYe3Rk_uAoolQaTnRVHv9f5cZeg5C3Jjc9z045m37VJkkCRWjTb9RhvdaOJgRDeWNOge6jdZz5grCwUXDLo-Tb_zkFejKpdADmUNhQDK6MyZcsWJpgxf3Nlu-w4fytPtklqjp1_maRVPbtUW5q5fSiKN64PpSESitDUkNa2NZ9YbdQ_P4AB5cIz_zReZWtXvBoW5rZwMoohclUt8ad9WMpmP7jJE8GnxZ5Au25yu8BpIAu_qhiUZCJHwoZUb7qzOtwXvCJJjUjqG8MqKRDqnLDDPKMAf2aPUtpIhQVxhTyKX3lenqLwyzmeu-bgoK_qo7MFVeINtoaF1ZKkijc-ElLkEyA1TdUIPUt8xXw8ONqiv8KLv7WIaGEV12ix_AVPomv3SkQMTyGaByhTaHruJedHoZIK87OqX8JhrzN8-syGQGC3msYxQZdr47YLVyBQ_fUJCBjp9iBz3gzKMHU-hk43VkasGYh0S0tsZyn4ujZfGJfBcjSpiK2_C5vm8wTH4XZE7rlFckpSQdZAj3qHS_bvp79cMfujImKS9Ut1MY-TKYiiEVcuVN7I1TpzZeBM1g=='
