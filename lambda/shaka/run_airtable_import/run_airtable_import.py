import logging
import os
from datetime import datetime, timedelta
import requests

logger = logging.getLogger()
logger.setLevel(logging.INFO)


def lambda_handler(_, __):
    api_endpoint = os.environ['CDR_DB_ENDPOINT']
    api_token = os.environ['CDR_DB_API_TOKEN']

    end_datetime = datetime.now()
    start_datetime = end_datetime - timedelta(hours=1)

    # Format dates to ISO format
    start_iso = start_datetime.isoformat()
    end_iso = end_datetime.isoformat()

    # Prepare the request payload
    for data_type in ['voice', 'data', 'sms']:
        payload = {
            'start_datetime': start_iso,
            'end_datetime': end_iso,
            'data_type': data_type,
        }

        # Make a request to your API
        headers = {
            'Authorization': f'Bearer {api_token}',
            'Content-Type': 'application/x-www-form-urlencoded',
        }

        logger.info('Posting to %s with %s', api_endpoint, payload)
        response = requests.post(api_endpoint, data=payload, headers=headers, timeout=60)
        response.raise_for_status()
