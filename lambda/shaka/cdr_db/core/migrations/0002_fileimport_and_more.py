# Generated by Django 4.2.7 on 2023-12-03 15:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FileImport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('in_progress', 'In Progress'), ('done', 'Done'), ('errored', 'Errored')], db_index=True, max_length=100)),
                ('filename', models.CharField(db_index=True, max_length=100)),
                ('started', models.DateTimeField(auto_now_add=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.RenameField(
            model_name='voicecdr',
            old_name='location_zone_name',
            new_name='location_zone',
        ),
        migrations.AddField(
            model_name='voicecdr',
            name='filename',
            field=models.CharField(default=None, max_length=100),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='voicecdr',
            name='answer_time',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='voicecdr',
            name='anumber',
            field=models.CharField(db_index=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='voicecdr',
            name='country_code',
            field=models.CharField(db_index=True, max_length=3),
        ),
        migrations.AlterField(
            model_name='voicecdr',
            name='disconnect_time',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='voicecdr',
            name='imsi',
            field=models.CharField(db_index=True, max_length=10),
        ),
        migrations.AlterField(
            model_name='voicecdr',
            name='msisdn',
            field=models.CharField(db_index=True, max_length=10),
        ),
        migrations.AlterField(
            model_name='voicecdr',
            name='record_id',
            field=models.BigIntegerField(db_index=True, unique=True),
        ),
        migrations.AlterField(
            model_name='voicecdr',
            name='setup_time',
            field=models.DateTimeField(db_index=True),
        ),
        migrations.AlterField(
            model_name='voicecdr',
            name='subscriber_id',
            field=models.CharField(db_index=True, max_length=100),
        ),
        migrations.CreateModel(
            name='SMSCDR',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('record_id', models.BigIntegerField(db_index=True, unique=True)),
                ('msisdn', models.CharField(db_index=True, max_length=10)),
                ('imsi', models.CharField(db_index=True, max_length=10)),
                ('subscriber_id', models.CharField(db_index=True, max_length=100)),
                ('cos', models.CharField(max_length=100)),
                ('call_type', models.CharField(choices=[('MOS', 'MOS'), ('ROS', 'ROS')], max_length=20)),
                ('anumber', models.CharField(db_index=True, max_length=100)),
                ('bnumber', models.CharField(max_length=100)),
                ('prefix', models.CharField(max_length=10)),
                ('destination', models.CharField(max_length=100)),
                ('location_zone', models.CharField(max_length=100)),
                ('location_code', models.CharField(max_length=100)),
                ('country_code', models.CharField(db_index=True, max_length=3)),
                ('mcc', models.IntegerField(blank=True, null=True)),
                ('mnc', models.IntegerField(blank=True, null=True)),
                ('event_time', models.DateTimeField(db_index=True)),
                ('filename', models.CharField(max_length=100)),
                ('mvno', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.mvno')),
            ],
        ),
        migrations.CreateModel(
            name='DataCDR',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('record_id', models.BigIntegerField(db_index=True, unique=True)),
                ('msisdn', models.CharField(db_index=True, max_length=10)),
                ('imsi', models.CharField(db_index=True, max_length=10)),
                ('subscriber_id', models.CharField(db_index=True, max_length=100)),
                ('cos', models.CharField(max_length=100)),
                ('call_type', models.CharField(choices=[('MOG', 'MOG'), ('ROG', 'ROG')], max_length=20)),
                ('apn', models.CharField(max_length=100)),
                ('location_zone', models.CharField(max_length=100)),
                ('location_code', models.CharField(max_length=100)),
                ('country_code', models.CharField(db_index=True, max_length=3)),
                ('mcc', models.IntegerField(blank=True, null=True)),
                ('mnc', models.IntegerField(blank=True, null=True)),
                ('cell_id', models.CharField(blank=True, default='', max_length=100)),
                ('rat', models.CharField(blank=True, default='', max_length=100)),
                ('imei', models.CharField(blank=True, default='', max_length=100)),
                ('event_time', models.DateTimeField(db_index=True)),
                ('usu', models.BigIntegerField()),
                ('filename', models.CharField(max_length=100)),
                ('mvno', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.mvno')),
            ],
        ),
    ]
