# Generated by Django 4.2.7 on 2023-12-07 11:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0006_alter_datacdr_mcc_alter_datacdr_mnc_alter_smscdr_mcc_and_more'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='datacdr',
            index=models.Index(fields=['msisdn', 'event_time'], name='core_datacd_msisdn_684812_idx'),
        ),
        migrations.AddIndex(
            model_name='smscdr',
            index=models.Index(fields=['msisdn', 'event_time'], name='core_smscdr_msisdn_febc04_idx'),
        ),
        migrations.AddIndex(
            model_name='voicecdr',
            index=models.Index(fields=['msisdn', 'setup_time'], name='core_voicec_msisdn_2c58e3_idx'),
        ),
        migrations.AddIndex(
            model_name='voicecdr',
            index=models.Index(fields=['msisdn', 'answer_time'], name='core_voicec_msisdn_58ddad_idx'),
        ),
        migrations.AddIndex(
            model_name='voicecdr',
            index=models.Index(fields=['msisdn', 'disconnect_time'], name='core_voicec_msisdn_5195e7_idx'),
        ),
    ]
