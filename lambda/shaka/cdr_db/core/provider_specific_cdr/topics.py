from django.conf import settings

from core.topic import topic

from . import events, handlers


ProviderSpecificEvent = events.GammaVoiceCDREvent | events.GammaDataCDREvent | events.GammaSMSCDREvent


provider_specific_cdr_received: topic.Topic[ProviderSpecificEvent] = topic.Topic(
    events_handlers=(
        topic.EventHandlers(events.GammaVoiceCDREvent, (handlers.convert_and_publish_gamma_voice_cdr,)),
        topic.EventHandlers(events.GammaDataCDREvent, (handlers.convert_and_publish_gamma_data_cdr,)),
        topic.EventHandlers(events.GammaSMSCDREvent, (handlers.convert_and_publish_gamma_sms_cdr,))
    ),
    publisher=topic.SNSPublisher(arn=settings.PROVIDER_SPECIFIC_CDR_TOPIC_ARN, subject='')
)
