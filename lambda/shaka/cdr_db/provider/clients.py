import time
import json
import hmac
import hashlib
import logging
from urllib.parse import urlencode
import requests
from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.utils import timezone
from core.slack import send_debug_slack_message
from .models import Pollable

logger = logging.getLogger()
logger.setLevel(logging.INFO)


class TransatelClient:
    url = 'https://api.transatel.com'

    def _get_access_token(self):
        data = {'grant_type': 'client_credentials'}
        headers = {
            'Authorization': f'Basic {settings.TRANSATEL_ACCESS_TOKEN}',
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        response = requests.post(f'{TransatelClient.url}/authentication/api/token', data=data, headers=headers, timeout=20)
        response.raise_for_status()
        return response.json()['access_token']

    def _post(self, path, data):
        access_token = self._get_access_token()
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        response = requests.post(f'{TransatelClient.url}{path}', json=data, headers=headers, timeout=20)
        response.raise_for_status()
        return response.json()

    def bar_data(self, sim_serial):
        logger.info('Barring %s', sim_serial)
        return self._turn_on_option(sim_serial,  "TSL_BAR_DATA")

    def upgrade_to_100gb(self, sim_serial):
        logger.info('Upgrading %s to 100gb', sim_serial)
        return self._turn_on_option(sim_serial,  "TSL_UK_DATA_100GB")

    def upgrade_to_30gb(self, sim_serial):
        logger.info('Upgrading %s to 30gb', sim_serial)
        return self._turn_on_option(sim_serial,  "TSL_UK_DATA_30GB")

    def upgrade_to_10gb(self, sim_serial):
        logger.info('Upgrading %s to 10gb', sim_serial)
        return self._turn_on_option(sim_serial,  "TSL_UK_DATA_10GB")

    def activate_sim(self, sim_serial, plan_code, external_reference=None):
        path = f'/connectivity-management/subscribers/api/subscribers/sim-serial/{sim_serial}/activate'
        data = {
            "ratePlan": "MVNA Wholesale PAYM 7",
            'options': [
                {
                    'name': plan_code,
                    'value': 'on'
                },
                {
		   "name":"TSL_VMS",
		   "value":"on"
		},
		{
		   "name":"BT_CW",
		   "value":"on"
		},
		{
		   "name":"TSL_BAR_SCP_NATIONAL_NO_GEO_NUMBER",
		   "value":"on"
		},
		{
		   "name":"BT_BAR_AOC",
		   "value":"on"
		},
		{
		   "name":"TSL_BAR_SCP_ALL_PREMIUM_CALLS",
		   "value":"on"
		},
		{
		   "name":"BT_CH",
		   "value":"on"
		},
		{
		   "name":"BT_BAR_MMS",
		   "value":"on"
		},
		{
		   "name":"TSL_BAR_SCP_ALL_PREMIUM_SMS",
		   "value":"on"
		},
		{
		   "name":"TSL_INTERNATIONAL_BARRING",
		   "value":"on"
		},
		{
		   "name":"TSL_ROAMING_RLAH",
		   "value":"on"
		},
		{
		   "name":"BT_AC_ALLOW_ADULT_CONTENT",
		   "value":"on"
		},
		{
		   "name":"BT_5G",
		   "value":"on"
		},
		{
		   "name":"BT_CLI",
		   "value":"on"
		},
		{
		   "name":"TSL_UK_VOICE_SMS_UNLIMITED",
		   "value":"on"
		}
            ]
        }
        if external_reference:
            data['external_reference'] = external_reference
        return self._post(path, data)

    def request_port_in(self, sim_serial, pac_code, msisdn, desired_date=None):
        path = f'/connectivity-management/subscribers/api/portability/uk/sim-serial/{sim_serial}/request'
        data = {
            'authorizationCode': {'type': 'PAC', 'code': pac_code},
            'portabilityMsisdn': msisdn,
        }
        if desired_date:
            data['wishedPortabilityDate'] = desired_date
        print(path, data)
        logger.info(path)
        logger.info(data)
        return self._post(path, data)

    def _modify_sim_serial(self, sim_serial, data):
        path = f'/connectivity-management/subscribers/api/subscribers/sim-serial/{sim_serial}/modify'
        return self._post(path, data)

    def _turn_on_option(self, sim_serial, option_name):
        return self._modify_sim_serial(sim_serial, {
            "ratePlan": "MVNA Wholesale PAYM 7",
            "options": [
                {
                    "name": option_name,
                    "value": "on"
                }
            ]
        })

class GammaClient:
    package_ids = ['UK_DATA_4GB', 'UK_DATA_6GB', 'UK_DATA_10GB', 'UK_DATA_20GB', 'UK_DATA_40GB']

    def __init__(self):
        self._access_token = None

    @staticmethod
    def _package_threshold_mb(package_id):
        if package_id == 'UK_DATA_4GB':
            return 5000
        elif package_id == 'UK_DATA_6GB':
            return 10000
        elif package_id == 'UK_DATA_10GB':
            return 20000
        elif package_id == 'UK_DATA_20GB':
            return 40000
        elif package_id == 'UK_DATA_40GB':
            return 500000
        raise RuntimeError(f'Unknown package id {package_id}')

    @property
    def access_token(self):
        if not self._access_token:
            data = {
                'grant_type': 'password',
                'username': settings.GAMMA_USERNAME,
                'password': settings.GAMMA_PASSWORD,
            }
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            response = requests.post(f'{settings.GAMMA_URL}/auth/token', data=data, timeout=20, headers=headers)
            response.raise_for_status()
            self._access_token = response.json()['access_token']
        return self._access_token

    def _get(self, path, query_parameters=None, include_channel_partner_id=True):
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        if query_parameters is None:
            query_parameters = {}
        if include_channel_partner_id:
            query_parameters = {**query_parameters, 'channelPartnerId': settings.GAMMA_CHANNEL_PARTNER_ID}
        query_string = urlencode(query_parameters)
        response = requests.get(f'{settings.GAMMA_URL}{path}?{query_string}', headers=headers, timeout=20)
        response.raise_for_status()
        return response.json()

    def _post(self, path, data, include_channel_partner_id=True):
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        if include_channel_partner_id:
            data = {**data, 'channelPartnerId': settings.GAMMA_CHANNEL_PARTNER_ID}
        response = requests.post(f'{settings.GAMMA_URL}{path}', json=data, headers=headers, timeout=20)
        try:
            response.raise_for_status()
        except Exception as e:  # pylint: disable=broad-except
            logger.exception('Error posting %s: %s', path, response.text)
            raise e
        return response.json()

    def _patch(self, path, data, include_channel_partner_id=True, expect_response_body=False):
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        if include_channel_partner_id:
            data = {**data, 'channelPartnerId': settings.GAMMA_CHANNEL_PARTNER_ID}
        response = requests.patch(f'{settings.GAMMA_URL}{path}', json=data, headers=headers, timeout=20)
        try:
            response.raise_for_status()
        except Exception as e:  # pylint: disable=broad-except
            logger.exception('Error patching %s: %s', path, response.text)
            raise e
        if expect_response_body:
            return response.json()
        else:
            return response

    def _get_first_available_msisdn(self):
        return self._get('/mobile/v1/msisdns', query_parameters={'status': 'AVAILABLE'})[0]['id']

    def _get_service_by_msisdn(self, msisdn):
        return self._get('/mobile/v1/services/standard', query_parameters={'msisdn': msisdn, 'expand': 'configuration'})[0]

    def get_service_by_msisdn(self, msisdn):
        return self._get_service_by_msisdn(msisdn)

    def get_service_by_sim(self, sim_serial):
        return self._get_service_by_sim(sim_serial)

    def _get_service_by_sim(self, sim_serial):
        return self._get('/mobile/v1/services/standard', query_parameters={'iccid': sim_serial, 'expand': 'configuration'})[0]

    def get_service(self, service_id):
        return self._get(f'/mobile/v1/services/standard/{service_id}', query_parameters={'expand': 'configuration'})

    def get_sim_by_sim_serial(self, sim_serial):
        return self._get(f'/mobile/v1/sims/{sim_serial}')

    def _get_active_services(self):
        return self._get('/mobile/v1/services/standard', query_parameters={'status': 'ACTIVE', 'expand': 'configuration'})

    def _get_users(self):
        return self._get('/mobile/v1/users')

    def provision_service(self, sim_serial, package_id, callback_url):
        logger.info('Provisioning %s with %s', sim_serial, package_id)
        msisdn = self._get_first_available_msisdn()
        response = self._post('/mobile/v1/services/standard',
                   {
                       'msisdn': {'id': msisdn},
                       'sim': {'id': sim_serial},
                       'tariff': {'id': 'BUSINESS_EXTRA'},
                       'user': {'id': settings.GAMMA_USER_ID},
                       'configuration': {
                           'bars': ["ROW_DATA_ROAMING", "OUTGOING_VOICE_EU_USA_CAN", "OUTGOING_VOICE_ROW", "ROW_VOICE_ROAMING"],
                           'boltOns': [package_id, "GLOBAL_BUSINESS_DAILY"],
                           'networkServices': ["MOBILE_DATA", "DATA_ROAMING", "INTERNATIONAL_ROAMING", "ADULT_CONTENT"],
                           'roamingConfiguration': {
                               'euRoamingMaxCharge': 'EU_004_4',
                               'rowRoamingMaxCharge': 'ROW_300_0'
                           }
                       },
                       'usageControls': {
                       }
                   })
        self._trigger_polling(Pollable.PollingTypeChoices.SERVICE_PROVISIONED, params={'service_id': response['id'], 'callback_url': callback_url, 'sim_serial': sim_serial, 'msisdn': msisdn})
        return response

    def establish_capping(self, msisdn):
        logger.info('Establishing capping %s', msisdn)
        service = self._get_service_by_msisdn(msisdn)
        for bolt_on in service['configuration']['boltOns']:
            if bolt_on in self.package_ids:
                threshold = self._package_threshold_mb(bolt_on)
                self._patch(
                    f'/mobile/v1/services/standard/{service["id"]}',
                    {
                        'usageControls': {
                            'notifications': {
                                'alertOwningService': False,
                                'alertEmails': ['<EMAIL>', '<EMAIL>']
                            },
                            'settings': {
                                'boltOns': {
                                    'gdbDataB': {
                                        'enabled': True
                                    },
                                    'gdbDataC': {
                                        'enabled': True
                                    },
                                    'gdbDataD': {
                                        'enabled': True
                                    },
                                    'gdbVoiceB': {
                                        'enabled': True
                                    },
                                    'gdbVoiceC': {
                                        'enabled': True
                                    },
                                    'gdbVoiceD': {
                                        'enabled': True
                                    }
                                }
                            }
                        }
                    })
                start_of_next_month = (timezone.now().replace(day=15) + relativedelta(months=1)).replace(day=1)
                for _ in range(100):
                    service = self._get_service_by_msisdn(msisdn)
                    if 'LOCKED' in service.get('readOnlyAttributes', []):
                        time.sleep(5)
                    else:
                        break
                response = self._post(
                    '/mobile/v1/usageControls/settings',
                    {
                        'boltOns': {
                            'dasilyBoltOns': {
                                'gdbDataB': {
                                    'enabled': True
                                },
                                'gdbDataC': {
                                    'enabled': True
                                },
                                'gdbDataD': {
                                    'enabled': True
                                },
                                'gdbVoiceB': {
                                    'enabled': True
                                },
                                'gdbVoiceC': {
                                    'enabled': True
                                },
                                'gdbVoiceD': {
                                    'enabled': True
                                }
                            }
                        },
                        'data': {
                            'ukEu': {
                                'thresholdMB': threshold,
                                'doCap': True,
                                'alertPercentages': [75, 90, 100],
                            },
                            'row': {
                                'thresholdMB': 1000,
                                'doCap': True,
                                'alertPercentages': [75, 90, 100],
                            }
                        },
                        'schedule': [
                            {
                                'serviceId': service['id'],
                                'applyDate': start_of_next_month.strftime('%Y-%m-%d')
                            }
                        ]
                    }
                )
                return response
        raise RuntimeError(f'Unexpected service for capping {service["status"]}, {service}')

    def bar_data(self, sim_serial, callback_url):
        logger.info('Barring data %s', sim_serial)
        service = self._get_service_by_sim(sim_serial)
        response = self._patch(
            f'/mobile/v1/services/standard/{service["id"]}',
            {
                'configuration': {
                    'networkServices': []
                }
            })
        self._trigger_polling(Pollable.PollingTypeChoices.SERVICE_BARRED, params={'service_id': service['id'], 'callback_url': callback_url, 'sim_serial': sim_serial})
        return response

    def unbar_data(self, sim_serial, callback_url):
        logger.info('Unbarring data %s', sim_serial)
        service = self._get_service_by_sim(sim_serial)
        response = self._patch(
            f'/mobile/v1/services/standard/{service["id"]}',
            {
                'configuration': {
                    'networkServices': ["MOBILE_DATA", "DATA_ROAMING", "INTERNATIONAL_ROAMING", "ADULT_CONTENT"]
                }
            })
        self._trigger_polling(Pollable.PollingTypeChoices.SERVICE_UNBARRED, params={'service_id': service['id'], 'callback_url': callback_url, 'sim_serial': sim_serial})
        return response

    def bar_roaming(self, msisdn):
        logger.info('Barring roaming %s', msisdn)
        send_debug_slack_message(f'Barring roaming {msisdn}')
#        service = self._get_service_by_msisdn(msisdn)
        # response = self._patch(
        #     f'/mobile/v1/services/standard/{service["id"]}',
        #     {
        #         'configuration': {
        #             'networkServices': ["MOBILE_DATA"]
        #         }
        #     })
        # return response
        return msisdn

    def activate_service(self, msisdn, callback_url):
        logger.info('Activating service %s', msisdn)
        service = self._get_service_by_msisdn(msisdn)
        if service['status'] == 'SET_UP':
            response = self._patch(f'/mobile/v1/services/standard/{service["id"]}', {'status': 'ACTIVE'}, expect_response_body=False)
            self._trigger_polling(Pollable.PollingTypeChoices.SERVICE_ACTIVATED, params={'service_id': service['id'], 'callback_url': callback_url, 'msisdn': msisdn})
            return response
        else:
            raise RuntimeError(f'Unexpected service status {service["status"]}, {service}')

    def cancel_service(self, sim_serial, callback_url):
        logger.info('Cancelling service %s', sim_serial)
        service = self._get_service_by_sim(sim_serial)
        response = self._patch(f'/mobile/v1/services/standard/{service["id"]}', {'status': 'CEASED'})
        self._trigger_polling(Pollable.PollingTypeChoices.SERVICE_CANCELLED, params={'service_id': service['id'], 'callback_url': callback_url, 'sim_serial': sim_serial})
        return response

    def _ensure_plan_in_boltons(self, bolt_ons, package_id):
        cleaned_bolt_ons = [b for b in bolt_ons if b not in self.package_ids]
        cleaned_bolt_ons.append(package_id)
        return cleaned_bolt_ons

    def change_plan(self, sim_serial, package_id, callback_url):
        logger.info('Changing plan for %s to %s', sim_serial, package_id)
        service = self._get_service_by_sim(sim_serial)
        response = self._patch(f'/mobile/v1/services/standard/{service["id"]}',
                   {
                       'configuration': {
                           'boltOns': self._ensure_plan_in_boltons(service['configuration']['boltOns'], package_id)
                       }
                   })
        self._trigger_polling(Pollable.PollingTypeChoices.SERVICE_ON_PLAN, params={'service_id': service['id'], 'callback_url': callback_url, 'sim_serial': sim_serial, 'gamma_package_id': package_id})
        return response

    def get_port_status(self, msisdn, pac):
        response = self._get(f'/mobile/v1/porting/pac/{pac}/{msisdn}')
        return response['status']

    def request_port_in(self, sim_serial, pac_code, new_msisdn, port_date=None, callback_url=None):  # pylint: disable=too-many-arguments
        port_data = {
            'pac': pac_code,
            'msisdn': new_msisdn
        }
        if port_date:
            port_data['portDate'] = port_date
        data = {
            'porting': {'portIn': port_data}
        }
        service = self._get_service_by_sim(sim_serial)
        try:
            response = self._patch(
                f'/mobile/v1/services/standard/{service["id"]}',
                data,
                expect_response_body=False
            )
        except:  # pylint: disable=bare-except
            logger.exception('Error requesting port in %s', data)
            # Post to callback url
            if callback_url:
                response_data = {
                    'event_type': 'port',
                    'service_id': service['id'],
                    'callback_url': callback_url,
                    'sim_serial': sim_serial,
                    'status': 'ERRORED'
                }
                json_data = json.dumps(response_data, sort_keys=True)
                hmac_digest = hmac.new(settings.GAMMA_WEBHOOK_SECRET_KEY.encode('utf-8'), json_data.encode('utf-8'), hashlib.sha256).hexdigest()
                headers = {
                    'X-GAMMA-WHSIG-256': hmac_digest,
                    'Content-Type': 'application/json'
                }
                requests.post(callback_url, data=json_data, headers=headers, timeout=20)
            raise
        self._trigger_polling(Pollable.PollingTypeChoices.PORT, params={'pac': pac_code, 'callback_url': callback_url, 'msisdn': new_msisdn, 'sim_serial': sim_serial})
        return response

    def _trigger_polling(self, result_type, params):
        logger.info('Triggering polling for %s, %s', result_type, params)
        Pollable.trigger_polling(result_type, params)
