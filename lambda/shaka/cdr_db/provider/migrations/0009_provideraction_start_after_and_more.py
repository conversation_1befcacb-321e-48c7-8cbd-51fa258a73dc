# Generated by Django 4.2.7 on 2025-01-31 21:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('provider', '0008_alter_pollable_last_updated'),
    ]

    operations = [
        migrations.AddField(
            model_name='provideraction',
            name='start_after',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='pollable',
            name='polling_type',
            field=models.CharField(choices=[('service_provisioned', 'Service Provisioned'), ('service_activated', 'Service Activated'), ('service_on_plan', 'Service On Plan'), ('port', 'Port'), ('service_barred', 'Service Barred'), ('service_unbarred', 'Service Unbarred'), ('service_cancelled', 'Service Cancelled')], max_length=100),
        ),
        migrations.AlterField(
            model_name='provideraction',
            name='action',
            field=models.CharField(choices=[('fake-bar-data-transatel', 'Fake Bar Data (Transatel)'), ('fake-plan-upgrade-transatel', 'Fake Plan Upgrade (Transatel)'), ('real-bar-data-transatel', 'Real Bar Data (Transatel)'), ('real-plan-upgrade-transatel', 'Real Plan Upgrade (Transatel)'), ('real-activate-sim-transatel', 'Real Activate Sim (Transatel)'), ('real-request-port-in-transatel', 'Real Request Port In (Transatel)'), ('gamma-provision-service', 'Gamma Provision Service'), ('gamma-activate-service', 'Gamma Activate Service'), ('gamma-change-plan', 'Gamma Change Plan'), ('gamma-request-port-in', 'Gamma Request Port In'), ('gamma-establish-capping', 'Gamma Establish Capping'), ('gamma-real-bar-data', 'Gamma Real Bar Data'), ('gamma-real-unbar-data', 'Gamma Real Unbar Data'), ('gamma-real-bar-roaming', 'Gamma Real Bar Roaming'), ('gamma-look-for-pac-out', 'Gamma Look For Pac Out'), ('gamma-cancel-service', 'Gamma Cancel Service')], max_length=100),
        ),
    ]
