# New client questions

This covers a range of technical questions and requirements to establish for a new client.

## Domains

There are two main hosted components - the dashboard and the subscriber webapp. We can host them on a subdomain of shaka.tel without issue, and we can host them on a client's domain too. There are two separate processes depending on if they want it on a root (apex) domain e.g. clientmobile.co.uk vs a subdomain e.g. mobile.client.co.uk

#### Subdomain (e.g. mobile.client.co.uk)

This will require them to cname to e.g. client-subscriber-app.shaka.tel and to provide a https certificate for that domain. Alternatively they can host their own reverse proxy.

#### Apex domain (e.g. clientmobile.co.uk)

This is more complex because you can't have a CNAME on a root domain. The options are

1. Use a subdomain instead
2. They host and maintain a reverse proxy
3. We host and maintain a reverse proxy (there'll be a small cost to this)
4. They give us control of the domain nameservers and we use a route 53 alias internally (note that we won't be accepting any other domain maintenance tasks)
5. (untested) They host the domain with Amazon and use a route 53 alias. I don't know if AWS will permit an Alias to a non-account distribution.


Regardless of how the domain is hosted we will require an ssl certificate from them (unless it's a subdomain.shaka.tel domain in which case we can generate our own.


## Certificates

Whether using our domain or theirs (or apex or root) we'll need an SSL certificate for HTTPS. They can provide this if they want, or we can request one via Amazon which will require them to add some cname records to their domains to validate the certificate. If they're doing their own apex->www redirect or something that will also need a certificate that they'll have to procure themselves.

## Communications (mail/SMS)

We can currently send mail via AWS SES or SMTP but we would consider alternative options. To send mail from their domain they'll need to provide us with a set of credentials.

We currently send SMS via Infobip and can register a sender for the client, but are open to alternative options.


## Branding

We need brand guidelines to put together the subscriber webapp branding.
